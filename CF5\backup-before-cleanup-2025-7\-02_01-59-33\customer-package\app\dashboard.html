<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة مؤسسة وقود المستقبل</title>

    <!-- PWA Meta Tags -->
    <meta name="description" content="نظام إدارة شامل لمحطة الغاز يشمل إدارة الزبائن، المركبات، الشهادات، والمخزون">
    <meta name="keywords" content="غاز، محطة وقود، إدارة، شهادات، GPL، الجزائر">
    <meta name="author" content="مؤسسة وقود المستقبل">
    <meta name="theme-color" content="#2196F3">
    <meta name="msapplication-TileColor" content="#2196F3">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="وقود المستقبل">
    <meta name="mobile-web-app-capable" content="yes">

    <!-- Icons -->
    <link rel="icon" type="image/x-icon" href="assets/icons/app-icon.ico">
    <link rel="icon" type="image/png" sizes="16x16" href="assets/icons/app-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="assets/icons/app-icon.png">
    <link rel="icon" type="image/png" sizes="96x96" href="assets/icons/app-icon.png">
    <link rel="icon" type="image/png" sizes="192x192" href="assets/icons/app-icon.png">
    <link rel="apple-touch-icon" sizes="180x180" href="assets/icons/app-icon.png">
    <link rel="apple-touch-icon" sizes="120x120" href="assets/icons/app-icon.png">
    <link rel="apple-touch-icon" sizes="76x76" href="assets/icons/app-icon.png">

    <!-- Manifest -->
    <link rel="manifest" href="manifest.webmanifest">

    <!-- Stylesheets -->
    <link rel="stylesheet" href="styles/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>

    <!-- Database Sync System -->
    <script src="database-sync.js"></script>

    <!-- Session Security Check -->
    <script>
        // فحص الجلسة عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔐 فحص صحة الجلسة...');

            const currentSession = localStorage.getItem('currentSession');
            if (!currentSession) {
                console.log('❌ لا توجد جلسة نشطة - إعادة توجيه لتسجيل الدخول');
                alert('انتهت صلاحية الجلسة. سيتم إعادة توجيهك لتسجيل الدخول.');
                window.location.href = 'index.html';
                return;
            }

            try {
                const session = JSON.parse(currentSession);
                console.log('✅ جلسة صالحة موجودة:', session);

                // التحقق من انتهاء صلاحية الترخيص
                if (session.expiresAt && new Date(session.expiresAt) < new Date()) {
                    console.log('❌ انتهت صلاحية الترخيص');
                    alert('انتهت صلاحية الترخيص. سيتم إعادة توجيهك لتسجيل الدخول.');
                    localStorage.removeItem('currentSession');
                    window.location.href = 'index.html';
                    return;
                }

                // عرض معلومات الجلسة في وحدة التحكم
                console.log('📊 معلومات الجلسة الحالية:');
                console.log('- كود الترخيص:', session.licenseCode);
                console.log('- نوع الترخيص:', session.licenseType);
                console.log('- معرف الجهاز:', session.deviceId);
                console.log('- وقت تسجيل الدخول:', session.loginTime);
                console.log('- تاريخ الانتهاء:', session.expiresAt);

                // تحديث عنوان الصفحة
                document.title = `نظام إدارة مؤسسة وقود المستقبل - ${session.licenseType.toUpperCase()}`;

                console.log('🎉 تم تحميل النظام الرئيسي بنجاح!');

            } catch (error) {
                console.error('❌ خطأ في قراءة بيانات الجلسة:', error);
                alert('خطأ في بيانات الجلسة. سيتم إعادة توجيهك لتسجيل الدخول.');
                localStorage.removeItem('currentSession');
                window.location.href = 'index.html';
            }
        });
    </script>
</head>
<body>
    <div class="container">
        <header>
            <div class="header-left">
                <div class="logo-container">
                    <img src="assets/icons/company-logo.svg" alt="شعار مؤسسة وقود المستقبل" class="header-logo">
                </div>
                <div class="header-text">
                    <h1>نظام إدارة مؤسسة وقود المستقبل</h1>
                    <p class="header-subtitle">Future Fuel Corporation Management System</p>
                </div>
            </div>
            <div class="header-actions">
                <div class="date-time">
                    <span id="current-date"></span>
                    <span id="current-time"></span>
                </div>
                <div id="connection-status" class="connection-status">
                    <i class="fas fa-wifi-slash"></i> فحص الاتصال...
                </div>
                <button type="button" id="notifications-btn" class="btn" title="الإشعارات">
                    <i class="fas fa-bell"></i>
                    <span id="notifications-badge" class="badge hidden">0</span>
                </button>
                <button type="button" id="customer-dashboard-btn" class="btn" title="لوحة العميل" onclick="openCustomerDashboard()">
                    <i class="fas fa-user-circle"></i>
                </button>
                <button type="button" id="dark-mode-toggle" class="btn" title="تفعيل الوضع المظلم">
                    <i class="fas fa-moon"></i>
                </button>
            </div>
        </header>

        <nav>
            <ul>
                <li><a href="#" class="active" data-section="dashboard"><i class="fas fa-home"></i> الرئيسية</a></li>
                <li><a href="#" data-section="gas-cards"><i class="fas fa-id-card"></i> بطاقات الغاز</a></li>
                <li><a href="#" data-section="appointments"><i class="fas fa-calendar-alt"></i> المواعيد</a></li>
                <li><a href="#" data-section="customers"><i class="fas fa-users"></i> الزبائن</a></li>
                <li><a href="#" data-section="suppliers"><i class="fas fa-truck"></i> الموردين</a></li>
                <li><a href="#" data-section="inventory"><i class="fas fa-boxes"></i> المخزون</a></li>
                <li><a href="#" data-section="sales"><i class="fas fa-shopping-cart"></i> المبيعات</a></li>
                <li><a href="#" data-section="purchases"><i class="fas fa-shopping-bag"></i> المشتريات</a></li>
                <li><a href="#" data-section="debts"><i class="fas fa-money-bill-wave"></i> الديون</a></li>
                <li><a href="#" data-section="certificates"><i class="fas fa-certificate"></i> الشهادات</a></li>

                <li><a href="#" data-section="settings"><i class="fas fa-cog"></i> الإعدادات</a></li>
            </ul>
        </nav>

        <main>
            <!-- لوحة التحكم الرئيسية -->
            <section id="dashboard" class="active-section">
                <h2>لوحة التحكم</h2>
                <div class="dashboard-cards">
                    <div class="card">
                        <div class="card-icon"><i class="fas fa-id-card"></i></div>
                        <div class="card-info">
                            <h3>بطاقات الغاز</h3>
                            <p>عدد البطاقات: <span id="total-cards">0</span></p>
                            <p>بطاقات تحتاج للتجديد: <span id="cards-to-renew" class="highlight">0</span></p>
                        </div>
                    </div>
                    <div class="card">
                        <div class="card-icon"><i class="fas fa-calendar-alt"></i></div>
                        <div class="card-info">
                            <h3>المواعيد</h3>
                            <p>مواعيد اليوم: <span id="today-appointments">0</span></p>
                            <p>إجمالي المواعيد القادمة: <span id="upcoming-appointments">0</span></p>
                        </div>
                    </div>
                    <div class="card">
                        <div class="card-icon"><i class="fas fa-boxes"></i></div>
                        <div class="card-info">
                            <h3>المخزون</h3>
                            <p>إجمالي الأصناف: <span id="total-items">0</span></p>
                            <p>أصناف منخفضة المخزون: <span id="low-stock-items" class="highlight">0</span></p>
                        </div>
                    </div>
                    <div class="card gas-cards">
                        <div class="card-icon"><i class="fas fa-shopping-cart"></i></div>
                        <div class="card-info">
                            <h3>المبيعات</h3>
                            <p>مبيعات اليوم: <span id="today-sales">0 د.ج</span></p>
                            <p>مبيعات الشهر: <span id="month-sales">0 د.ج</span></p>
                        </div>
                    </div>
                    <div class="card vehicles">
                        <div class="card-icon"><i class="fas fa-shopping-bag"></i></div>
                        <div class="card-info">
                            <h3>المشتريات</h3>
                            <p>مشتريات اليوم: <span id="today-purchases">0 د.ج</span></p>
                            <p>مشتريات الشهر: <span id="month-purchases">0 د.ج</span></p>
                        </div>
                    </div>
                    <div class="card debts">
                        <div class="card-icon"><i class="fas fa-money-bill-wave"></i></div>
                        <div class="card-info">
                            <h3>الديون</h3>
                            <p>الديون النشطة: <span id="active-debts">0</span></p>
                            <p>ديون متأخرة: <span id="overdue-debts" class="highlight">0</span></p>
                        </div>
                    </div>
                    <div class="card customers">
                        <div class="card-icon"><i class="fas fa-users"></i></div>
                        <div class="card-info">
                            <h3>الزبائن</h3>
                            <p>إجمالي الزبائن: <span id="total-customers">0</span></p>
                            <p>زبائن جدد هذا الشهر: <span id="new-customers">0</span></p>
                        </div>
                    </div>
                    <div class="card appointments">
                        <div class="card-icon"><i class="fas fa-truck"></i></div>
                        <div class="card-info">
                            <h3>الموردين</h3>
                            <p>إجمالي الموردين: <span id="total-suppliers">0</span></p>
                            <p>موردين نشطين: <span id="active-suppliers">0</span></p>
                        </div>
                    </div>
                </div>

                <div class="dashboard-tabs">
                    <div class="tab-buttons">
                        <button type="button" class="tab-btn active" data-tab="tables">الجداول</button>
                        <button type="button" class="tab-btn" data-tab="stats">الإحصائيات</button>
                    </div>

                    <div class="tab-content active" id="tables-tab">
                        <div class="dashboard-tables">
                            <div class="table-container">
                                <h3>بطاقات تحتاج للتجديد قريباً</h3>
                                <table id="upcoming-renewals">
                                    <thead>
                                        <tr>
                                            <th>اسم الزبون</th>
                                            <th>رقم السيارة</th>
                                            <th>تاريخ انتهاء البطاقة</th>
                                            <th>الأيام المتبقية</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- سيتم ملؤها بواسطة JavaScript -->
                                    </tbody>
                                </table>
                            </div>

                            <div class="table-container">
                                <h3>مواعيد اليوم</h3>
                                <table id="today-appointments-table">
                                    <thead>
                                        <tr>
                                            <th>الوقت</th>
                                            <th>اسم الزبون</th>
                                            <th>رقم السيارة</th>
                                            <th>نوع الخدمة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- سيتم ملؤها بواسطة JavaScript -->
                                    </tbody>
                                </table>
                            </div>

                            <div class="table-container">
                                <h3>ديون مستحقة قريباً</h3>
                                <table id="upcoming-debts">
                                    <thead>
                                        <tr>
                                            <th>اسم الزبون</th>
                                            <th>المبلغ المتبقي</th>
                                            <th>تاريخ الاستحقاق</th>
                                            <th>الأيام المتبقية</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- سيتم ملؤها بواسطة JavaScript -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <div class="tab-content" id="stats-tab">
                        <div class="stats-container">
                            <div class="stats-card">
                                <h3>إحصائيات البطاقات</h3>
                                <div class="chart-container">
                                    <canvas id="cards-chart"></canvas>
                                </div>
                                <div class="stats-summary">
                                    <div class="stat-item">
                                        <span class="stat-label">البطاقات السارية:</span>
                                        <span class="stat-value" id="active-cards-count">0</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">البطاقات قريبة الانتهاء:</span>
                                        <span class="stat-value" id="expiring-cards-count">0</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">البطاقات المنتهية:</span>
                                        <span class="stat-value" id="expired-cards-count">0</span>
                                    </div>
                                </div>
                            </div>

                            <div class="stats-card">
                                <h3>إحصائيات المواعيد</h3>
                                <div class="chart-container">
                                    <canvas id="appointments-chart"></canvas>
                                </div>
                                <div class="stats-summary">
                                    <div class="stat-item">
                                        <span class="stat-label">مواعيد هذا الشهر:</span>
                                        <span class="stat-value" id="month-appointments-count">0</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">مواعيد الشهر الماضي:</span>
                                        <span class="stat-value" id="last-month-appointments-count">0</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">نسبة النمو:</span>
                                        <span class="stat-value" id="appointments-growth">0%</span>
                                    </div>
                                </div>
                            </div>

                            <div class="stats-card">
                                <h3>إحصائيات الديون</h3>
                                <div class="chart-container">
                                    <canvas id="debts-chart"></canvas>
                                </div>
                                <div class="stats-summary">
                                    <div class="stat-item">
                                        <span class="stat-label">الديون النشطة:</span>
                                        <span class="stat-value" id="active-debts-count">0</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">الديون المتأخرة:</span>
                                        <span class="stat-value" id="overdue-debts-count">0</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">الديون المسددة:</span>
                                        <span class="stat-value" id="paid-debts-count">0</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">إجمالي المبالغ المستحقة:</span>
                                        <span class="stat-value" id="total-debt-amount">0</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- قسم بطاقات الغاز -->
            <section id="gas-cards">
                <h2>إدارة بطاقات الغاز</h2>
                <div class="action-bar">
                    <div class="action-buttons">
                        <button type="button" id="add-card-btn" class="btn primary"><i class="fas fa-plus"></i> إضافة بطاقة جديدة</button>
                        <button type="button" id="print-cards-btn" class="btn"><i class="fas fa-print"></i> طباعة التقرير</button>
                        <button type="button" id="export-cards-pdf" class="btn"><i class="fas fa-file-pdf"></i> تصدير PDF</button>
                    </div>
                    <div class="search-container">
                        <input type="text" id="search-cards" placeholder="بحث عن بطاقة...">
                        <button type="button" class="btn" title="بحث"><i class="fas fa-search"></i></button>
                    </div>
                </div>

                <div class="table-container">
                    <table id="gas-cards-table">
                        <thead>
                            <tr>
                                <th>اسم الزبون</th>
                                <th>رقم السيارة</th>
                                <th>رقم البطاقة</th>
                                <th>تاريخ الإصدار</th>
                                <th>تاريخ الانتهاء</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- سيتم ملؤها بواسطة JavaScript -->
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- قسم المواعيد -->
            <section id="appointments">
                <h2>إدارة المواعيد</h2>
                <div class="action-bar">
                    <div class="action-buttons">
                        <button type="button" id="add-appointment-btn" class="btn primary"><i class="fas fa-plus"></i> إضافة موعد جديد</button>
                        <button type="button" id="print-appointments-btn" class="btn"><i class="fas fa-print"></i> طباعة التقرير</button>
                        <button type="button" id="export-appointments-pdf" class="btn"><i class="fas fa-file-pdf"></i> تصدير PDF</button>
                    </div>
                    <div class="search-container">
                        <input type="text" id="search-appointments" placeholder="بحث عن موعد...">
                        <button type="button" class="btn" title="بحث"><i class="fas fa-search"></i></button>
                    </div>
                </div>

                <div class="calendar-container">
                    <div class="calendar-header">
                        <button type="button" id="prev-month" class="btn" title="الشهر السابق"><i class="fas fa-chevron-right"></i></button>
                        <h3 id="current-month">شهر السنة</h3>
                        <button type="button" id="next-month" class="btn" title="الشهر التالي"><i class="fas fa-chevron-left"></i></button>
                    </div>
                    <div class="calendar" id="appointments-calendar">
                        <!-- سيتم إنشاؤه بواسطة JavaScript -->
                    </div>
                </div>

                <div class="table-container">
                    <h3 id="appointments-list-title">مواعيد اليوم</h3>
                    <table id="appointments-table">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>الوقت</th>
                                <th>اسم الزبون</th>
                                <th>رقم السيارة</th>
                                <th>نوع الخدمة</th>
                                <th>ملاحظات</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- سيتم ملؤها بواسطة JavaScript -->
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- قسم الزبائن -->
            <section id="customers">
                <h2>إدارة الزبائن</h2>
                <div class="action-bar">
                    <div class="action-buttons">
                        <button type="button" id="add-customer-btn" class="btn primary"><i class="fas fa-plus"></i> إضافة زبون جديد</button>
                        <button type="button" id="print-customers-btn" class="btn"><i class="fas fa-print"></i> طباعة التقرير</button>
                        <button type="button" id="export-customers-pdf" class="btn"><i class="fas fa-file-pdf"></i> تصدير PDF</button>
                    </div>
                    <div class="search-container">
                        <input type="text" id="search-customers" placeholder="بحث عن زبون...">
                        <button type="button" class="btn" title="بحث"><i class="fas fa-search"></i></button>
                    </div>
                </div>

                <div class="table-container">
                    <table id="customers-table">
                        <thead>
                            <tr>
                                <th>الاسم</th>
                                <th>رقم الهاتف</th>
                                <th>عدد السيارات</th>
                                <th>تاريخ آخر زيارة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- سيتم ملؤها بواسطة JavaScript -->
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- قسم الموردين -->
            <section id="suppliers">
                <h2>إدارة الموردين</h2>
                <div class="action-bar">
                    <div class="action-buttons">
                        <button type="button" id="add-supplier-btn" class="btn primary"><i class="fas fa-plus"></i> إضافة مورد جديد</button>
                        <button type="button" id="print-suppliers-btn" class="btn"><i class="fas fa-print"></i> طباعة التقرير</button>
                        <button type="button" id="export-suppliers-pdf" class="btn"><i class="fas fa-file-pdf"></i> تصدير PDF</button>
                    </div>
                    <div class="search-container">
                        <input type="text" id="search-suppliers" placeholder="بحث عن مورد...">
                        <button type="button" class="btn" title="بحث"><i class="fas fa-search"></i></button>
                    </div>
                </div>

                <div class="table-container">
                    <table id="suppliers-table">
                        <thead>
                            <tr>
                                <th>اسم المورد</th>
                                <th>رقم الهاتف</th>
                                <th>العنوان</th>
                                <th>نوع المنتجات</th>
                                <th>آخر عملية شراء</th>
                                <th>إجمالي المشتريات</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- سيتم ملؤها بواسطة JavaScript -->
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- قسم المخزون -->
            <section id="inventory">
                <h2>إدارة المخزون</h2>
                <div class="action-bar">
                    <div class="action-buttons">
                        <button type="button" id="add-item-btn" class="btn primary"><i class="fas fa-plus"></i> إضافة صنف جديد</button>
                        <button type="button" id="adjust-stock-btn" class="btn warning"><i class="fas fa-edit"></i> تعديل المخزون</button>
                        <button type="button" id="print-inventory-btn" class="btn"><i class="fas fa-print"></i> طباعة التقرير</button>
                        <button type="button" id="export-inventory-pdf" class="btn"><i class="fas fa-file-pdf"></i> تصدير PDF</button>
                    </div>
                    <div class="search-container">
                        <input type="text" id="search-inventory" placeholder="بحث عن صنف...">
                        <button type="button" class="btn" title="بحث"><i class="fas fa-search"></i></button>
                    </div>
                </div>

                <div class="filter-bar">
                    <div class="filter-group">
                        <label for="category-filter">تصفية حسب الفئة:</label>
                        <select id="category-filter">
                            <option value="all">الكل</option>
                            <option value="gas-parts">قطع غيار الغاز</option>
                            <option value="tools">أدوات</option>
                            <option value="accessories">إكسسوارات</option>
                            <option value="consumables">مواد استهلاكية</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="stock-status-filter">تصفية حسب حالة المخزون:</label>
                        <select id="stock-status-filter">
                            <option value="all">الكل</option>
                            <option value="in-stock">متوفر</option>
                            <option value="low-stock">مخزون منخفض</option>
                            <option value="out-of-stock">نفد المخزون</option>
                        </select>
                    </div>
                </div>

                <div class="table-container">
                    <table id="inventory-table">
                        <thead>
                            <tr>
                                <th>كود الصنف</th>
                                <th>اسم الصنف</th>
                                <th>الفئة</th>
                                <th>الكمية المتوفرة</th>
                                <th>الحد الأدنى</th>
                                <th>سعر الشراء</th>
                                <th>سعر البيع</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- سيتم ملؤها بواسطة JavaScript -->
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- قسم المبيعات -->
            <section id="sales">
                <h2>إدارة المبيعات</h2>
                <div class="action-bar">
                    <div class="action-buttons">
                        <button type="button" id="new-sale-btn" class="btn primary"><i class="fas fa-plus"></i> فاتورة بيع جديدة</button>
                        <button type="button" id="print-sales-btn" class="btn"><i class="fas fa-print"></i> طباعة التقرير</button>
                        <button type="button" id="export-sales-pdf" class="btn"><i class="fas fa-file-pdf"></i> تصدير PDF</button>
                    </div>
                    <div class="search-container">
                        <input type="text" id="search-sales" placeholder="بحث في المبيعات...">
                        <button type="button" class="btn" title="بحث"><i class="fas fa-search"></i></button>
                    </div>
                </div>

                <div class="filter-bar">
                    <div class="filter-group">
                        <label for="sales-date-filter">تصفية حسب التاريخ:</label>
                        <select id="sales-date-filter">
                            <option value="today">اليوم</option>
                            <option value="week">هذا الأسبوع</option>
                            <option value="month">هذا الشهر</option>
                            <option value="year">هذا العام</option>
                            <option value="custom">فترة مخصصة</option>
                        </select>
                    </div>
                    <div class="filter-group custom-date-range" id="custom-date-range">
                        <label for="sales-date-from">من:</label>
                        <input type="date" id="sales-date-from">
                        <label for="sales-date-to">إلى:</label>
                        <input type="date" id="sales-date-to">
                    </div>
                </div>

                <div class="sales-summary">
                    <div class="summary-card">
                        <h4>إجمالي المبيعات</h4>
                        <span id="total-sales-amount">0 د.ج</span>
                    </div>
                    <div class="summary-card">
                        <h4>عدد الفواتير</h4>
                        <span id="total-sales-count">0</span>
                    </div>
                    <div class="summary-card">
                        <h4>متوسط الفاتورة</h4>
                        <span id="average-sale-amount">0 د.ج</span>
                    </div>
                </div>

                <div class="table-container">
                    <table id="sales-table">
                        <thead>
                            <tr>
                                <th>رقم الفاتورة</th>
                                <th>التاريخ</th>
                                <th>اسم الزبون</th>
                                <th>إجمالي المبلغ</th>
                                <th>طريقة الدفع</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- سيتم ملؤها بواسطة JavaScript -->
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- قسم المشتريات -->
            <section id="purchases">
                <h2>إدارة المشتريات</h2>
                <div class="action-bar">
                    <div class="action-buttons">
                        <button type="button" id="new-purchase-btn" class="btn primary"><i class="fas fa-plus"></i> فاتورة شراء جديدة</button>
                        <button type="button" id="print-purchases-btn" class="btn"><i class="fas fa-print"></i> طباعة التقرير</button>
                        <button type="button" id="export-purchases-pdf" class="btn"><i class="fas fa-file-pdf"></i> تصدير PDF</button>
                    </div>
                    <div class="search-container">
                        <input type="text" id="search-purchases" placeholder="بحث في المشتريات...">
                        <button type="button" class="btn" title="بحث"><i class="fas fa-search"></i></button>
                    </div>
                </div>

                <div class="filter-bar">
                    <div class="filter-group">
                        <label for="purchases-date-filter">تصفية حسب التاريخ:</label>
                        <select id="purchases-date-filter">
                            <option value="today">اليوم</option>
                            <option value="week">هذا الأسبوع</option>
                            <option value="month">هذا الشهر</option>
                            <option value="year">هذا العام</option>
                            <option value="custom">فترة مخصصة</option>
                        </select>
                    </div>
                    <div class="filter-group custom-date-range" id="custom-purchase-date-range">
                        <label for="purchases-date-from">من:</label>
                        <input type="date" id="purchases-date-from">
                        <label for="purchases-date-to">إلى:</label>
                        <input type="date" id="purchases-date-to">
                    </div>
                    <div class="filter-group">
                        <label for="supplier-filter">تصفية حسب المورد:</label>
                        <select id="supplier-filter">
                            <option value="all">الكل</option>
                            <!-- سيتم ملؤها بواسطة JavaScript -->
                        </select>
                    </div>
                </div>

                <div class="purchases-summary">
                    <div class="summary-card">
                        <h4>إجمالي المشتريات</h4>
                        <span id="total-purchases-amount">0 د.ج</span>
                    </div>
                    <div class="summary-card">
                        <h4>عدد الفواتير</h4>
                        <span id="total-purchases-count">0</span>
                    </div>
                    <div class="summary-card">
                        <h4>متوسط الفاتورة</h4>
                        <span id="average-purchase-amount">0 د.ج</span>
                    </div>
                </div>

                <div class="table-container">
                    <table id="purchases-table">
                        <thead>
                            <tr>
                                <th>رقم الفاتورة</th>
                                <th>التاريخ</th>
                                <th>اسم المورد</th>
                                <th>إجمالي المبلغ</th>
                                <th>طريقة الدفع</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- سيتم ملؤها بواسطة JavaScript -->
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- قسم الديون -->
            <section id="debts">
                <h2>إدارة الديون</h2>
                <div class="action-bar">
                    <div class="action-buttons">
                        <button type="button" id="add-debt-btn" class="btn primary"><i class="fas fa-plus"></i> إضافة دين جديد</button>
                        <button type="button" id="add-payment-btn" class="btn success"><i class="fas fa-money-bill"></i> تسجيل دفعة</button>
                        <button type="button" id="print-debts-btn" class="btn"><i class="fas fa-print"></i> طباعة التقرير</button>
                        <button type="button" id="print-monthly-debts-btn" class="btn"><i class="fas fa-calendar-alt"></i> تقرير شهري</button>
                        <button type="button" id="export-debts-pdf" class="btn"><i class="fas fa-file-pdf"></i> تصدير PDF</button>
                    </div>
                    <div class="search-container">
                        <input type="text" id="search-debts" placeholder="بحث عن دين...">
                        <button type="button" class="btn" title="بحث"><i class="fas fa-search"></i></button>
                    </div>
                </div>
                <div class="filter-bar">
                    <div class="filter-group">
                        <label for="debt-status-filter">تصفية حسب الحالة:</label>
                        <select id="debt-status-filter">
                            <option value="all">الكل</option>
                            <option value="active">نشطة</option>
                            <option value="overdue">متأخرة</option>
                            <option value="upcoming">مستحقة قريباً</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="debt-customer-filter">تصفية حسب الزبون:</label>
                        <select id="debt-customer-filter">
                            <option value="all">الكل</option>
                            <!-- سيتم ملؤها بواسطة JavaScript -->
                        </select>
                    </div>
                </div>

                <div class="tabs">
                    <div class="tab-buttons">
                        <button type="button" class="tab-btn active" data-tab="active-debts">الديون النشطة</button>
                        <button type="button" class="tab-btn" data-tab="paid-debts">الديون المسددة</button>
                        <button type="button" class="tab-btn" data-tab="payments-history">سجل المدفوعات</button>
                    </div>

                    <div class="tab-content active" id="active-debts-tab">
                        <div class="table-container">
                            <table id="active-debts-table">
                                <thead>
                                    <tr>
                                        <th>اسم الزبون</th>
                                        <th>المبلغ الأصلي</th>
                                        <th>المبلغ المتبقي</th>
                                        <th>تاريخ الاستحقاق</th>
                                        <th>الأيام المتبقية</th>
                                        <th>ملاحظات</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- سيتم ملؤها بواسطة JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div class="tab-content" id="paid-debts-tab">
                        <div class="table-container">
                            <table id="paid-debts-table">
                                <thead>
                                    <tr>
                                        <th>اسم الزبون</th>
                                        <th>المبلغ الأصلي</th>
                                        <th>تاريخ الإنشاء</th>
                                        <th>تاريخ السداد</th>
                                        <th>ملاحظات</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- سيتم ملؤها بواسطة JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div class="tab-content" id="payments-history-tab">
                        <div class="table-container">
                            <table id="payments-history-table">
                                <thead>
                                    <tr>
                                        <th>اسم الزبون</th>
                                        <th>المبلغ المدفوع</th>
                                        <th>تاريخ الدفع</th>
                                        <th>طريقة الدفع</th>
                                        <th>ملاحظات</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- سيتم ملؤها بواسطة JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </section>

            <!-- قسم الشهادات -->
            <section id="certificates">
                <h2>إدارة الشهادات</h2>
                <div class="action-bar">
                    <div class="action-buttons">
                        <button type="button" id="search-customer-for-certificate" class="btn primary">
                            <i class="fas fa-search"></i> البحث عن زبون لطباعة الشهادة
                        </button>
                        <button type="button" id="print-certificates-btn" class="btn">
                            <i class="fas fa-print"></i> طباعة التقرير
                        </button>
                        <button type="button" id="export-certificates-pdf" class="btn">
                            <i class="fas fa-file-pdf"></i> تصدير PDF
                        </button>
                    </div>
                    <div class="search-container">
                        <input type="text" id="search-certificates" placeholder="بحث في الشهادات...">
                        <button type="button" class="btn" title="بحث"><i class="fas fa-search"></i></button>
                    </div>
                </div>

                <div class="certificates-tabs">
                    <div class="tab-buttons">
                        <button type="button" class="tab-btn active" data-tab="installation-certificates">شهادات التركيب</button>
                        <button type="button" class="tab-btn" data-tab="monitoring-certificates">شهادات المراقبة الدورية</button>
                        <button type="button" class="tab-btn" data-tab="certificate-reminders">التذكيرات</button>
                    </div>

                    <div class="tab-content active" id="installation-certificates-tab">
                        <h3>شهادات التركيب</h3>
                        <div class="table-container">
                            <table id="installation-certificates-table">
                                <thead>
                                    <tr>
                                        <th>اسم الزبون</th>
                                        <th>رقم السيارة</th>
                                        <th>تاريخ التركيب</th>
                                        <th>تاريخ انتهاء الصلاحية</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- سيتم ملؤها بواسطة JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div class="tab-content" id="monitoring-certificates-tab">
                        <h3>شهادات المراقبة الدورية</h3>
                        <div class="table-container">
                            <table id="monitoring-certificates-table">
                                <thead>
                                    <tr>
                                        <th>اسم الزبون</th>
                                        <th>رقم السيارة</th>
                                        <th>تاريخ آخر مراقبة</th>
                                        <th>تاريخ المراقبة القادمة</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- سيتم ملؤها بواسطة JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div class="tab-content" id="certificate-reminders-tab">
                        <h3>التذكيرات</h3>
                        <div class="reminders-container">
                            <div class="reminder-card urgent">
                                <h4><i class="fas fa-exclamation-triangle"></i> تذكيرات عاجلة</h4>
                                <div class="table-container">
                                    <table id="urgent-reminders-table">
                                        <thead>
                                            <tr>
                                                <th>اسم الزبون</th>
                                                <th>نوع الشهادة</th>
                                                <th>تاريخ الانتهاء</th>
                                                <th>الأيام المتبقية</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <!-- سيتم ملؤها بواسطة JavaScript -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <div class="reminder-card warning">
                                <h4><i class="fas fa-clock"></i> تذكيرات قريبة</h4>
                                <div class="table-container">
                                    <table id="upcoming-reminders-table">
                                        <thead>
                                            <tr>
                                                <th>اسم الزبون</th>
                                                <th>نوع الشهادة</th>
                                                <th>تاريخ الانتهاء</th>
                                                <th>الأيام المتبقية</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <!-- سيتم ملؤها بواسطة JavaScript -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- قسم الإعدادات -->
            <section id="settings">
                <h2>الإعدادات</h2>
                <div class="settings-container">
                    <div class="settings-card">
                        <h3>إعدادات النظام</h3>
                        <form id="system-settings">
                            <div class="form-group">
                                <label for="shop-name">اسم المحل:</label>
                                <input type="text" id="shop-name" placeholder="أدخل اسم المحل">
                            </div>
                            <div class="form-group">
                                <label for="reminder-days">التذكير قبل انتهاء البطاقة بـ (أيام):</label>
                                <input type="number" id="reminder-days" min="1" max="90" value="30">
                            </div>
                            <div class="form-group">
                                <label for="debt-reminder-days">التذكير قبل استحقاق الدين بـ (أيام):</label>
                                <input type="number" id="debt-reminder-days" min="1" max="30" value="7">
                            </div>
                            <div class="form-group">
                                <label for="working-hours-start">ساعات العمل من:</label>
                                <input type="time" id="working-hours-start" value="08:00">
                            </div>
                            <div class="form-group">
                                <label for="working-hours-end">ساعات العمل إلى:</label>
                                <input type="time" id="working-hours-end" value="18:00">
                            </div>
                            <button type="submit" class="btn primary">حفظ الإعدادات</button>
                        </form>
                    </div>

                    <div class="settings-card">
                        <h3>📱 إعدادات تيليجرام للنسخ الاحتياطي</h3>
                        <form id="telegram-settings">
                            <div class="form-group">
                                <label>
                                    <input type="checkbox" id="telegram-enabled"> تفعيل النسخ الاحتياطي إلى تيليجرام
                                </label>
                            </div>

                            <div class="form-group">
                                <label for="telegram-bot-token">رمز البوت (Bot Token):</label>
                                <input type="password" id="telegram-bot-token" placeholder="123456789:ABCdefGHIjklMNOpqrsTUVwxyz">
                                <small>احصل على الرمز من @BotFather في تيليجرام</small>
                            </div>

                            <div class="form-group">
                                <label for="telegram-chat-id">معرف المحادثة (Chat ID):</label>
                                <input type="text" id="telegram-chat-id" placeholder="-1001234567890">
                                <small>معرف المجموعة أو المحادثة الخاصة</small>
                            </div>

                            <div class="form-group">
                                <label>
                                    <input type="checkbox" id="telegram-auto-backup"> النسخ الاحتياطي التلقائي
                                </label>
                            </div>

                            <div class="form-group">
                                <label for="telegram-backup-frequency">تكرار النسخ الاحتياطي:</label>
                                <select id="telegram-backup-frequency">
                                    <option value="manual">يدوي فقط</option>
                                    <option value="daily">يومي</option>
                                    <option value="weekly">أسبوعي</option>
                                </select>
                            </div>

                            <div class="telegram-controls">
                                <button type="button" class="btn info" onclick="testTelegramConnection()">
                                    <i class="fas fa-wifi"></i> اختبار الاتصال
                                </button>
                                <button type="button" class="btn success" onclick="sendBackupToTelegram('manual')">
                                    <i class="fas fa-paper-plane"></i> إرسال نسخة احتياطية
                                </button>
                                <button type="button" class="btn warning" onclick="sendDailyReportToTelegram()">
                                    <i class="fas fa-chart-line"></i> إرسال تقرير يومي
                                </button>
                            </div>

                            <div class="telegram-help">
                                <h4>📖 كيفية إعداد تيليجرام:</h4>
                                <ol>
                                    <li>ابحث عن @BotFather في تيليجرام وابدأ محادثة</li>
                                    <li>أرسل الأمر /newbot واتبع التعليمات</li>
                                    <li>انسخ رمز البوت (Bot Token) والصقه أعلاه</li>
                                    <li>أضف البوت إلى مجموعة أو ابدأ محادثة خاصة معه</li>
                                    <li>للحصول على Chat ID، ابحث عن @userinfobot وأرسل له رسالة</li>
                                    <li>انسخ معرف المحادثة والصقه أعلاه</li>
                                    <li>اضغط "اختبار الاتصال" للتأكد من صحة الإعدادات</li>
                                </ol>
                            </div>

                            <button type="submit" class="btn primary">حفظ إعدادات تيليجرام</button>
                        </form>
                    </div>

                    <div class="settings-card">
                        <h3>النسخ الاحتياطي واستعادة البيانات</h3>
                        <div id="backup-restore-section">
                            <div class="form-group">
                                <label for="backup-select">النسخ الاحتياطية:</label>
                                <select id="backup-select">
                                    <option value="">لا توجد نسخ احتياطية</option>
                                </select>
                            </div>
                            <div class="backup-controls">
                                <button type="button" id="create-backup-btn" class="btn"><i class="fas fa-save"></i> إنشاء نسخة احتياطية</button>
                                <button type="button" id="restore-backup-btn" class="btn"><i class="fas fa-undo"></i> استعادة نسخة احتياطية</button>
                            </div>
                            <div class="backup-controls">
                                <button type="button" id="export-data-btn" class="btn"><i class="fas fa-file-export"></i> تصدير البيانات</button>
                                <button type="button" id="import-data-btn" class="btn"><i class="fas fa-file-import"></i> استيراد البيانات</button>
                            </div>
                        </div>
                        <div class="backup-controls">
                            <button type="button" id="export-data" class="btn"><i class="fas fa-download"></i> تصدير البيانات (محلياً)</button>
                            <button type="button" id="import-data" class="btn"><i class="fas fa-upload"></i> استيراد البيانات (محلياً)</button>
                            <input type="file" id="import-file" accept=".json" class="hidden-input" aria-label="ملف البيانات" title="اختر ملف JSON لاستيراد البيانات">
                        </div>
                        <div class="danger-zone">
                            <h4>منطقة الخطر</h4>
                            <button type="button" id="clear-data" class="btn danger"><i class="fas fa-trash"></i> مسح جميع البيانات</button>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- النوافذ المنبثقة -->
    <div class="modal" id="card-modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2 id="card-modal-title">إضافة بطاقة غاز جديدة</h2>
            <form id="card-form">
                <input type="hidden" id="card-id">
                <div class="form-group">
                    <label for="card-customer">اسم الزبون:</label>
                    <select id="card-customer" required>
                        <option value="">اختر زبون</option>
                        <!-- سيتم ملؤها بواسطة JavaScript -->
                    </select>
                </div>
                <div class="form-group">
                    <label for="card-vehicle">رقم السيارة:</label>
                    <select id="card-vehicle" required>
                        <option value="">اختر سيارة</option>
                        <!-- سيتم ملؤها بواسطة JavaScript -->
                    </select>
                </div>
                <div class="form-group">
                    <label for="card-number">رقم البطاقة:</label>
                    <input type="text" id="card-number" required>
                </div>
                <div class="form-group">
                    <label for="card-issue-date">تاريخ الإصدار:</label>
                    <input type="date" id="card-issue-date" required>
                </div>
                <div class="form-group">
                    <label for="card-expiry-date">تاريخ الانتهاء:</label>
                    <input type="date" id="card-expiry-date" required>
                </div>
                <div class="form-group">
                    <label for="card-notes">ملاحظات:</label>
                    <textarea id="card-notes"></textarea>
                </div>
                <div class="form-buttons">
                    <button type="submit" class="btn primary">حفظ</button>
                    <button type="button" class="btn" id="cancel-card">إلغاء</button>
                </div>
            </form>
        </div>
    </div>

    <div class="modal" id="appointment-modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2 id="appointment-modal-title">إضافة موعد جديد</h2>
            <form id="appointment-form">
                <input type="hidden" id="appointment-id">
                <div class="form-group">
                    <label for="appointment-date">التاريخ:</label>
                    <input type="date" id="appointment-date" required>
                </div>
                <div class="form-group">
                    <label for="appointment-time">الوقت:</label>
                    <input type="time" id="appointment-time" required>
                </div>
                <div class="form-group">
                    <label for="appointment-customer">اسم الزبون:</label>
                    <select id="appointment-customer" required>
                        <option value="">اختر زبون</option>
                        <!-- سيتم ملؤها بواسطة JavaScript -->
                    </select>
                </div>
                <div class="form-group">
                    <label for="appointment-vehicle">رقم السيارة:</label>
                    <select id="appointment-vehicle" required>
                        <option value="">اختر سيارة</option>
                        <!-- سيتم ملؤها بواسطة JavaScript -->
                    </select>
                </div>
                <div class="form-group">
                    <label for="appointment-service">نوع الخدمة:</label>
                    <select id="appointment-service" required>
                        <option value="">اختر نوع الخدمة</option>
                        <option value="تركيب">تركيب</option>
                        <option value="صيانة">صيانة</option>
                        <option value="فحص">فحص</option>
                        <option value="تجديد بطاقة">تجديد بطاقة</option>
                        <option value="أخرى">أخرى</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="appointment-notes">ملاحظات:</label>
                    <textarea id="appointment-notes"></textarea>
                </div>
                <div class="form-buttons">
                    <button type="submit" class="btn primary">حفظ</button>
                    <button type="button" class="btn" id="cancel-appointment">إلغاء</button>
                </div>
            </form>
        </div>
    </div>

    <div class="modal" id="customer-modal">
        <div class="modal-content large">
            <span class="close">&times;</span>
            <h2 id="customer-modal-title">إضافة زبون جديد</h2>
            <form id="customer-form">
                <input type="hidden" id="customer-id">

                <!-- معلومات الزبون الشخصية -->
                <div class="customer-form-section">
                    <h3 class="personal-info">
                        <i class="fas fa-user"></i> معلومات الزبون الشخصية / Informations du Client
                    </h3>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="customer-name">اسم الزبون / Nom du client: <span class="required">*</span></label>
                        <input type="text" id="customer-name" required placeholder="مثال: أحمد محمد علي">
                    </div>
                    <div class="form-group">
                        <label for="customer-phone">رقم الهاتف / Téléphone: <span class="required">*</span></label>
                        <input type="tel" id="customer-phone" required placeholder="مثال: 0555123456">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="customer-address">العنوان / Adresse: <span class="required">*</span></label>
                        <input type="text" id="customer-address" required placeholder="مثال: حي السلام، الجزائر العاصمة">
                    </div>
                    <div class="form-group">
                        <label for="customer-email">البريد الإلكتروني / Email:</label>
                        <input type="email" id="customer-email" placeholder="مثال: <EMAIL>">
                    </div>
                </div>

                <!-- معلومات السيارة -->
                <div class="customer-form-section">
                    <h3 class="vehicle-info">
                        <i class="fas fa-car"></i> معلومات السيارة / Renseignement sur le Véhicule
                    </h3>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="vehicle-plate-number">رقم التسجيل / N°IMMATRICULATION: <span class="required">*</span></label>
                        <input type="text" id="vehicle-plate-number" required placeholder="مثال: 1506615-26">
                    </div>
                    <div class="form-group">
                        <label for="vehicle-brand">الماركة / MARQUE: <span class="required">*</span></label>
                        <input type="text" id="vehicle-brand" required placeholder="مثال: Toyota, Peugeot, Renault">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="vehicle-model">النوع / TYPE: <span class="required">*</span></label>
                        <input type="text" id="vehicle-model" required placeholder="مثال: Corolla, 206, Clio">
                    </div>
                    <div class="form-group">
                        <label for="vehicle-chassis-number">رقم السلسلة / N° DE SERIE:</label>
                        <input type="text" id="vehicle-chassis-number" placeholder="مثال: VF32A5FWC12345678">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="vehicle-year">سنة الصنع / Année:</label>
                        <input type="number" id="vehicle-year" min="1990" max="2030" placeholder="مثال: 2020">
                    </div>
                    <div class="form-group">
                        <label for="vehicle-color">اللون / Couleur:</label>
                        <input type="text" id="vehicle-color" placeholder="مثال: أبيض، أزرق، أحمر">
                    </div>
                </div>

                <!-- معلومات الخزان -->
                <div class="customer-form-section">
                    <h3 class="tank-info">
                        <i class="fas fa-gas-pump"></i> معلومات الخزان / Renseignement sur Réservoir
                    </h3>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="tank-type">نوع الخزان / TYPE RESERVOIR: <span class="required">*</span></label>
                        <select id="tank-type" required>
                            <option value="">اختر نوع الخزان</option>
                            <option value="cylindrical">أسطواني / Cylindrique</option>
                            <option value="toroidal">دائري / Toroïdal</option>
                            <option value="rectangular">مستطيل / Rectangulaire</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="tank-brand">ماركة الخزان / MARQUE: <span class="required">*</span></label>
                        <input type="text" id="tank-brand" required placeholder="مثال: TMS, STAKO, BRC">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="tank-serial-number">رقم السلسلة / N° DE SERIE: <span class="required">*</span></label>
                        <input type="text" id="tank-serial-number" required placeholder="مثال: 1506623">
                    </div>
                    <div class="form-group">
                        <label for="tank-capacity">السعة / CAPACITE (لتر): <span class="required">*</span></label>
                        <input type="number" id="tank-capacity" required min="20" max="200" step="0.1" placeholder="مثال: 60">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="tank-gas-type">نوع الغاز / Type de Gaz:</label>
                        <select id="tank-gas-type">
                            <option value="GPL">GPL (غاز البترول المسال)</option>
                            <option value="CNG">CNG (الغاز الطبيعي المضغوط)</option>
                            <option value="LPG">LPG</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="tank-installation-date">تاريخ التركيب / Date d'installation:</label>
                        <input type="date" id="tank-installation-date">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="tank-manufacture-date">تاريخ الصنع / Date de fabrication:</label>
                        <input type="month" id="tank-manufacture-date" placeholder="الشهر/السنة" pattern="[0-9]{4}-[0-9]{2}">
                    </div>
                    <div class="form-group">
                        <label for="customer-notes">ملاحظات / Notes:</label>
                        <textarea id="customer-notes" rows="2" placeholder="أي ملاحظات إضافية..."></textarea>
                    </div>
                </div>

                <div class="form-buttons">
                    <button type="submit" class="btn primary">
                        <i class="fas fa-save"></i> حفظ البيانات
                    </button>
                    <button type="button" class="btn" id="cancel-customer">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- نافذة معلومات الزبون -->
    <div class="modal" id="customer-info-modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>معلومات الزبون</h2>
            <div class="customer-info-container">
                <div class="customer-info-header">
                    <div class="customer-info-avatar">
                        <i class="fas fa-user-circle"></i>
                    </div>
                    <div class="customer-info-details">
                        <h3 id="info-customer-name"></h3>
                        <p><i class="fas fa-phone"></i> <span id="info-customer-phone"></span></p>
                        <p><i class="fas fa-envelope"></i> <span id="info-customer-email"></span></p>
                        <p><i class="fas fa-map-marker-alt"></i> <span id="info-customer-address"></span></p>
                    </div>
                </div>

                <div class="customer-info-tabs">
                    <div class="tab-buttons">
                        <button type="button" class="tab-btn active" data-tab="customer-vehicles">السيارات</button>
                        <button type="button" class="tab-btn" data-tab="customer-cards">بطاقات الغاز</button>
                        <button type="button" class="tab-btn" data-tab="customer-appointments">المواعيد</button>
                        <button type="button" class="tab-btn" data-tab="customer-debts">الديون</button>
                    </div>

                    <div class="tab-content active" id="customer-vehicles-tab">
                        <h3>سيارات الزبون</h3>
                        <div class="table-container">
                            <table id="info-vehicles-table">
                                <thead>
                                    <tr>
                                        <th>رقم اللوحة</th>
                                        <th>النوع</th>
                                        <th>الموديل</th>
                                        <th>سنة الصنع</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- سيتم ملؤها بواسطة JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div class="tab-content" id="customer-cards-tab">
                        <h3>بطاقات الغاز</h3>
                        <div class="table-container">
                            <table id="info-cards-table">
                                <thead>
                                    <tr>
                                        <th>رقم البطاقة</th>
                                        <th>رقم السيارة</th>
                                        <th>تاريخ الإصدار</th>
                                        <th>تاريخ الانتهاء</th>
                                        <th>الحالة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- سيتم ملؤها بواسطة JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div class="tab-content" id="customer-appointments-tab">
                        <h3>المواعيد</h3>
                        <div class="table-container">
                            <table id="info-appointments-table">
                                <thead>
                                    <tr>
                                        <th>التاريخ</th>
                                        <th>الوقت</th>
                                        <th>نوع الخدمة</th>
                                        <th>رقم السيارة</th>
                                        <th>الحالة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- سيتم ملؤها بواسطة JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div class="tab-content" id="customer-debts-tab">
                        <h3>الديون</h3>
                        <div class="table-container">
                            <table id="info-debts-table">
                                <thead>
                                    <tr>
                                        <th>المبلغ</th>
                                        <th>المبلغ المتبقي</th>
                                        <th>تاريخ الاستحقاق</th>
                                        <th>الحالة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- سيتم ملؤها بواسطة JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="customer-info-actions">
                    <button type="button" id="print-customer-info" class="btn"><i class="fas fa-print"></i> طباعة بطاقة الزبون</button>
                    <button type="button" id="send-sms-customer" class="btn"><i class="fas fa-sms"></i> إرسال رسالة</button>
                    <button type="button" id="record-visit-btn" class="btn success"><i class="fas fa-check-circle"></i> تسجيل زيارة</button>
                    <button type="button" id="edit-customer-info" class="btn primary"><i class="fas fa-edit"></i> تعديل</button>
                    <button type="button" id="close-customer-info" class="btn">إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة دين جديد -->
    <div class="modal" id="debt-modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2 id="debt-modal-title">إضافة دين جديد</h2>
            <form id="debt-form">
                <input type="hidden" id="debt-id">
                <div class="form-group">
                    <label for="debt-customer">اسم الزبون:</label>
                    <select id="debt-customer" required>
                        <option value="">اختر زبون</option>
                        <!-- سيتم ملؤها بواسطة JavaScript -->
                    </select>
                </div>
                <div class="form-group">
                    <label for="debt-amount">المبلغ:</label>
                    <input type="number" id="debt-amount" min="1" step="0.01" required>
                </div>
                <div class="form-group">
                    <label for="debt-due-date">تاريخ الاستحقاق:</label>
                    <input type="date" id="debt-due-date" required>
                </div>
                <div class="form-group">
                    <label for="debt-reason">سبب الدين:</label>
                    <select id="debt-reason" required>
                        <option value="">اختر السبب</option>
                        <option value="تركيب غاز">تركيب غاز</option>
                        <option value="صيانة">صيانة</option>
                        <option value="قطع غيار">قطع غيار</option>
                        <option value="أخرى">أخرى</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="debt-notes">ملاحظات:</label>
                    <textarea id="debt-notes"></textarea>
                </div>
                <div class="form-buttons">
                    <button type="submit" class="btn primary">حفظ</button>
                    <button type="button" class="btn" id="cancel-debt">إلغاء</button>
                </div>
            </form>
        </div>
    </div>

    <!-- نافذة تسجيل دفعة -->
    <div class="modal" id="payment-modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2 id="payment-modal-title">تسجيل دفعة</h2>
            <form id="payment-form">
                <input type="hidden" id="payment-id">
                <div class="form-group">
                    <label for="payment-debt">اختر الدين:</label>
                    <select id="payment-debt" required>
                        <option value="">اختر الدين</option>
                        <!-- سيتم ملؤها بواسطة JavaScript -->
                    </select>
                </div>
                <div class="debt-info" id="debt-info">
                    <p>الزبون: <span id="payment-customer-name"></span></p>
                    <p>المبلغ الأصلي: <span id="payment-original-amount"></span></p>
                    <p>المبلغ المتبقي: <span id="payment-remaining-amount"></span></p>
                </div>
                <div class="form-group">
                    <label for="payment-amount">المبلغ المدفوع:</label>
                    <input type="number" id="payment-amount" min="1" step="0.01" required>
                </div>
                <div class="form-group">
                    <label for="payment-date">تاريخ الدفع:</label>
                    <input type="date" id="payment-date" required>
                </div>
                <div class="form-group">
                    <label for="payment-method">طريقة الدفع:</label>
                    <select id="payment-method" required>
                        <option value="نقدي">نقدي</option>
                        <option value="تحويل بنكي">تحويل بنكي</option>
                        <option value="شيك">شيك</option>
                        <option value="أخرى">أخرى</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="payment-notes">ملاحظات:</label>
                    <textarea id="payment-notes"></textarea>
                </div>
                <div class="form-buttons">
                    <button type="submit" class="btn primary">حفظ</button>
                    <button type="button" class="btn" id="cancel-payment">إلغاء</button>
                </div>
            </form>
        </div>
    </div>

    <!-- نافذة إضافة مورد جديد -->
    <div class="modal" id="supplier-modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2 id="supplier-modal-title">إضافة مورد جديد</h2>
            <form id="supplier-form">
                <input type="hidden" id="supplier-id">
                <div class="form-group">
                    <label for="supplier-name">اسم المورد:</label>
                    <input type="text" id="supplier-name" required>
                </div>
                <div class="form-group">
                    <label for="supplier-phone">رقم الهاتف:</label>
                    <input type="tel" id="supplier-phone" required>
                </div>
                <div class="form-group">
                    <label for="supplier-email">البريد الإلكتروني:</label>
                    <input type="email" id="supplier-email">
                </div>
                <div class="form-group">
                    <label for="supplier-address">العنوان:</label>
                    <input type="text" id="supplier-address">
                </div>
                <div class="form-group">
                    <label for="supplier-products">نوع المنتجات:</label>
                    <input type="text" id="supplier-products" placeholder="مثال: قطع غيار، أدوات، إكسسوارات">
                </div>
                <div class="form-group">
                    <label for="supplier-notes">ملاحظات:</label>
                    <textarea id="supplier-notes"></textarea>
                </div>
                <div class="form-buttons">
                    <button type="submit" class="btn primary">حفظ</button>
                    <button type="button" class="btn" id="cancel-supplier">إلغاء</button>
                </div>
            </form>
        </div>
    </div>

    <!-- نافذة إضافة صنف جديد للمخزون -->
    <div class="modal" id="item-modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2 id="item-modal-title">إضافة صنف جديد</h2>
            <form id="item-form">
                <input type="hidden" id="item-id">
                <div class="form-group">
                    <label for="item-code">كود الصنف:</label>
                    <input type="text" id="item-code" required>
                </div>
                <div class="form-group">
                    <label for="item-name">اسم الصنف:</label>
                    <input type="text" id="item-name" required>
                </div>
                <div class="form-group">
                    <label for="item-category">الفئة:</label>
                    <select id="item-category" required>
                        <option value="">اختر الفئة</option>
                        <option value="gas-parts">قطع غيار الغاز</option>
                        <option value="tools">أدوات</option>
                        <option value="accessories">إكسسوارات</option>
                        <option value="consumables">مواد استهلاكية</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="item-quantity">الكمية الحالية:</label>
                    <input type="number" id="item-quantity" min="0" required>
                </div>
                <div class="form-group">
                    <label for="item-min-quantity">الحد الأدنى:</label>
                    <input type="number" id="item-min-quantity" min="1" required>
                </div>
                <div class="form-group">
                    <label for="item-purchase-price">سعر الشراء:</label>
                    <input type="number" id="item-purchase-price" min="0" step="0.01" required>
                </div>
                <div class="form-group">
                    <label for="item-sale-price">سعر البيع:</label>
                    <input type="number" id="item-sale-price" min="0" step="0.01" required>
                </div>
                <div class="form-group">
                    <label for="item-description">الوصف:</label>
                    <textarea id="item-description"></textarea>
                </div>
                <div class="form-buttons">
                    <button type="submit" class="btn primary">حفظ</button>
                    <button type="button" class="btn" id="cancel-item">إلغاء</button>
                </div>
            </form>
        </div>
    </div>

    <!-- نافذة تعديل المخزون -->
    <div class="modal" id="stock-adjustment-modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>تعديل المخزون</h2>
            <form id="stock-adjustment-form">
                <div class="form-group">
                    <label for="adjustment-item">الصنف:</label>
                    <select id="adjustment-item" required>
                        <option value="">اختر الصنف</option>
                        <!-- سيتم ملؤها بواسطة JavaScript -->
                    </select>
                </div>
                <div class="item-info" id="adjustment-item-info">
                    <p>الكمية الحالية: <span id="current-quantity">0</span></p>
                </div>
                <div class="form-group">
                    <label for="adjustment-type">نوع التعديل:</label>
                    <select id="adjustment-type" required>
                        <option value="add">إضافة</option>
                        <option value="subtract">خصم</option>
                        <option value="set">تحديد الكمية</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="adjustment-quantity">الكمية:</label>
                    <input type="number" id="adjustment-quantity" min="1" required>
                </div>
                <div class="form-group">
                    <label for="adjustment-reason">السبب:</label>
                    <select id="adjustment-reason" required>
                        <option value="">اختر السبب</option>
                        <option value="purchase">شراء جديد</option>
                        <option value="sale">بيع</option>
                        <option value="damage">تلف</option>
                        <option value="loss">فقدان</option>
                        <option value="return">إرجاع</option>
                        <option value="correction">تصحيح</option>
                        <option value="other">أخرى</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="adjustment-notes">ملاحظات:</label>
                    <textarea id="adjustment-notes"></textarea>
                </div>
                <div class="form-buttons">
                    <button type="submit" class="btn primary">حفظ</button>
                    <button type="button" class="btn" id="cancel-adjustment">إلغاء</button>
                </div>
            </form>
        </div>
    </div>

    <!-- نافذة فاتورة بيع جديدة -->
    <div class="modal" id="sale-modal">
        <div class="modal-content large">
            <span class="close">&times;</span>
            <h2 id="sale-modal-title">فاتورة بيع جديدة</h2>
            <form id="sale-form">
                <input type="hidden" id="sale-id">
                <div class="form-row">
                    <div class="form-group">
                        <label for="sale-customer">الزبون:</label>
                        <select id="sale-customer" required>
                            <option value="">اختر الزبون</option>
                            <!-- سيتم ملؤها بواسطة JavaScript -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="sale-date">التاريخ:</label>
                        <input type="date" id="sale-date" required>
                    </div>
                    <div class="form-group">
                        <label for="sale-payment-method">طريقة الدفع:</label>
                        <select id="sale-payment-method" required>
                            <option value="نقدي">نقدي</option>
                            <option value="تحويل بنكي">تحويل بنكي</option>
                            <option value="شيك">شيك</option>
                            <option value="آجل">آجل</option>
                        </select>
                    </div>
                </div>

                <h3>الأصناف</h3>
                <div class="items-section">
                    <div class="add-item-row">
                        <select id="sale-item-select" title="اختر الصنف للبيع">
                            <option value="">اختر الصنف</option>
                            <!-- سيتم ملؤها بواسطة JavaScript -->
                        </select>
                        <input type="number" id="sale-item-quantity" placeholder="الكمية" min="1">
                        <button type="button" id="add-sale-item" class="btn primary">إضافة</button>
                    </div>

                    <div class="table-container">
                        <table id="sale-items-table">
                            <thead>
                                <tr>
                                    <th>الصنف</th>
                                    <th>الكمية</th>
                                    <th>السعر</th>
                                    <th>الإجمالي</th>
                                    <th>إجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- سيتم ملؤها بواسطة JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="sale-summary">
                    <div class="summary-row">
                        <span>المجموع الفرعي:</span>
                        <span id="sale-subtotal">0 د.ج</span>
                    </div>
                    <div class="summary-row">
                        <span>الضريبة (19%):</span>
                        <span id="sale-tax">0 د.ج</span>
                    </div>
                    <div class="summary-row total">
                        <span>الإجمالي:</span>
                        <span id="sale-total">0 د.ج</span>
                    </div>
                </div>

                <div class="form-group">
                    <label for="sale-notes">ملاحظات:</label>
                    <textarea id="sale-notes"></textarea>
                </div>

                <div class="form-buttons">
                    <button type="submit" class="btn primary">حفظ الفاتورة</button>
                    <button type="button" id="print-sale-invoice" class="btn">طباعة الفاتورة</button>
                    <button type="button" class="btn" id="cancel-sale">إلغاء</button>
                </div>
            </form>
        </div>
    </div>

    <!-- نافذة فاتورة شراء جديدة -->
    <div class="modal" id="purchase-modal">
        <div class="modal-content large">
            <span class="close">&times;</span>
            <h2 id="purchase-modal-title">فاتورة شراء جديدة</h2>
            <form id="purchase-form">
                <input type="hidden" id="purchase-id">
                <div class="form-row">
                    <div class="form-group">
                        <label for="purchase-supplier">المورد:</label>
                        <select id="purchase-supplier" required>
                            <option value="">اختر المورد</option>
                            <!-- سيتم ملؤها بواسطة JavaScript -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="purchase-date">التاريخ:</label>
                        <input type="date" id="purchase-date" required>
                    </div>
                    <div class="form-group">
                        <label for="purchase-payment-method">طريقة الدفع:</label>
                        <select id="purchase-payment-method" required>
                            <option value="نقدي">نقدي</option>
                            <option value="تحويل بنكي">تحويل بنكي</option>
                            <option value="شيك">شيك</option>
                            <option value="آجل">آجل</option>
                        </select>
                    </div>
                </div>

                <h3>الأصناف</h3>
                <div class="items-section">
                    <div class="add-item-row">
                        <select id="purchase-item-select" title="اختر الصنف للشراء">
                            <option value="">اختر الصنف</option>
                            <!-- سيتم ملؤها بواسطة JavaScript -->
                        </select>
                        <input type="number" id="purchase-item-quantity" placeholder="الكمية" min="1">
                        <input type="number" id="purchase-item-price" placeholder="سعر الشراء" min="0" step="0.01">
                        <button type="button" id="add-purchase-item" class="btn primary">إضافة</button>
                    </div>

                    <div class="table-container">
                        <table id="purchase-items-table">
                            <thead>
                                <tr>
                                    <th>الصنف</th>
                                    <th>الكمية</th>
                                    <th>السعر</th>
                                    <th>الإجمالي</th>
                                    <th>إجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- سيتم ملؤها بواسطة JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="purchase-summary">
                    <div class="summary-row">
                        <span>المجموع الفرعي:</span>
                        <span id="purchase-subtotal">0 د.ج</span>
                    </div>
                    <div class="summary-row">
                        <span>الضريبة (19%):</span>
                        <span id="purchase-tax">0 د.ج</span>
                    </div>
                    <div class="summary-row total">
                        <span>الإجمالي:</span>
                        <span id="purchase-total">0 د.ج</span>
                    </div>
                </div>

                <div class="form-group">
                    <label for="purchase-notes">ملاحظات:</label>
                    <textarea id="purchase-notes"></textarea>
                </div>

                <div class="form-buttons">
                    <button type="submit" class="btn primary">حفظ الفاتورة</button>
                    <button type="button" id="print-purchase-invoice" class="btn">طباعة الفاتورة</button>
                    <button type="button" class="btn" id="cancel-purchase">إلغاء</button>
                </div>
            </form>
        </div>
    </div>

    <!-- نافذة عرض تفاصيل الفاتورة -->
    <div class="modal" id="invoice-details-modal">
        <div class="modal-content large">
            <span class="close">&times;</span>
            <h2 id="invoice-details-title">تفاصيل الفاتورة</h2>
            <div class="invoice-details-content">
                <div class="invoice-header">
                    <div class="invoice-info">
                        <h3>فاتورة رقم: <span id="invoice-number"></span></h3>
                        <p>التاريخ: <span id="invoice-date"></span></p>
                        <p>طريقة الدفع: <span id="invoice-payment-method"></span></p>
                    </div>
                    <div class="party-info">
                        <h4 id="party-label">الزبون:</h4>
                        <p id="party-name"></p>
                        <p id="party-phone"></p>
                    </div>
                </div>

                <div class="table-container">
                    <table id="invoice-items-table">
                        <thead>
                            <tr>
                                <th>الصنف</th>
                                <th>الكمية</th>
                                <th>السعر</th>
                                <th>الإجمالي</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- سيتم ملؤها بواسطة JavaScript -->
                        </tbody>
                    </table>
                </div>

                <div class="invoice-summary">
                    <div class="summary-row">
                        <span>المجموع الفرعي:</span>
                        <span id="invoice-subtotal">0 د.ج</span>
                    </div>
                    <div class="summary-row">
                        <span>الضريبة (19%):</span>
                        <span id="invoice-tax">0 د.ج</span>
                    </div>
                    <div class="summary-row total">
                        <span>الإجمالي:</span>
                        <span id="invoice-total">0 د.ج</span>
                    </div>
                </div>

                <div class="invoice-notes">
                    <h4>ملاحظات:</h4>
                    <p id="invoice-notes"></p>
                </div>

                <div class="form-buttons">
                    <button type="button" id="print-invoice-details" class="btn primary">طباعة الفاتورة</button>
                    <button type="button" id="edit-invoice" class="btn">تعديل</button>
                    <button type="button" id="close-invoice-details" class="btn">إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة البحث عن الزبون لطباعة الشهادة -->
    <div class="modal" id="customer-search-modal">
        <div class="modal-content large">
            <span class="close">&times;</span>
            <h2><i class="fas fa-search"></i> البحث عن زبون لطباعة الشهادة</h2>

            <div class="search-section">
                <div class="form-group">
                    <label for="customer-search-input">ابحث عن الزبون:</label>
                    <input type="text" id="customer-search-input" placeholder="أدخل اسم الزبون أو رقم الهاتف أو رقم السيارة...">
                </div>
                <button type="button" id="search-customers-btn" class="btn primary">
                    <i class="fas fa-search"></i> بحث
                </button>
            </div>

            <div class="search-results" id="customer-search-results">
                <h3>نتائج البحث:</h3>
                <div class="table-container">
                    <table id="customer-search-table">
                        <thead>
                            <tr>
                                <th>اسم الزبون</th>
                                <th>رقم الهاتف</th>
                                <th>رقم السيارة</th>
                                <th>نوع الخزان</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- سيتم ملؤها بواسطة JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="form-buttons">
                <button type="button" class="btn" id="cancel-customer-search">إغلاق</button>
            </div>
        </div>
    </div>

    <!-- نافذة طباعة الشهادة -->
    <div class="modal" id="certificate-print-modal">
        <div class="modal-content extra-large">
            <span class="close">&times;</span>
            <h2><i class="fas fa-certificate"></i> طباعة شهادة التركيب</h2>

            <div class="certificate-type-selection">
                <div class="form-group">
                    <label>نوع الشهادة:</label>
                    <div class="radio-group">
                        <label class="radio-label">
                            <input type="radio" name="certificate-type" value="installation" checked>
                            <span class="radio-custom"></span>
                            شهادة التركيب
                        </label>
                        <label class="radio-label">
                            <input type="radio" name="certificate-type" value="monitoring">
                            <span class="radio-custom"></span>
                            شهادة المراقبة الدورية
                        </label>
                    </div>
                </div>
            </div>

            <div class="certificate-preview" id="certificate-preview">
                <!-- سيتم إنشاء معاينة الشهادة هنا -->
            </div>

            <div class="form-buttons">
                <button type="button" id="print-certificate-btn" class="btn primary">
                    <i class="fas fa-print"></i> طباعة الشهادة
                </button>
                <button type="button" id="save-certificate-pdf" class="btn">
                    <i class="fas fa-file-pdf"></i> حفظ كـ PDF
                </button>
                <button type="button" class="btn" id="cancel-certificate-print">إغلاق</button>
            </div>
        </div>
    </div>

    <div class="toast" id="toast">
        <div class="toast-content">
            <i class="fas fa-check-circle toast-icon"></i>
            <div class="toast-message">تمت العملية بنجاح</div>
        </div>
        <div class="toast-progress"></div>
    </div>

    <!-- نظام الإشعارات الثاني (مخفي) -->
    <div class="notifications-container-backup hidden-element" id="notifications-container-backup">
        <div class="notifications-header">
            <h3>الإشعارات</h3>
            <button type="button" id="close-notifications-backup" class="btn" title="إغلاق"><i class="fas fa-times"></i></button>
        </div>
        <div class="notifications-list" id="notifications-list-backup">
            <!-- سيتم ملؤها بواسطة JavaScript -->
        </div>
    </div>

    <button type="button" id="notifications-btn-backup" class="floating-btn hidden-element" title="الإشعارات">
        <i class="fas fa-bell"></i>
        <span class="badge" id="notifications-count-backup">0</span>
    </button>



    <!-- لوحة الإشعارات -->
    <div id="notifications-container" class="notifications-container">
        <div class="notifications-header">
            <h3><i class="fas fa-bell"></i> الإشعارات</h3>
            <button type="button" id="close-notifications" class="btn" title="إغلاق">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div id="notifications-list" class="notifications-list">
            <!-- سيتم ملؤها بواسطة JavaScript -->
        </div>
        <div class="notifications-footer">
            <button type="button" id="mark-all-read" class="btn">تحديد الكل كمقروء</button>
            <button type="button" id="clear-notifications" class="btn">مسح الكل</button>
        </div>
    </div>

    <!-- أيقونة التنبيهات العائمة -->
    <button type="button" id="floating-notifications-btn" class="floating-notifications-btn" title="التنبيهات والإشعارات">
        <i class="fas fa-bell"></i>
        <span id="floating-notifications-badge" class="floating-badge hidden">0</span>
        <div class="floating-btn-ripple"></div>
    </button>

    <script src="scripts/script.js"></script>

    <!-- Service Worker Registration -->
    <script>
        // تسجيل Service Worker للـ PWA
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('./sw.js')
                    .then(registration => {
                        console.log('✅ Service Worker registered successfully:', registration.scope);

                        // التحقق من وجود تحديثات
                        registration.addEventListener('updatefound', () => {
                            const newWorker = registration.installing;
                            newWorker.addEventListener('statechange', () => {
                                if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                                    // يوجد تحديث جديد
                                    showUpdateNotification();
                                }
                            });
                        });
                    })
                    .catch(error => {
                        console.log('❌ Service Worker registration failed:', error);
                    });
            });
        }

        // إظهار إشعار التحديث
        function showUpdateNotification() {
            if (confirm('يتوفر تحديث جديد للتطبيق. هل تريد إعادة تحميل الصفحة لتطبيق التحديث؟')) {
                window.location.reload();
            }
        }

        // طلب إذن الإشعارات
        if ('Notification' in window && navigator.serviceWorker) {
            if (Notification.permission === 'default') {
                Notification.requestPermission().then(permission => {
                    if (permission === 'granted') {
                        console.log('✅ Notification permission granted');
                    }
                });
            }
        }

        // إضافة مستمع لحدث beforeinstallprompt
        let deferredPrompt;
        window.addEventListener('beforeinstallprompt', (e) => {
            // منع عرض النافذة التلقائية
            e.preventDefault();
            // حفظ الحدث للاستخدام لاحقاً
            deferredPrompt = e;
            // إظهار زر التثبيت المخصص
            showInstallButton();
        });

        // إظهار زر التثبيت
        function showInstallButton() {
            const installBtn = document.createElement('button');
            installBtn.textContent = '📱 تثبيت التطبيق';
            installBtn.className = 'install-btn';
            installBtn.style.cssText = `
                position: fixed;
                bottom: 20px;
                right: 20px;
                background: #4CAF50;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 25px;
                font-weight: bold;
                cursor: pointer;
                box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
                z-index: 10000;
                transition: all 0.3s ease;
            `;

            installBtn.addEventListener('click', async () => {
                if (deferredPrompt) {
                    // إظهار نافذة التثبيت
                    deferredPrompt.prompt();
                    // انتظار اختيار المستخدم
                    const { outcome } = await deferredPrompt.userChoice;
                    console.log(`User response to the install prompt: ${outcome}`);
                    // إعادة تعيين المتغير
                    deferredPrompt = null;
                    // إخفاء الزر
                    installBtn.remove();
                }
            });

            installBtn.addEventListener('mouseenter', () => {
                installBtn.style.transform = 'translateY(-2px)';
                installBtn.style.boxShadow = '0 6px 20px rgba(76, 175, 80, 0.4)';
            });

            installBtn.addEventListener('mouseleave', () => {
                installBtn.style.transform = 'translateY(0)';
                installBtn.style.boxShadow = '0 4px 15px rgba(76, 175, 80, 0.3)';
            });

            document.body.appendChild(installBtn);
        }

        // مراقبة حالة التثبيت
        window.addEventListener('appinstalled', () => {
            console.log('✅ PWA was installed');
            // إخفاء زر التثبيت إذا كان موجوداً
            const installBtn = document.querySelector('.install-btn');
            if (installBtn) {
                installBtn.remove();
            }
        });

        // مراقبة حالة الاتصال
        window.addEventListener('online', () => {
            console.log('🌐 Back online');
            showToast('تم استعادة الاتصال بالإنترنت', true);
        });

        window.addEventListener('offline', () => {
            console.log('📡 Gone offline');
            showToast('تم فقدان الاتصال بالإنترنت - التطبيق يعمل في الوضع غير المتصل', false);
        });
    </script>

    <!-- Enhanced JavaScript Modules -->
    <script src="scripts/security.js"></script>
    <script src="scripts/notifications.js"></script>
    <script src="scripts/updater.js"></script>
    <script src="scripts/reports.js"></script>
</body>
</html>
