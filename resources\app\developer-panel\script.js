// نظام إدارة لوحة تحكم المطور
class DeveloperPanel {
    constructor() {
        this.activationRequests = [];
        this.issuedLicenses = [];
        this.currentRequest = null;
        this.init();
    }

    init() {
        console.log('🔧 تهيئة لوحة تحكم المطور...');
        this.loadData();
        this.setupEventListeners();
        this.updateStats();
        this.renderRequests();
        this.renderLicenses();
    }

    setupEventListeners() {
        // التبويبات
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => this.switchTab(e.target.dataset.tab));
        });

        // أزرار الإجراءات
        document.getElementById('refresh-btn').addEventListener('click', () => this.refreshData());
        document.getElementById('export-requests-btn').addEventListener('click', () => this.exportRequests());
        document.getElementById('export-licenses-btn').addEventListener('click', () => this.exportLicenses());
        document.getElementById('clear-requests-btn').addEventListener('click', () => this.clearProcessedRequests());

        // مولد التراخيص
        document.getElementById('license-generator-form').addEventListener('submit', (e) => this.generateLicense(e));
        document.getElementById('license-duration').addEventListener('change', (e) => this.toggleCustomDuration(e));
        document.getElementById('copy-license-btn').addEventListener('click', () => this.copyLicense());
        document.getElementById('save-license-btn').addEventListener('click', () => this.saveLicense());

        // النوافذ المنبثقة
        document.getElementById('close-request-modal').addEventListener('click', () => this.closeModal());
        document.getElementById('close-modal-btn').addEventListener('click', () => this.closeModal());
        document.getElementById('approve-request-btn').addEventListener('click', () => this.approveRequest());
        document.getElementById('reject-request-btn').addEventListener('click', () => this.rejectRequest());

        // التصفية والبحث
        document.getElementById('status-filter').addEventListener('change', () => this.filterRequests());
        document.getElementById('search-requests').addEventListener('input', () => this.filterRequests());
    }

    loadData() {
        // تحميل طلبات التفعيل من localStorage
        const savedRequests = localStorage.getItem('developerPanel_activationRequests');
        if (savedRequests) {
            this.activationRequests = JSON.parse(savedRequests);
        }

        // تحميل التراخيص المُصدرة
        const savedLicenses = localStorage.getItem('developerPanel_issuedLicenses');
        if (savedLicenses) {
            this.issuedLicenses = JSON.parse(savedLicenses);
        }

        // محاكاة بيانات تجريبية إذا لم توجد بيانات
        if (this.activationRequests.length === 0) {
            this.generateSampleData();
        }
    }

    generateSampleData() {
        // إنشاء بيانات تجريبية للاختبار
        const sampleRequests = [
            {
                id: 'REQ-' + Date.now() + '-001',
                firstName: 'أحمد',
                lastName: 'محمد',
                phone: '**********',
                state: '01',
                municipality: 'أدرار',
                businessName: 'محطة الغاز الحديثة',
                deviceId: 'FFC-A1B2-C3D4-E5F6-G7H8',
                timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
                status: 'pending',
                notes: 'طلب تفعيل لمحطة غاز جديدة'
            },
            {
                id: 'REQ-' + Date.now() + '-002',
                firstName: 'فاطمة',
                lastName: 'علي',
                phone: '0666789012',
                state: '16',
                municipality: 'الجزائر',
                businessName: 'مؤسسة النور للغاز',
                deviceId: 'FFC-B2C3-D4E5-F6G7-H8I9',
                timestamp: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
                status: 'pending',
                notes: 'توسيع نشاط المؤسسة'
            }
        ];

        this.activationRequests = sampleRequests;
        this.saveData();
    }

    saveData() {
        localStorage.setItem('developerPanel_activationRequests', JSON.stringify(this.activationRequests));
        localStorage.setItem('developerPanel_issuedLicenses', JSON.stringify(this.issuedLicenses));
    }

    switchTab(tabName) {
        // إخفاء جميع التبويبات
        document.querySelectorAll('.tab-content').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });

        // إظهار التبويب المحدد
        document.getElementById(tabName + '-tab').classList.add('active');
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
    }

    updateStats() {
        const totalRequests = this.activationRequests.length;
        const pendingRequests = this.activationRequests.filter(req => req.status === 'pending').length;
        const activeLicenses = this.issuedLicenses.filter(license => license.status === 'active').length;

        document.getElementById('total-requests').textContent = totalRequests;
        document.getElementById('pending-requests-badge').textContent = pendingRequests;
        document.getElementById('active-licenses').textContent = activeLicenses;
    }

    renderRequests() {
        const tbody = document.querySelector('#requests-table tbody');
        tbody.innerHTML = '';

        this.activationRequests.forEach(request => {
            const row = this.createRequestRow(request);
            tbody.appendChild(row);
        });
    }

    createRequestRow(request) {
        const row = document.createElement('tr');
        const statusClass = `status-${request.status}`;
        const statusText = this.getStatusText(request.status);
        const date = new Date(request.timestamp).toLocaleDateString('ar-DZ');

        row.innerHTML = `
            <td>${request.id}</td>
            <td>${request.firstName} ${request.lastName}</td>
            <td>${request.phone}</td>
            <td>${request.municipality}</td>
            <td><code>${request.deviceId}</code></td>
            <td>${date}</td>
            <td><span class="${statusClass}">${statusText}</span></td>
            <td>
                <button class="btn btn-secondary" onclick="developerPanel.viewRequestDetails('${request.id}')">
                    <i class="fas fa-eye"></i> عرض
                </button>
                ${request.status === 'pending' ? `
                    <button class="btn btn-success" onclick="developerPanel.quickApprove('${request.id}')">
                        <i class="fas fa-check"></i> موافقة
                    </button>
                ` : ''}
            </td>
        `;

        return row;
    }

    getStatusText(status) {
        const statusMap = {
            'pending': 'في الانتظار',
            'approved': 'موافق عليه',
            'rejected': 'مرفوض'
        };
        return statusMap[status] || status;
    }

    viewRequestDetails(requestId) {
        const request = this.activationRequests.find(req => req.id === requestId);
        if (!request) return;

        this.currentRequest = request;
        this.showRequestModal(request);
    }

    showRequestModal(request) {
        const modal = document.getElementById('request-details-modal');
        const body = document.getElementById('request-details-body');

        body.innerHTML = `
            <div class="request-details">
                <div class="detail-group">
                    <h4>معلومات شخصية</h4>
                    <div class="detail-item">
                        <span class="detail-label">الاسم الكامل:</span>
                        <span class="detail-value">${request.firstName} ${request.lastName}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">رقم الهاتف:</span>
                        <span class="detail-value">${request.phone}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">الولاية:</span>
                        <span class="detail-value">${request.state}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">البلدية:</span>
                        <span class="detail-value">${request.municipality}</span>
                    </div>
                </div>
                
                <div class="detail-group">
                    <h4>معلومات تقنية</h4>
                    <div class="detail-item">
                        <span class="detail-label">معرف الجهاز:</span>
                        <span class="detail-value"><code>${request.deviceId}</code></span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">تاريخ الطلب:</span>
                        <span class="detail-value">${new Date(request.timestamp).toLocaleString('ar-DZ')}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">الحالة:</span>
                        <span class="detail-value status-${request.status}">${this.getStatusText(request.status)}</span>
                    </div>
                </div>
                
                <div class="detail-group" style="grid-column: 1 / -1;">
                    <h4>معلومات إضافية</h4>
                    <div class="detail-item">
                        <span class="detail-label">اسم المؤسسة:</span>
                        <span class="detail-value">${request.businessName || 'غير محدد'}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">ملاحظات:</span>
                        <span class="detail-value">${request.notes || 'لا توجد ملاحظات'}</span>
                    </div>
                </div>
            </div>
        `;

        modal.classList.add('show');
        modal.style.display = 'flex';
    }

    closeModal() {
        const modal = document.getElementById('request-details-modal');
        modal.classList.remove('show');
        modal.style.display = 'none';
        this.currentRequest = null;
    }

    quickApprove(requestId) {
        const request = this.activationRequests.find(req => req.id === requestId);
        if (!request) return;

        if (confirm(`هل أنت متأكد من الموافقة على طلب ${request.firstName} ${request.lastName}؟`)) {
            this.approveRequestById(requestId);
        }
    }

    approveRequest() {
        if (!this.currentRequest) return;
        this.approveRequestById(this.currentRequest.id);
        this.closeModal();
    }

    approveRequestById(requestId) {
        const request = this.activationRequests.find(req => req.id === requestId);
        if (!request) return;

        // تحديث حالة الطلب
        request.status = 'approved';
        request.approvedAt = new Date().toISOString();

        // إنشاء ترخيص تلقائياً
        const license = this.createLicenseForRequest(request);
        this.issuedLicenses.push(license);

        this.saveData();
        this.updateStats();
        this.renderRequests();
        this.renderLicenses();

        this.showAlert('success', `تم الموافقة على الطلب وإنشاء ترخيص للعميل ${request.firstName} ${request.lastName}`);
    }

    rejectRequest() {
        if (!this.currentRequest) return;

        const reason = prompt('سبب الرفض (اختياري):');
        
        this.currentRequest.status = 'rejected';
        this.currentRequest.rejectedAt = new Date().toISOString();
        this.currentRequest.rejectionReason = reason;

        this.saveData();
        this.updateStats();
        this.renderRequests();
        this.closeModal();

        this.showAlert('warning', `تم رفض طلب ${this.currentRequest.firstName} ${this.currentRequest.lastName}`);
    }

    createLicenseForRequest(request) {
        const licenseId = 'LIC-' + Date.now() + '-' + Math.random().toString(36).substr(2, 6).toUpperCase();
        const issuedAt = new Date();
        const expiryDate = new Date(issuedAt.getTime() + 365 * 24 * 60 * 60 * 1000); // سنة واحدة

        const licenseData = {
            deviceId: request.deviceId,
            type: 'FFC_LICENSE',
            issuedAt: issuedAt.toISOString(),
            expiryDate: expiryDate.toISOString(),
            clientName: `${request.firstName} ${request.lastName}`,
            businessName: request.businessName,
            licenseId: licenseId
        };

        const licenseKey = btoa(JSON.stringify(licenseData));

        return {
            id: licenseId,
            deviceId: request.deviceId,
            clientName: `${request.firstName} ${request.lastName}`,
            businessName: request.businessName,
            licenseKey: licenseKey,
            issuedAt: issuedAt.toISOString(),
            expiryDate: expiryDate.toISOString(),
            status: 'active',
            requestId: request.id
        };
    }

    showAlert(type, message) {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type}`;
        alertDiv.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
            ${message}
        `;

        document.querySelector('.main-content').insertBefore(alertDiv, document.querySelector('.main-content').firstChild);

        setTimeout(() => {
            alertDiv.remove();
        }, 5000);
    }

    refreshData() {
        this.loadData();
        this.updateStats();
        this.renderRequests();
        this.renderLicenses();
        this.showAlert('info', 'تم تحديث البيانات بنجاح');
    }

    renderLicenses() {
        const tbody = document.querySelector('#licenses-table tbody');
        tbody.innerHTML = '';

        this.issuedLicenses.forEach(license => {
            const row = this.createLicenseRow(license);
            tbody.appendChild(row);
        });
    }

    createLicenseRow(license) {
        const row = document.createElement('tr');
        const statusClass = `status-${license.status}`;
        const statusText = license.status === 'active' ? 'نشط' : 'منتهي';
        const issuedDate = new Date(license.issuedAt).toLocaleDateString('ar-DZ');
        const expiryDate = new Date(license.expiryDate).toLocaleDateString('ar-DZ');

        row.innerHTML = `
            <td>${license.id}</td>
            <td><code>${license.deviceId}</code></td>
            <td>${license.clientName}</td>
            <td>${issuedDate}</td>
            <td>${expiryDate}</td>
            <td><span class="${statusClass}">${statusText}</span></td>
            <td>
                <button class="btn btn-secondary" onclick="developerPanel.viewLicense('${license.id}')">
                    <i class="fas fa-eye"></i> عرض
                </button>
                <button class="btn btn-warning" onclick="developerPanel.copyLicenseKey('${license.id}')">
                    <i class="fas fa-copy"></i> نسخ المفتاح
                </button>
            </td>
        `;

        return row;
    }

    viewLicense(licenseId) {
        const license = this.issuedLicenses.find(lic => lic.id === licenseId);
        if (!license) return;

        alert(`ترخيص: ${license.id}\nالعميل: ${license.clientName}\nمعرف الجهاز: ${license.deviceId}\nتاريخ الإصدار: ${new Date(license.issuedAt).toLocaleDateString('ar-DZ')}\nتاريخ الانتهاء: ${new Date(license.expiryDate).toLocaleDateString('ar-DZ')}`);
    }

    copyLicenseKey(licenseId) {
        const license = this.issuedLicenses.find(lic => lic.id === licenseId);
        if (!license) return;

        navigator.clipboard.writeText(license.licenseKey).then(() => {
            this.showAlert('success', 'تم نسخ مفتاح الترخيص إلى الحافظة');
        }).catch(() => {
            this.showAlert('danger', 'فشل في نسخ مفتاح الترخيص');
        });
    }

    generateLicense(event) {
        event.preventDefault();

        const formData = new FormData(event.target);
        const clientName = formData.get('client-name');
        const deviceId = formData.get('device-id-input');
        const duration = formData.get('license-duration');
        const customDays = formData.get('custom-days');
        const notes = formData.get('license-notes');

        if (!clientName || !deviceId) {
            this.showAlert('danger', 'يرجى ملء جميع الحقول المطلوبة');
            return;
        }

        // التحقق من صيغة معرف الجهاز
        if (!/^FFC-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$/.test(deviceId)) {
            this.showAlert('danger', 'صيغة معرف الجهاز غير صحيحة');
            return;
        }

        const days = duration === 'custom' ? parseInt(customDays) : parseInt(duration);
        const licenseId = 'LIC-' + Date.now() + '-' + Math.random().toString(36).substr(2, 6).toUpperCase();
        const issuedAt = new Date();
        const expiryDate = new Date(issuedAt.getTime() + days * 24 * 60 * 60 * 1000);

        const licenseData = {
            deviceId: deviceId,
            type: 'FFC_LICENSE',
            issuedAt: issuedAt.toISOString(),
            expiryDate: expiryDate.toISOString(),
            clientName: clientName,
            licenseId: licenseId,
            notes: notes
        };

        const licenseKey = btoa(JSON.stringify(licenseData));

        // عرض الترخيص المُولد
        document.getElementById('license-output').value = licenseKey;
        document.getElementById('generated-license').style.display = 'block';

        // حفظ الترخيص في القائمة
        const license = {
            id: licenseId,
            deviceId: deviceId,
            clientName: clientName,
            licenseKey: licenseKey,
            issuedAt: issuedAt.toISOString(),
            expiryDate: expiryDate.toISOString(),
            status: 'active',
            notes: notes
        };

        this.issuedLicenses.push(license);
        this.saveData();
        this.updateStats();
        this.renderLicenses();

        this.showAlert('success', `تم إنشاء ترخيص جديد للعميل ${clientName}`);
    }

    toggleCustomDuration(event) {
        const customDurationDiv = document.getElementById('custom-duration');
        if (event.target.value === 'custom') {
            customDurationDiv.style.display = 'block';
        } else {
            customDurationDiv.style.display = 'none';
        }
    }

    copyLicense() {
        const licenseOutput = document.getElementById('license-output');
        navigator.clipboard.writeText(licenseOutput.value).then(() => {
            this.showAlert('success', 'تم نسخ الترخيص إلى الحافظة');
        }).catch(() => {
            this.showAlert('danger', 'فشل في نسخ الترخيص');
        });
    }

    saveLicense() {
        const licenseOutput = document.getElementById('license-output');
        const blob = new Blob([licenseOutput.value], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `license-${Date.now()}.txt`;
        a.click();
        URL.revokeObjectURL(url);
        this.showAlert('success', 'تم حفظ الترخيص كملف');
    }

    filterRequests() {
        const statusFilter = document.getElementById('status-filter').value;
        const searchTerm = document.getElementById('search-requests').value.toLowerCase();

        const filteredRequests = this.activationRequests.filter(request => {
            const matchesStatus = statusFilter === 'all' || request.status === statusFilter;
            const matchesSearch = searchTerm === '' ||
                request.firstName.toLowerCase().includes(searchTerm) ||
                request.lastName.toLowerCase().includes(searchTerm) ||
                request.phone.includes(searchTerm) ||
                request.deviceId.toLowerCase().includes(searchTerm);

            return matchesStatus && matchesSearch;
        });

        this.renderFilteredRequests(filteredRequests);
    }

    renderFilteredRequests(requests) {
        const tbody = document.querySelector('#requests-table tbody');
        tbody.innerHTML = '';

        requests.forEach(request => {
            const row = this.createRequestRow(request);
            tbody.appendChild(row);
        });
    }

    exportRequests() {
        const data = JSON.stringify(this.activationRequests, null, 2);
        const blob = new Blob([data], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `activation-requests-${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        URL.revokeObjectURL(url);
        this.showAlert('success', 'تم تصدير طلبات التفعيل');
    }

    exportLicenses() {
        const data = JSON.stringify(this.issuedLicenses, null, 2);
        const blob = new Blob([data], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `issued-licenses-${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        URL.revokeObjectURL(url);
        this.showAlert('success', 'تم تصدير التراخيص المُصدرة');
    }

    clearProcessedRequests() {
        if (confirm('هل أنت متأكد من حذف جميع الطلبات المعالجة (الموافق عليها والمرفوضة)؟')) {
            this.activationRequests = this.activationRequests.filter(req => req.status === 'pending');
            this.saveData();
            this.updateStats();
            this.renderRequests();
            this.showAlert('warning', 'تم حذف الطلبات المعالجة');
        }
    }
}

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.developerPanel = new DeveloperPanel();
});
