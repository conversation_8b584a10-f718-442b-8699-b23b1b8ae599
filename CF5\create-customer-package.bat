@echo off
chcp 65001 >nul
title إنشاء حزمة العميل - مؤسسة وقود المستقبل
color 0A

echo.
echo ================================================================================
echo                    إنشاء حزمة العميل - مؤسسة وقود المستقبل
echo                    Create Customer Package - Future Fuel Corporation
echo ================================================================================
echo.
echo هذا المعالج سيقوم بإنشاء حزمة تثبيت كاملة للعملاء
echo.

:: التحقق من وجود الملفات المطلوبة
echo 🔍 فحص الملفات المطلوبة...

if not exist "customer-package" (
    echo ❌ مجلد customer-package غير موجود
    pause
    exit /b 1
)

if not exist "customer-package\app\index.html" (
    echo ❌ ملفات التطبيق غير موجودة
    pause
    exit /b 1
)

echo ✓ جميع الملفات المطلوبة موجودة
echo.

:: إنشاء مجلدات إضافية
echo 📁 إنشاء المجلدات الإضافية...
if not exist "customer-package\data" mkdir "customer-package\data"
if not exist "customer-package\backup" mkdir "customer-package\backup"
if not exist "customer-package\logs" mkdir "customer-package\logs"
if not exist "customer-package\temp" mkdir "customer-package\temp"
if not exist "customer-package\updates" mkdir "customer-package\updates"
if not exist "customer-package\assets" mkdir "customer-package\assets"
echo ✓ تم إنشاء المجلدات

:: نسخ الأصول والأيقونات
echo 🎨 نسخ الأصول والأيقونات...
if exist "installer-package\app\assets" (
    xcopy /E /I /Y "installer-package\app\assets\*" "customer-package\assets\" >nul
    echo ✓ تم نسخ الأصول
) else (
    echo ⚠️ مجلد الأصول غير موجود
)

:: إنشاء ملف معلومات الحزمة
echo 📄 إنشاء ملف معلومات الحزمة...
(
echo {
echo   "packageName": "FutureFuel_Customer_Package",
echo   "version": "2.2.0",
echo   "buildDate": "%DATE% %TIME%",
echo   "packageType": "customer",
echo   "platform": "windows",
echo   "size": "calculating...",
echo   "files": {
echo     "total": 0,
echo     "executable": 0,
echo     "data": 0,
echo     "assets": 0
echo   },
echo   "installer": {
echo     "type": "batch",
echo     "requiresAdmin": true,
echo     "autoStart": true,
echo     "createShortcuts": true
echo   },
echo   "features": [
echo     "نظام إدارة شامل",
echo     "واجهة عربية احترافية",
echo     "نظام تراخيص آمن",
echo     "نسخ احتياطية تلقائية",
echo     "تحديثات تلقائية",
echo     "دعم فني شامل"
echo   ]
echo }
) > "customer-package\package-info.json"
echo ✓ تم إنشاء ملف معلومات الحزمة

:: إنشاء ملف تشغيل سريع
echo ⚡ إنشاء ملف التشغيل السريع...
(
echo @echo off
echo chcp 65001 ^>nul
echo title مؤسسة وقود المستقبل
echo.
echo echo مرحباً بك في نظام مؤسسة وقود المستقبل
echo echo جاري تشغيل النظام...
echo.
echo if exist "FutureFuel.vbs" ^(
echo     start "" "FutureFuel.vbs"
echo ^) else ^(
echo     start "" "app\index.html"
echo ^)
echo.
echo exit
) > "customer-package\تشغيل النظام.bat"
echo ✓ تم إنشاء ملف التشغيل السريع

:: إنشاء ملف معلومات سريعة
echo 📋 إنشاء ملف المعلومات السريعة...
(
echo ================================================================================
echo                    مؤسسة وقود المستقبل - معلومات سريعة
echo ================================================================================
echo.
echo 🚀 للتشغيل السريع:
echo    - انقر نقراً مزدوجاً على "تشغيل النظام.bat"
echo    - أو انقر على "QuickStart.bat" للخيارات المتقدمة
echo.
echo 🔑 تفعيل الترخيص:
echo    - شغل التطبيق وأدخل كود الترخيص
echo    - أو استخدم QuickStart.bat ^> خيار 3
echo.
echo 📞 الدعم الفني:
echo    - البريد: <EMAIL>
echo    - الهاتف: +966-11-123-4567
echo.
echo 📖 للمزيد من المعلومات:
echo    - اقرأ ملف README.txt
echo    - راجع ملف license.txt
echo.
echo ================================================================================
) > "customer-package\معلومات سريعة.txt"
echo ✓ تم إنشاء ملف المعلومات السريعة

:: حساب حجم الحزمة
echo 📊 حساب حجم الحزمة...
for /f "tokens=3" %%a in ('dir /s /-c "customer-package" ^| find "bytes"') do set PACKAGE_SIZE=%%a
set /a PACKAGE_SIZE_MB=%PACKAGE_SIZE%/1048576
echo ✓ حجم الحزمة: %PACKAGE_SIZE_MB% MB

:: إنشاء ملف فحص التكامل
echo 🔐 إنشاء ملف فحص التكامل...
(
echo # فحص تكامل حزمة العميل
echo # Package Integrity Check
echo.
echo تاريخ الإنشاء: %DATE% %TIME%
echo حجم الحزمة: %PACKAGE_SIZE_MB% MB
echo.
echo الملفات الأساسية:
dir /b "customer-package\*.bat" 2>nul
dir /b "customer-package\*.txt" 2>nul
dir /b "customer-package\*.json" 2>nul
dir /b "customer-package\*.vbs" 2>nul
echo.
echo مجلدات التطبيق:
dir /b "customer-package\app" 2>nul
dir /b "customer-package\assets" 2>nul
) > "customer-package\integrity-check.txt"
echo ✓ تم إنشاء ملف فحص التكامل

:: إنشاء ملف تشغيل تجريبي
echo 🧪 إنشاء ملف التشغيل التجريبي...
(
echo @echo off
echo chcp 65001 ^>nul
echo title مؤسسة وقود المستقبل - النسخة التجريبية
echo.
echo echo ================================================================================
echo echo                    مؤسسة وقود المستقبل - النسخة التجريبية
echo echo ================================================================================
echo echo.
echo echo مرحباً بك في النسخة التجريبية من نظام مؤسسة وقود المستقبل
echo echo.
echo echo ⏰ مدة التجربة: 30 يوماً
echo echo 🎯 جميع الميزات متاحة
echo echo 📞 دعم فني محدود
echo echo.
echo echo للحصول على الترخيص الكامل، يرجى التواصل معنا:
echo echo 📧 <EMAIL>
echo echo 📞 +966-11-123-4567
echo echo.
echo pause
echo.
echo start "" "app\index.html"
) > "customer-package\تجربة النظام.bat"
echo ✓ تم إنشاء ملف التشغيل التجريبي

echo.
echo ================================================================================
echo                              اكتمل إنشاء الحزمة
echo ================================================================================
echo.
echo 🎉 تم إنشاء حزمة العميل بنجاح!
echo.
echo 📁 مجلد الحزمة: customer-package\
echo 📊 حجم الحزمة: %PACKAGE_SIZE_MB% MB
echo 📄 عدد الملفات: جاري الحساب...
echo.
echo ================================================================================
echo                              محتويات الحزمة
echo ================================================================================
echo.
echo 📋 ملفات التثبيت:
echo    ✓ install.bat - معالج التثبيت الرئيسي
echo    ✓ uninstall.bat - معالج إلغاء التثبيت
echo    ✓ QuickStart.bat - البدء السريع
echo.
echo 🚀 ملفات التشغيل:
echo    ✓ FutureFuel.bat - تشغيل عادي
echo    ✓ FutureFuel.vbs - تشغيل مخفي
echo    ✓ تشغيل النظام.bat - تشغيل سريع
echo    ✓ تجربة النظام.bat - نسخة تجريبية
echo.
echo ⚙️ ملفات الإعداد:
echo    ✓ config.json - إعدادات النظام
echo    ✓ version.json - معلومات الإصدار
echo    ✓ package-info.json - معلومات الحزمة
echo.
echo 📖 ملفات التوثيق:
echo    ✓ README.txt - دليل المستخدم
echo    ✓ license.txt - اتفاقية الترخيص
echo    ✓ معلومات سريعة.txt - معلومات سريعة
echo.
echo 📁 مجلدات التطبيق:
echo    ✓ app\ - ملفات التطبيق الرئيسية
echo    ✓ assets\ - الصور والأيقونات
echo    ✓ data\ - قاعدة البيانات
echo    ✓ backup\ - النسخ الاحتياطية
echo    ✓ logs\ - ملفات السجلات
echo.
echo ================================================================================
echo.

set /p "CREATE_ZIP=هل تريد إنشاء ملف مضغوط للحزمة؟ (Y/N): "
if /i "%CREATE_ZIP%"=="Y" (
    echo.
    echo 📦 إنشاء ملف مضغوط...
    echo يرجى استخدام برنامج ضغط خارجي لإنشاء ملف ZIP
    echo المجلد المطلوب ضغطه: customer-package\
    echo اسم الملف المقترح: FutureFuel_v2.2.0_Customer_Package.zip
    echo.
    explorer "customer-package"
)

echo.
echo 🎯 الحزمة جاهزة للتوزيع على العملاء!
echo.
echo ملاحظات مهمة:
echo • تأكد من اختبار الحزمة قبل التوزيع
echo • قم بإنشاء أكواد تراخيص للعملاء
echo • وفر الدعم الفني للعملاء عند الحاجة
echo.
echo ================================================================================
echo.
pause
