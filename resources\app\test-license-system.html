<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام الترخيص</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-section h3 {
            color: #2c3e50;
            margin-top: 0;
        }
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #2980b9;
        }
        .success {
            background: #2ecc71;
        }
        .danger {
            background: #e74c3c;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            background: #ecf0f1;
        }
        .device-id {
            font-family: monospace;
            background: #34495e;
            color: white;
            padding: 10px;
            border-radius: 5px;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار نظام الترخيص</h1>
        <p>هذه الصفحة لاختبار جميع وظائف نظام الترخيص</p>

        <!-- اختبار معرف الجهاز -->
        <div class="test-section">
            <h3>1. اختبار معرف الجهاز</h3>
            <button onclick="generateDeviceId()">توليد معرف جهاز</button>
            <button onclick="showCurrentDeviceId()">عرض المعرف الحالي</button>
            <button onclick="clearDeviceId()" class="danger">مسح المعرف</button>
            <div id="device-result" class="result" style="display: none;"></div>
        </div>

        <!-- اختبار طلب التفعيل -->
        <div class="test-section">
            <h3>2. اختبار طلب التفعيل</h3>
            <button onclick="createTestRequest()">إنشاء طلب تفعيل تجريبي</button>
            <button onclick="showAllRequests()">عرض جميع الطلبات</button>
            <button onclick="clearAllRequests()" class="danger">مسح جميع الطلبات</button>
            <div id="request-result" class="result" style="display: none;"></div>
        </div>

        <!-- اختبار الترخيص -->
        <div class="test-section">
            <h3>3. اختبار الترخيص</h3>
            <button onclick="generateTestLicense()" class="success">توليد ترخيص تجريبي</button>
            <button onclick="validateCurrentLicense()">فحص الترخيص الحالي</button>
            <button onclick="clearLicense()" class="danger">مسح الترخيص</button>
            <div id="license-result" class="result" style="display: none;"></div>
        </div>

        <!-- اختبار لوحة التحكم -->
        <div class="test-section">
            <h3>4. اختبار لوحة التحكم</h3>
            <button onclick="openDeveloperPanel()">فتح لوحة التحكم</button>
            <button onclick="syncData()">مزامنة البيانات</button>
            <div id="panel-result" class="result" style="display: none;"></div>
        </div>

        <!-- معلومات النظام -->
        <div class="test-section">
            <h3>5. معلومات النظام</h3>
            <button onclick="showSystemInfo()">عرض معلومات النظام</button>
            <button onclick="exportAllData()" class="success">تصدير جميع البيانات</button>
            <button onclick="resetSystem()" class="danger">إعادة تعيين النظام</button>
            <div id="system-result" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        // وظائف اختبار معرف الجهاز
        function generateDeviceId() {
            const timestamp = Date.now();
            const random = Math.random().toString(36).substr(2, 9);
            const hash = simpleHash(`${timestamp}-${random}-${navigator.userAgent}`);
            const deviceId = `FFC-${hash.substr(0, 4)}-${hash.substr(4, 4)}-${hash.substr(8, 4)}-${hash.substr(12, 4)}`.toUpperCase();
            
            localStorage.setItem('deviceId', deviceId);
            showResult('device-result', `تم توليد معرف جديد: <div class="device-id">${deviceId}</div>`);
        }

        function showCurrentDeviceId() {
            const deviceId = localStorage.getItem('deviceId');
            if (deviceId) {
                showResult('device-result', `المعرف الحالي: <div class="device-id">${deviceId}</div>`);
            } else {
                showResult('device-result', 'لا يوجد معرف محفوظ');
            }
        }

        function clearDeviceId() {
            localStorage.removeItem('deviceId');
            showResult('device-result', 'تم مسح معرف الجهاز');
        }

        // وظائف اختبار طلب التفعيل
        function createTestRequest() {
            const deviceId = localStorage.getItem('deviceId') || 'FFC-TEST-TEST-TEST-TEST';
            const request = {
                id: 'REQ-' + Date.now(),
                firstName: 'أحمد',
                lastName: 'محمد',
                phone: '0555123456',
                state: '16',
                municipality: 'الجزائر',
                businessName: 'محطة الاختبار',
                deviceId: deviceId,
                timestamp: new Date().toISOString(),
                status: 'pending',
                notes: 'طلب تفعيل تجريبي'
            };

            const requests = JSON.parse(localStorage.getItem('developerPanel_activationRequests') || '[]');
            requests.push(request);
            localStorage.setItem('developerPanel_activationRequests', JSON.stringify(requests));
            
            showResult('request-result', `تم إنشاء طلب تفعيل: ${request.id}`);
        }

        function showAllRequests() {
            const requests = JSON.parse(localStorage.getItem('developerPanel_activationRequests') || '[]');
            showResult('request-result', `عدد الطلبات: ${requests.length}<br>${requests.map(r => `- ${r.id} (${r.status})`).join('<br>')}`);
        }

        function clearAllRequests() {
            localStorage.removeItem('developerPanel_activationRequests');
            showResult('request-result', 'تم مسح جميع الطلبات');
        }

        // وظائف اختبار الترخيص
        function generateTestLicense() {
            const deviceId = localStorage.getItem('deviceId') || 'FFC-TEST-TEST-TEST-TEST';
            const licenseData = {
                deviceId: deviceId,
                type: 'FFC_LICENSE',
                issuedAt: new Date().toISOString(),
                expiryDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
                clientName: 'عميل تجريبي',
                licenseId: 'LIC-TEST-' + Date.now()
            };

            const licenseKey = btoa(JSON.stringify(licenseData));
            const license = {
                key: licenseKey,
                deviceId: deviceId,
                activatedAt: new Date().toISOString(),
                expiryDate: licenseData.expiryDate,
                status: 'active'
            };

            localStorage.setItem('appLicense', JSON.stringify(license));
            showResult('license-result', `تم إنشاء ترخيص تجريبي:<br><textarea style="width:100%;height:100px;">${licenseKey}</textarea>`);
        }

        function validateCurrentLicense() {
            const license = localStorage.getItem('appLicense');
            if (license) {
                try {
                    const licenseData = JSON.parse(license);
                    const expiryDate = new Date(licenseData.expiryDate);
                    const isValid = new Date() < expiryDate && licenseData.status === 'active';
                    showResult('license-result', `الترخيص ${isValid ? 'صالح' : 'غير صالح'}<br>ينتهي في: ${expiryDate.toLocaleDateString('ar-DZ')}`);
                } catch (e) {
                    showResult('license-result', 'ترخيص تالف');
                }
            } else {
                showResult('license-result', 'لا يوجد ترخيص');
            }
        }

        function clearLicense() {
            localStorage.removeItem('appLicense');
            showResult('license-result', 'تم مسح الترخيص');
        }

        // وظائف لوحة التحكم
        function openDeveloperPanel() {
            window.open('developer-panel/index.html', '_blank');
            showResult('panel-result', 'تم فتح لوحة التحكم في نافذة جديدة');
        }

        function syncData() {
            // محاكاة مزامنة البيانات
            showResult('panel-result', 'تم مزامنة البيانات بنجاح');
        }

        // معلومات النظام
        function showSystemInfo() {
            const info = {
                deviceId: localStorage.getItem('deviceId'),
                hasLicense: !!localStorage.getItem('appLicense'),
                requestsCount: JSON.parse(localStorage.getItem('developerPanel_activationRequests') || '[]').length,
                licensesCount: JSON.parse(localStorage.getItem('developerPanel_issuedLicenses') || '[]').length,
                userAgent: navigator.userAgent,
                timestamp: new Date().toLocaleString('ar-DZ')
            };
            
            showResult('system-result', `
                <strong>معلومات النظام:</strong><br>
                معرف الجهاز: ${info.deviceId || 'غير موجود'}<br>
                يوجد ترخيص: ${info.hasLicense ? 'نعم' : 'لا'}<br>
                عدد الطلبات: ${info.requestsCount}<br>
                عدد التراخيص: ${info.licensesCount}<br>
                الوقت: ${info.timestamp}
            `);
        }

        function exportAllData() {
            const data = {
                deviceId: localStorage.getItem('deviceId'),
                license: localStorage.getItem('appLicense'),
                requests: localStorage.getItem('developerPanel_activationRequests'),
                licenses: localStorage.getItem('developerPanel_issuedLicenses'),
                exportDate: new Date().toISOString()
            };
            
            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `license-system-backup-${Date.now()}.json`;
            a.click();
            URL.revokeObjectURL(url);
            
            showResult('system-result', 'تم تصدير جميع البيانات');
        }

        function resetSystem() {
            if (confirm('هل أنت متأكد من إعادة تعيين النظام؟ سيتم حذف جميع البيانات!')) {
                localStorage.removeItem('deviceId');
                localStorage.removeItem('appLicense');
                localStorage.removeItem('developerPanel_activationRequests');
                localStorage.removeItem('developerPanel_issuedLicenses');
                showResult('system-result', 'تم إعادة تعيين النظام بالكامل');
            }
        }

        // وظائف مساعدة
        function showResult(elementId, message) {
            const element = document.getElementById(elementId);
            element.innerHTML = message;
            element.style.display = 'block';
        }

        function simpleHash(str) {
            let hash = 0;
            for (let i = 0; i < str.length; i++) {
                const char = str.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash;
            }
            return Math.abs(hash).toString(16).padStart(16, '0');
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🧪 صفحة اختبار نظام الترخيص جاهزة');
        });
    </script>
</body>
</html>
