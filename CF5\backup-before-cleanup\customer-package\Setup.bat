@echo off
title Future Fuel Corporation - Smart Installer
color 0A
mode con: cols=80 lines=30

:: تعيين الترميز
chcp 65001 >nul 2>&1

:: التحقق من صلاحيات المدير
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo.
    echo ================================================================================
    echo                    ADMINISTRATOR PRIVILEGES REQUIRED
    echo                    مطلوب صلاحيات المدير
    echo ================================================================================
    echo.
    echo This installer requires administrator privileges to install properly.
    echo يتطلب هذا المثبت صلاحيات المدير للتثبيت بشكل صحيح.
    echo.
    echo Please right-click on this file and select "Run as administrator"
    echo يرجى النقر بالزر الأيمن على هذا الملف واختيار "تشغيل كمسؤول"
    echo.
    echo ================================================================================
    pause
    exit /b 1
)

cls
echo.
echo                ████████╗██╗   ██╗████████╗██╗   ██╗██████╗ ███████╗
echo                ██╔══██║██║   ██║╚══██╔══╝██║   ██║██╔══██╗██╔════╝
echo                ██████╔╝██║   ██║   ██║   ██║   ██║██████╔╝█████╗  
echo                ██╔══██╗██║   ██║   ██║   ██║   ██║██╔══██╗██╔══╝  
echo                ██║  ██║╚██████╔╝   ██║   ╚██████╔╝██║  ██║███████╗
echo                ╚═╝  ╚═╝ ╚═════╝    ╚═╝    ╚═════╝ ╚═╝  ╚═╝╚══════╝
echo.
echo                ███████╗██╗   ██╗███████╗██╗         
echo                ██╔════╝██║   ██║██╔════╝██║         
echo                █████╗  ██║   ██║█████╗  ██║         
echo                ██╔══╝  ██║   ██║██╔══╝  ██║         
echo                ██║     ╚██████╔╝███████╗███████╗    
echo                ╚═╝      ╚═════╝ ╚══════╝╚══════╝    
echo.
echo ================================================================================
echo                    FUTURE FUEL CORPORATION - SMART INSTALLER
echo                    مؤسسة وقود المستقبل - المثبت الذكي
echo ================================================================================
echo.
echo                           🚀 Version 2.2.0 Professional
echo                           📅 Build Date: December 19, 2024
echo                           🏢 Future Fuel Corporation
echo.
echo ================================================================================

timeout /t 3 /nobreak >nul

cls
echo.
echo ================================================================================
echo                              SYSTEM CHECK
echo                              فحص النظام
echo ================================================================================
echo.

:: فحص نظام التشغيل
echo [1/5] Checking Operating System... فحص نظام التشغيل
for /f "tokens=4-5 delims=. " %%i in ('ver') do set VERSION=%%i.%%j
echo      ✓ Windows %VERSION% - Compatible متوافق
echo.

:: فحص المساحة المتاحة
echo [2/5] Checking Disk Space... فحص مساحة القرص
for /f "tokens=3" %%a in ('dir /-c %SystemDrive%\ ^| find "bytes free"') do set FREESPACE=%%a
set /a FREESPACE_MB=%FREESPACE%/1048576
if %FREESPACE_MB% LSS 100 (
    echo      ❌ Insufficient disk space. Required: 100MB, Available: %FREESPACE_MB%MB
    echo      ❌ مساحة القرص غير كافية. مطلوب: 100 ميجا، متاح: %FREESPACE_MB% ميجا
    pause
    exit /b 1
) else (
    echo      ✓ Disk Space: %FREESPACE_MB%MB Available كافية
)
echo.

:: فحص الذاكرة
echo [3/5] Checking Memory... فحص الذاكرة
for /f "skip=1" %%p in ('wmic computersystem get TotalPhysicalMemory') do (
    set MEMORY=%%p
    goto :memory_done
)
:memory_done
set /a MEMORY_GB=%MEMORY%/1073741824
echo      ✓ RAM: %MEMORY_GB%GB Available متاحة
echo.

:: فحص المتصفح
echo [4/5] Checking Browser... فحص المتصفح
where chrome >nul 2>&1
if %errorLevel% equ 0 (
    echo      ✓ Google Chrome - Found موجود
    set BROWSER=chrome
    goto browser_found
)
where firefox >nul 2>&1
if %errorLevel% equ 0 (
    echo      ✓ Mozilla Firefox - Found موجود
    set BROWSER=firefox
    goto browser_found
)
where msedge >nul 2>&1
if %errorLevel% equ 0 (
    echo      ✓ Microsoft Edge - Found موجود
    set BROWSER=msedge
    goto browser_found
)
echo      ⚠️ No modern browser found. System will use default.
echo      ⚠️ لم يتم العثور على متصفح حديث. سيتم استخدام الافتراضي.
set BROWSER=default

:browser_found
echo.

:: فحص الملفات
echo [5/5] Checking Installation Files... فحص ملفات التثبيت
if not exist "app\index.html" (
    echo      ❌ Application files missing ملفات التطبيق مفقودة
    pause
    exit /b 1
)
echo      ✓ All files present جميع الملفات موجودة
echo.

echo ================================================================================
echo                              SYSTEM READY
echo                              النظام جاهز
echo ================================================================================
echo.
echo System Requirements Check: ✓ PASSED
echo فحص متطلبات النظام: ✓ نجح
echo.
timeout /t 2 /nobreak >nul

:: اختيار مجلد التثبيت
cls
echo.
echo ================================================================================
echo                              INSTALLATION PATH
echo                              مسار التثبيت
echo ================================================================================
echo.

set "INSTALL_DIR=C:\FutureFuel"
echo Default installation path: %INSTALL_DIR%
echo مسار التثبيت الافتراضي: %INSTALL_DIR%
echo.
echo Press ENTER to use default path, or type a custom path:
echo اضغط ENTER لاستخدام المسار الافتراضي، أو اكتب مساراً مخصصاً:
echo.
set /p "CUSTOM_DIR=Custom Path مسار مخصص: "

if not "%CUSTOM_DIR%"=="" set "INSTALL_DIR=%CUSTOM_DIR%"

echo.
echo Installation will proceed to: %INSTALL_DIR%
echo سيتم التثبيت في: %INSTALL_DIR%
echo.
echo Press any key to continue or Ctrl+C to cancel...
echo اضغط أي مفتاح للمتابعة أو Ctrl+C للإلغاء...
pause >nul

:: بدء التثبيت
cls
echo.
echo ================================================================================
echo                              INSTALLING
echo                              جاري التثبيت
echo ================================================================================
echo.

echo [1/8] Creating directories... إنشاء المجلدات
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"
if not exist "%INSTALL_DIR%\app" mkdir "%INSTALL_DIR%\app"
if not exist "%INSTALL_DIR%\assets" mkdir "%INSTALL_DIR%\assets"
if not exist "%INSTALL_DIR%\data" mkdir "%INSTALL_DIR%\data"
if not exist "%INSTALL_DIR%\backup" mkdir "%INSTALL_DIR%\backup"
if not exist "%INSTALL_DIR%\logs" mkdir "%INSTALL_DIR%\logs"
if not exist "%INSTALL_DIR%\temp" mkdir "%INSTALL_DIR%\temp"
if not exist "%INSTALL_DIR%\updates" mkdir "%INSTALL_DIR%\updates"
echo      ✓ Directories created المجلدات تم إنشاؤها
echo.

echo [2/8] Copying application files... نسخ ملفات التطبيق
xcopy /E /I /Y "app\*" "%INSTALL_DIR%\app\" >nul
echo      ✓ Application files copied ملفات التطبيق تم نسخها
echo.

echo [3/8] Copying assets... نسخ الأصول
xcopy /E /I /Y "assets\*" "%INSTALL_DIR%\assets\" >nul 2>&1
echo      ✓ Assets copied الأصول تم نسخها
echo.

echo [4/8] Installing configuration... تثبيت الإعدادات
copy /Y "config.json" "%INSTALL_DIR%\" >nul
copy /Y "version.json" "%INSTALL_DIR%\" >nul
copy /Y "license.txt" "%INSTALL_DIR%\" >nul
copy /Y "README.txt" "%INSTALL_DIR%\" >nul
echo      ✓ Configuration installed الإعدادات تم تثبيتها
echo.

echo [5/8] Creating launcher... إنشاء المشغل
(
echo @echo off
echo chcp 65001 ^>nul
echo title Future Fuel Corporation
echo cd /d "%INSTALL_DIR%"
echo if exist "temp\app.lock" ^(
echo     echo Application is already running التطبيق يعمل بالفعل
echo     timeout /t 3 /nobreak ^>nul
echo     exit /b 0
echo ^)
echo echo %date% %time% ^> temp\app.lock
echo start "" "app\index.html"
echo timeout /t 30 /nobreak ^>nul
echo if exist "temp\app.lock" del "temp\app.lock" ^>nul 2^>^&1
) > "%INSTALL_DIR%\FutureFuel.bat"
echo      ✓ Launcher created المشغل تم إنشاؤه
echo.

echo [6/8] Creating desktop shortcut... إنشاء اختصار سطح المكتب
set "DESKTOP=%USERPROFILE%\Desktop"
(
echo [InternetShortcut]
echo URL=file:///%INSTALL_DIR:\=/%/app/index.html
echo IconFile=%INSTALL_DIR%\assets\icon.ico
echo IconIndex=0
echo HotKey=0
echo IDList=
echo [{000214A0-0000-0000-C000-000000000046}]
echo Prop3=19,11
) > "%DESKTOP%\Future Fuel Corporation.url"
echo      ✓ Desktop shortcut created اختصار سطح المكتب تم إنشاؤه
echo.

echo [7/8] Registering application... تسجيل التطبيق
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FutureFuel" /v "DisplayName" /t REG_SZ /d "Future Fuel Corporation" /f >nul
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FutureFuel" /v "DisplayVersion" /t REG_SZ /d "2.2.0" /f >nul
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FutureFuel" /v "Publisher" /t REG_SZ /d "Future Fuel Corporation" /f >nul
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FutureFuel" /v "InstallLocation" /t REG_SZ /d "%INSTALL_DIR%" /f >nul
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FutureFuel" /v "UninstallString" /t REG_SZ /d "%INSTALL_DIR%\uninstall.bat" /f >nul
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FutureFuel" /v "DisplayIcon" /t REG_SZ /d "%INSTALL_DIR%\assets\icon.ico" /f >nul
echo      ✓ Application registered التطبيق تم تسجيله
echo.

echo [8/8] Finalizing installation... إنهاء التثبيت
copy /Y "uninstall.bat" "%INSTALL_DIR%\" >nul
(
echo {
echo   "installDate": "%DATE% %TIME%",
echo   "installPath": "%INSTALL_DIR%",
echo   "version": "2.2.0",
echo   "computerName": "%COMPUTERNAME%",
echo   "userName": "%USERNAME%",
echo   "installId": "%RANDOM%%RANDOM%"
echo }
) > "%INSTALL_DIR%\install-info.json"
echo      ✓ Installation completed التثبيت اكتمل
echo.

cls
echo.
echo                ██╗███╗   ██╗███████╗████████╗ █████╗ ██╗     ██╗     ███████╗██████╗ 
echo                ██║████╗  ██║██╔════╝╚══██╔══╝██╔══██╗██║     ██║     ██╔════╝██╔══██╗
echo                ██║██╔██╗ ██║███████╗   ██║   ███████║██║     ██║     █████╗  ██║  ██║
echo                ██║██║╚██╗██║╚════██║   ██║   ██╔══██║██║     ██║     ██╔══╝  ██║  ██║
echo                ██║██║ ╚████║███████║   ██║   ██║  ██║███████╗███████╗███████╗██████╔╝
echo                ╚═╝╚═╝  ╚═══╝╚══════╝   ╚═╝   ╚═╝  ╚═╝╚══════╝╚══════╝╚══════╝╚═════╝ 
echo.
echo ================================================================================
echo                              INSTALLATION COMPLETE
echo                              اكتمل التثبيت
echo ================================================================================
echo.
echo ✅ Future Fuel Corporation has been successfully installed!
echo ✅ تم تثبيت مؤسسة وقود المستقبل بنجاح!
echo.
echo 📍 Installation Path: %INSTALL_DIR%
echo 📍 مسار التثبيت: %INSTALL_DIR%
echo.
echo 🖥️ Desktop shortcut: "Future Fuel Corporation"
echo 🖥️ اختصار سطح المكتب: "Future Fuel Corporation"
echo.
echo ================================================================================
echo                              NEXT STEPS
echo                              الخطوات التالية
echo ================================================================================
echo.
echo 1. Double-click the desktop shortcut to launch the application
echo    انقر نقراً مزدوجاً على اختصار سطح المكتب لتشغيل التطبيق
echo.
echo 2. Enter your license code when prompted
echo    أدخل كود الترخيص عند الطلب
echo.
echo 3. Start managing your fuel business!
echo    ابدأ في إدارة أعمال الوقود الخاصة بك!
echo.
echo ================================================================================
echo                              SUPPORT
echo                              الدعم
echo ================================================================================
echo.
echo 📧 Email: <EMAIL>
echo 📞 Phone: +966-11-123-4567
echo 🌐 Website: www.futurefuel.com
echo.
echo Working Hours: Sunday-Thursday 8AM-6PM, Friday-Saturday 9AM-3PM
echo ساعات العمل: الأحد-الخميس 8ص-6م، الجمعة-السبت 9ص-3م
echo.
echo ================================================================================
echo.

set /p "LAUNCH=Launch application now? تشغيل التطبيق الآن؟ (Y/N): "
if /i "%LAUNCH%"=="Y" (
    echo.
    echo 🚀 Launching Future Fuel Corporation...
    echo 🚀 تشغيل مؤسسة وقود المستقبل...
    start "" "%INSTALL_DIR%\app\index.html"
)

echo.
echo Thank you for choosing Future Fuel Corporation!
echo شكراً لاختيارك مؤسسة وقود المستقبل!
echo.
pause
