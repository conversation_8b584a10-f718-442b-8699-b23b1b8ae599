<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تسجيل الدخول</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        button {
            background: #2196f3;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
        }
        button:hover {
            background: #1976d2;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        .success {
            background: #e8f5e8;
            color: #2e7d32;
            border: 1px solid #4caf50;
        }
        .error {
            background: #ffebee;
            color: #c62828;
            border: 1px solid #f44336;
        }
        .info {
            background: #e3f2fd;
            color: #1976d2;
            border: 1px solid #2196f3;
        }
        .device-id {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            margin: 10px 0;
            border: 1px solid #ddd;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار تسجيل الدخول</h1>
        
        <div class="form-group">
            <label>معرف الجهاز:</label>
            <div class="device-id" id="deviceId">جاري التحميل...</div>
        </div>
        
        <form id="testForm">
            <div class="form-group">
                <label for="licenseCode">كود الترخيص:</label>
                <input type="text" id="licenseCode" placeholder="XXXX-XXXX-XXXX-XXXX" maxlength="19">
            </div>
            
            <button type="submit">اختبار تسجيل الدخول</button>
        </form>
        
        <div id="result" class="result"></div>
        
        <div style="margin-top: 30px;">
            <h3>🔑 تراخيص للاختبار:</h3>
            <ul>
                <li><strong>تجريبي:</strong> DEMO-2024-TEST-0001</li>
                <li><strong>سنوي:</strong> FULL-2024-PROD-0001</li>
                <li><strong>إداري:</strong> ADMIN-2024-CTRL-0001</li>
            </ul>
        </div>
        
        <div style="margin-top: 20px;">
            <button type="button" onclick="testDashboard()">اختبار فتح dashboard.html مباشرة</button>
        </div>
    </div>

    <script src="database-sync.js"></script>
    <script>
        // توليد معرف جهاز فريد
        function generateDeviceId() {
            const timestamp = Date.now().toString(36);
            const random = Math.random().toString(36).substr(2, 9);
            return `DEV-${timestamp}-${random}`.toUpperCase();
        }

        // تحديث معرف الجهاز
        function updateDeviceId() {
            let deviceId = localStorage.getItem('deviceId');
            if (!deviceId) {
                deviceId = generateDeviceId();
                localStorage.setItem('deviceId', deviceId);
            }
            document.getElementById('deviceId').textContent = deviceId;
            return deviceId;
        }

        // الحصول على التراخيص الصالحة
        function getValidLicenses() {
            const stored = localStorage.getItem('validLicenses');
            if (stored) {
                return JSON.parse(stored);
            }

            // إنشاء تراخيص افتراضية
            const defaultLicenses = [
                {
                    code: 'DEMO-2024-TEST-0001',
                    type: 'demo',
                    expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
                    isActive: true,
                    deviceId: null,
                    activatedAt: null,
                    createdAt: new Date().toISOString()
                },
                {
                    code: 'FULL-2024-PROD-0001',
                    type: 'yearly',
                    expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
                    isActive: true,
                    deviceId: null,
                    activatedAt: null,
                    createdAt: new Date().toISOString()
                },
                {
                    code: 'ADMIN-2024-CTRL-0001',
                    type: 'admin',
                    expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
                    isActive: true,
                    deviceId: null,
                    activatedAt: null,
                    createdAt: new Date().toISOString()
                }
            ];

            localStorage.setItem('validLicenses', JSON.stringify(defaultLicenses));
            return defaultLicenses;
        }

        // التحقق من صحة الترخيص
        function validateLicense(licenseCode, deviceId) {
            console.log('🔍 التحقق من الترخيص:', licenseCode);
            
            const licenses = getValidLicenses();
            console.log('📋 التراخيص المتاحة:', licenses);

            const license = licenses.find(l => l.code === licenseCode);
            console.log('🎫 الترخيص الموجود:', license);

            if (!license) {
                return { valid: false, message: 'كود الترخيص غير صحيح' };
            }

            if (!license.isActive) {
                return { valid: false, message: 'هذا الترخيص معطل' };
            }

            const now = new Date();
            const expiryDate = new Date(license.expiresAt);

            if (now > expiryDate) {
                return { valid: false, message: 'انتهت صلاحية هذا الترخيص' };
            }

            if (license.deviceId && license.deviceId !== deviceId) {
                return { valid: false, message: 'هذا الترخيص مرتبط بجهاز آخر' };
            }

            return {
                valid: true,
                license: license,
                message: 'تم التحقق من الترخيص بنجاح'
            };
        }

        // عرض النتيجة
        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = message;
            resultDiv.style.display = 'block';
        }

        // اختبار فتح dashboard مباشرة
        function testDashboard() {
            console.log('🧪 اختبار فتح dashboard.html مباشرة...');
            showResult('🔄 محاولة فتح dashboard.html...', 'info');
            
            try {
                window.location.href = 'dashboard.html';
            } catch (error) {
                console.error('❌ خطأ في فتح dashboard:', error);
                showResult(`❌ خطأ في فتح dashboard: ${error.message}`, 'error');
            }
        }

        // معالجة النموذج
        document.getElementById('testForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const licenseCode = document.getElementById('licenseCode').value.trim();
            const deviceId = document.getElementById('deviceId').textContent;
            
            console.log('🚀 بدء اختبار تسجيل الدخول...');
            console.log('📝 كود الترخيص:', licenseCode);
            console.log('📱 معرف الجهاز:', deviceId);
            
            if (!licenseCode) {
                showResult('⚠️ يرجى إدخال كود الترخيص', 'error');
                return;
            }
            
            // التحقق من تنسيق كود الترخيص
            const licensePattern = /^[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$/;
            if (!licensePattern.test(licenseCode)) {
                showResult('❌ تنسيق كود الترخيص غير صحيح. يجب أن يكون بالشكل: XXXX-XXXX-XXXX-XXXX', 'error');
                return;
            }
            
            // التحقق من صحة الترخيص
            const validation = validateLicense(licenseCode, deviceId);
            console.log('✅ نتيجة التحقق:', validation);
            
            if (!validation.valid) {
                showResult(`❌ ${validation.message}`, 'error');
                return;
            }
            
            // نجح التحقق
            showResult(`✅ ${validation.message}<br><br>🎯 سيتم الانتقال إلى النظام الرئيسي خلال 3 ثوان...`, 'success');
            
            // حفظ معلومات الجلسة
            const sessionData = {
                licenseCode: licenseCode,
                deviceId: deviceId,
                loginTime: new Date().toISOString(),
                isActivated: true,
                licenseType: validation.license.type,
                expiresAt: validation.license.expiresAt
            };
            
            localStorage.setItem('currentSession', JSON.stringify(sessionData));
            console.log('💾 تم حفظ بيانات الجلسة:', sessionData);
            
            // الانتقال للنظام الرئيسي
            setTimeout(() => {
                console.log('🔄 محاولة الانتقال إلى dashboard.html...');
                try {
                    window.location.href = 'dashboard.html';
                } catch (error) {
                    console.error('❌ خطأ في الانتقال:', error);
                    showResult(`❌ خطأ في فتح النظام الرئيسي: ${error.message}`, 'error');
                }
            }, 3000);
        });

        // تنسيق كود الترخيص تلقائياً
        document.getElementById('licenseCode').addEventListener('input', function(e) {
            let value = e.target.value.replace(/[^A-Z0-9]/g, '').toUpperCase();
            let formatted = value.match(/.{1,4}/g)?.join('-') || value;
            if (formatted.length > 19) formatted = formatted.substr(0, 19);
            e.target.value = formatted;
        });

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateDeviceId();
            console.log('🎯 صفحة اختبار تسجيل الدخول جاهزة');
            
            // عرض التراخيص المتاحة
            const licenses = getValidLicenses();
            console.log('📋 التراخيص المتاحة للاختبار:', licenses);
        });
    </script>
</body>
</html>
