// Future Fuel Management System - Advanced Notifications System
// Version 2.2.0 - Professional notification management

class NotificationManager {
    constructor() {
        this.notifications = [];
        this.maxNotifications = 5;
        this.defaultDuration = 5000;
        this.container = null;
        this.init();
    }

    init() {
        this.createContainer();
        this.setupStyles();
        this.checkPermissions();
    }

    createContainer() {
        // Remove existing container if any
        const existing = document.getElementById('notification-container');
        if (existing) {
            existing.remove();
        }

        // Create new container
        this.container = document.createElement('div');
        this.container.id = 'notification-container';
        this.container.className = 'notification-container';
        document.body.appendChild(this.container);
    }

    setupStyles() {
        if (document.getElementById('notification-styles')) return;

        const styles = document.createElement('style');
        styles.id = 'notification-styles';
        styles.textContent = `
            .notification-container {
                position: fixed;
                top: 20px;
                left: 20px;
                z-index: 10000;
                pointer-events: none;
                max-width: 400px;
            }

            .notification {
                background: white;
                border-radius: 8px;
                padding: 16px;
                margin-bottom: 12px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.15);
                border-left: 4px solid #4CAF50;
                transform: translateX(-100%);
                transition: all 0.3s ease;
                pointer-events: auto;
                position: relative;
                overflow: hidden;
            }

            .notification.show {
                transform: translateX(0);
            }

            .notification.success {
                border-left-color: #4CAF50;
            }

            .notification.error {
                border-left-color: #f44336;
            }

            .notification.warning {
                border-left-color: #ff9800;
            }

            .notification.info {
                border-left-color: #2196F3;
            }

            .notification-header {
                display: flex;
                align-items: center;
                margin-bottom: 8px;
            }

            .notification-icon {
                width: 20px;
                height: 20px;
                margin-left: 8px;
                flex-shrink: 0;
            }

            .notification-title {
                font-weight: bold;
                font-size: 14px;
                color: #333;
                flex: 1;
            }

            .notification-close {
                background: none;
                border: none;
                font-size: 18px;
                cursor: pointer;
                color: #999;
                padding: 0;
                width: 20px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .notification-close:hover {
                color: #666;
            }

            .notification-message {
                font-size: 13px;
                color: #666;
                line-height: 1.4;
            }

            .notification-progress {
                position: absolute;
                bottom: 0;
                right: 0;
                height: 3px;
                background: rgba(0,0,0,0.1);
                transition: width linear;
            }

            .notification.success .notification-progress {
                background: #4CAF50;
            }

            .notification.error .notification-progress {
                background: #f44336;
            }

            .notification.warning .notification-progress {
                background: #ff9800;
            }

            .notification.info .notification-progress {
                background: #2196F3;
            }

            /* Dark mode support */
            .dark-mode .notification {
                background: var(--surface-color);
                color: var(--text-color);
                border: 1px solid var(--border-color);
            }

            .dark-mode .notification-title {
                color: var(--text-color);
            }

            .dark-mode .notification-message {
                color: var(--text-secondary);
            }

            /* RTL support */
            [dir="rtl"] .notification-container {
                right: 20px;
                left: auto;
            }

            [dir="rtl"] .notification {
                transform: translateX(100%);
                border-right: 4px solid #4CAF50;
                border-left: none;
            }

            [dir="rtl"] .notification.show {
                transform: translateX(0);
            }

            [dir="rtl"] .notification-icon {
                margin-right: 8px;
                margin-left: 0;
            }
        `;
        document.head.appendChild(styles);
    }

    checkPermissions() {
        // Check for browser notification permissions
        if ('Notification' in window && Notification.permission === 'default') {
            Notification.requestPermission();
        }
    }

    show(options) {
        const {
            type = 'info',
            title = 'إشعار',
            message = '',
            duration = this.defaultDuration,
            persistent = false,
            actions = [],
            showBrowserNotification = false
        } = options;

        // Create notification element
        const notification = this.createNotificationElement({
            type,
            title,
            message,
            persistent,
            actions
        });

        // Add to container
        this.container.appendChild(notification);
        this.notifications.push(notification);

        // Show animation
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);

        // Auto-hide if not persistent
        if (!persistent && duration > 0) {
            this.startProgressBar(notification, duration);
            setTimeout(() => {
                this.hide(notification);
            }, duration);
        }

        // Show browser notification if requested
        if (showBrowserNotification && 'Notification' in window && Notification.permission === 'granted') {
            new Notification(title, {
                body: message,
                icon: 'assets/icons/app-icon.ico',
                tag: 'future-fuel-' + Date.now()
            });
        }

        // Limit number of notifications
        this.limitNotifications();

        return notification;
    }

    createNotificationElement(options) {
        const { type, title, message, persistent, actions } = options;

        const notification = document.createElement('div');
        notification.className = `notification ${type}`;

        const icons = {
            success: '✅',
            error: '❌',
            warning: '⚠️',
            info: 'ℹ️'
        };

        notification.innerHTML = `
            <div class="notification-header">
                <span class="notification-icon">${icons[type] || icons.info}</span>
                <span class="notification-title">${title}</span>
                ${!persistent ? '<button class="notification-close" onclick="notificationManager.hide(this.closest(\'.notification\'))">&times;</button>' : ''}
            </div>
            <div class="notification-message">${message}</div>
            ${actions.length > 0 ? this.createActionsHTML(actions) : ''}
            ${!persistent ? '<div class="notification-progress"></div>' : ''}
        `;

        return notification;
    }

    createActionsHTML(actions) {
        const actionsHTML = actions.map(action => 
            `<button class="notification-action" onclick="${action.callback}">${action.label}</button>`
        ).join('');

        return `<div class="notification-actions">${actionsHTML}</div>`;
    }

    startProgressBar(notification, duration) {
        const progressBar = notification.querySelector('.notification-progress');
        if (progressBar) {
            progressBar.style.width = '100%';
            progressBar.style.transition = `width ${duration}ms linear`;
            setTimeout(() => {
                progressBar.style.width = '0%';
            }, 50);
        }
    }

    hide(notification) {
        if (!notification || !notification.parentNode) return;

        notification.classList.remove('show');
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
            this.notifications = this.notifications.filter(n => n !== notification);
        }, 300);
    }

    limitNotifications() {
        while (this.notifications.length > this.maxNotifications) {
            const oldest = this.notifications[0];
            this.hide(oldest);
        }
    }

    clear() {
        this.notifications.forEach(notification => {
            this.hide(notification);
        });
    }

    // Convenience methods
    success(title, message, options = {}) {
        return this.show({
            type: 'success',
            title,
            message,
            ...options
        });
    }

    error(title, message, options = {}) {
        return this.show({
            type: 'error',
            title,
            message,
            persistent: true,
            ...options
        });
    }

    warning(title, message, options = {}) {
        return this.show({
            type: 'warning',
            title,
            message,
            ...options
        });
    }

    info(title, message, options = {}) {
        return this.show({
            type: 'info',
            title,
            message,
            ...options
        });
    }

    // System notifications for specific events
    showSaveSuccess() {
        this.success('تم الحفظ', 'تم حفظ البيانات بنجاح');
    }

    showSaveError(error) {
        this.error('خطأ في الحفظ', `فشل في حفظ البيانات: ${error}`);
    }

    showBackupSuccess(filename) {
        this.success('نسخة احتياطية', `تم إنشاء النسخة الاحتياطية: ${filename}`);
    }

    showConnectionLost() {
        this.warning('انقطاع الاتصال', 'تم فقدان الاتصال بالخادم');
    }

    showConnectionRestored() {
        this.success('استعادة الاتصال', 'تم استعادة الاتصال بالخادم');
    }

    showUpdateAvailable() {
        this.info('تحديث متوفر', 'يتوفر تحديث جديد للتطبيق', {
            actions: [
                {
                    label: 'تحديث الآن',
                    callback: 'updateApp()'
                }
            ]
        });
    }
}

// Initialize notification manager
const notificationManager = new NotificationManager();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = NotificationManager;
}
