@echo off
title Future Fuel Corporation - Demo Login
color 0A
mode con: cols=80 lines=25

chcp 65001 >nul 2>&1

cls
echo.
echo ================================================================================
echo                    FUTURE FUEL CORPORATION - DEMO LOGIN
echo                    مؤسسة وقود المستقبل - تسجيل دخول تجريبي
echo ================================================================================
echo.
echo                           🆓 FREE 30-DAY TRIAL 🆓
echo                           🆓 تجربة مجانية 30 يوم 🆓
echo.
echo ================================================================================
echo.

echo 🎯 This will launch the application with demo credentials
echo 🎯 سيتم تشغيل التطبيق مع بيانات اعتماد تجريبية
echo.
echo Demo License Code: DEMO-2024-TEST-0001
echo كود الترخيص التجريبي: DEMO-2024-TEST-0001
echo.
echo ⏰ Trial Period: 30 days from first use
echo ⏰ فترة التجربة: 30 يوماً من أول استخدام
echo.
echo ✅ All features available جميع الميزات متاحة
echo ✅ Full functionality وظائف كاملة
echo ✅ Technical support دعم فني
echo ✅ No limitations بدون قيود
echo.

echo ================================================================================
echo                              DEMO FEATURES
echo                              ميزات التجربة
echo ================================================================================
echo.
echo 🏢 BUSINESS MANAGEMENT إدارة الأعمال:
echo    ✓ Gas card management إدارة بطاقات الغاز
echo    ✓ Customer database قاعدة بيانات الزبائن
echo    ✓ Appointment scheduling جدولة المواعيد
echo    ✓ Inventory tracking تتبع المخزون
echo.
echo 📊 REPORTING & ANALYTICS التقارير والتحليلات:
echo    ✓ Sales reports تقارير المبيعات
echo    ✓ Financial tracking تتبع مالي
echo    ✓ Performance analytics تحليلات الأداء
echo    ✓ Custom reports تقارير مخصصة
echo.
echo 🔒 SECURITY & BACKUP الأمان والنسخ الاحتياطية:
echo    ✓ Data encryption تشفير البيانات
echo    ✓ Automatic backups نسخ احتياطية تلقائية
echo    ✓ User access control تحكم في وصول المستخدمين
echo    ✓ Audit trails مسارات التدقيق
echo.

echo ================================================================================
echo                              AFTER TRIAL
echo                              بعد التجربة
echo ================================================================================
echo.
echo 💰 PURCHASE OPTIONS خيارات الشراء:
echo.
echo 📅 MONTHLY شهري: 299 SAR/month
echo    ✓ All features جميع الميزات
echo    ✓ Full support دعم كامل
echo    ✓ Regular updates تحديثات منتظمة
echo.
echo 📆 YEARLY سنوي: 2999 SAR/year (Save 1000 SAR!)
echo    ✓ All features + priority support جميع الميزات + دعم أولوية
echo    ✓ Free updates تحديثات مجانية
echo    ✓ Training included تدريب مشمول
echo.
echo ♾️ LIFETIME مدى الحياة: 9999 SAR (one-time)
echo    ✓ Lifetime updates تحديثات مدى الحياة
echo    ✓ Lifetime support دعم مدى الحياة
echo    ✓ All future features جميع الميزات المستقبلية
echo.

echo ================================================================================
echo                              CONTACT FOR PURCHASE
echo                              التواصل للشراء
echo ================================================================================
echo.
echo 📧 EMAIL: <EMAIL>
echo 📞 PHONE: +966-11-123-4567
echo 🌐 WEBSITE: www.futurefuel.com
echo 💬 WHATSAPP: +966-50-123-4567
echo.
echo Working Hours: Sunday-Thursday 8AM-6PM, Friday-Saturday 9AM-3PM
echo ساعات العمل: الأحد-الخميس 8ص-6م، الجمعة-السبت 9ص-3م
echo.

echo ================================================================================
echo.

set /p "START=Start demo trial now? بدء التجربة الآن؟ (Y/N): "
if /i "%START%"=="Y" (
    echo.
    echo 🚀 Launching demo application...
    echo 🚀 تشغيل التطبيق التجريبي...
    echo.
    echo 📋 Demo credentials will be auto-filled:
    echo 📋 بيانات الاعتماد التجريبية ستملأ تلقائياً:
    echo    License Code: DEMO-2024-TEST-0001
    echo    كود الترخيص: DEMO-2024-TEST-0001
    echo.
    
    :: إنشاء ملف إعدادات تجريبي
    (
    echo {
    echo   "demoMode": true,
    echo   "autoFillLicense": "DEMO-2024-TEST-0001",
    echo   "trialStartDate": "%DATE%",
    echo   "trialDays": 30,
    echo   "features": {
    echo     "allEnabled": true,
    echo     "supportLevel": "demo"
    echo   }
    echo }
    ) > "data\demo-config.json" 2>nul
    
    timeout /t 2 /nobreak >nul
    
    :: تشغيل التطبيق
    if exist "FutureFuel-Launcher.vbs" (
        start "" "FutureFuel-Launcher.vbs"
    ) else if exist "app\index.html" (
        start "" "app\index.html"
    ) else (
        echo ❌ Application files not found
        echo ❌ ملفات التطبيق غير موجودة
        echo.
        echo Please run the installer first
        echo يرجى تشغيل المثبت أولاً
        pause
        exit /b 1
    )
    
    echo.
    echo ✅ Demo application launched successfully!
    echo ✅ تم تشغيل التطبيق التجريبي بنجاح!
    echo.
    echo 💡 TIP: The demo license code is already filled in
    echo 💡 نصيحة: كود الترخيص التجريبي مملوء مسبقاً
    echo.
    echo 🎯 Enjoy your 30-day free trial!
    echo 🎯 استمتع بتجربتك المجانية لمدة 30 يوماً!
    
) else (
    echo.
    echo Thank you for your interest!
    echo شكراً لاهتمامك!
    echo.
    echo Contact us when you're ready to start your trial:
    echo تواصل معنا عندما تكون مستعداً لبدء التجربة:
    echo 📧 <EMAIL>
    echo 📞 +966-11-123-4567
)

echo.
echo ================================================================================
echo.
pause
