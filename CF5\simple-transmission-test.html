<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار جدول الإرسال المبسط</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', sans-serif;
            background-color: #f1f5f9;
            margin: 0;
            padding: 20px;
            direction: rtl;
            color: #334155;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .section {
            padding: 2rem;
            border-bottom: 1px solid #e2e8f0;
        }

        .section h2 {
            color: #1e3a8a;
            margin-bottom: 1rem;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            font-size: 1rem;
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .btn {
            padding: 0.75rem 1.25rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            font-size: 0.9rem;
            margin: 0.5rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
            color: white;
        }

        .btn-success {
            background: #10b981;
            color: white;
        }

        .btn-warning {
            background: #f59e0b;
            color: white;
        }

        .btn-danger {
            background: #ef4444;
            color: white;
        }

        .transmission-header {
            background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
            color: white;
            padding: 2rem;
            margin-bottom: 1.5rem;
            border-radius: 8px;
            text-align: center;
        }

        .transmission-header h3 {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
            font-weight: bold;
        }

        .transmission-header h4 {
            font-size: 1.2rem;
            margin-bottom: 0.5rem;
            font-weight: 600;
            opacity: 0.9;
        }

        .transmission-header p {
            margin: 0.3rem 0;
            font-size: 1rem;
            opacity: 0.8;
        }

        .transmission-header .highlight {
            font-weight: bold;
            font-size: 1.1rem;
            margin: 1rem 0;
            background: rgba(255, 255, 255, 0.1);
            padding: 0.5rem;
            border-radius: 4px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        th {
            background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
            color: white;
            padding: 1rem 0.75rem;
            text-align: center;
            font-weight: 600;
            font-size: 0.9rem;
        }

        td {
            padding: 0.875rem 0.75rem;
            text-align: center;
            border-bottom: 1px solid #e2e8f0;
            font-size: 0.9rem;
        }

        tbody tr:nth-child(even) {
            background-color: #f8fafc;
        }

        tbody tr:hover {
            background-color: #f1f5f9;
            transform: scale(1.01);
            transition: all 0.2s ease;
        }

        .empty-state {
            text-align: center;
            padding: 3rem;
            color: #6b7280;
            background: #f8fafc;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        .empty-state h3 {
            margin-bottom: 0.5rem;
            color: #334155;
        }

        .empty-state p {
            margin-bottom: 1.5rem;
            opacity: 0.7;
        }

        .service-info {
            margin-top: 0.5rem;
            padding: 0.5rem;
            background: #e0f2fe;
            border-radius: 4px;
            font-size: 0.9rem;
            color: #0277bd;
            display: none;
        }

        .summary-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1.5rem;
        }

        .summary-card {
            background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .summary-card h4 {
            margin: 0 0 0.5rem 0;
            font-size: 1rem;
            opacity: 0.9;
        }

        .summary-card .count {
            font-size: 2rem;
            font-weight: bold;
            margin: 0;
        }

        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #10b981;
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }

        .toast.show {
            transform: translateX(0);
        }

        .toast.error {
            background: #ef4444;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 اختبار جدول الإرسال المبسط</h1>
            <p>اختبار مباشر لجميع وظائف جدول الإرسال</p>
        </div>

        <!-- نموذج إضافة عميل مع نوع الخدمة -->
        <div class="section">
            <h2>1️⃣ إضافة عميل مع نوع الخدمة</h2>
            
            <form id="customer-form">
                <div class="form-row">
                    <div class="form-group">
                        <label for="service-type">نوع الخدمة: <span style="color: red;">*</span></label>
                        <select id="service-type" required>
                            <option value="">اختر نوع الخدمة</option>
                            <option value="تركيب">تركيب خزان غاز</option>
                            <option value="مراقبة">مراقبة دورية</option>
                            <option value="تجديد بطاقة">تجديد بطاقة الغاز</option>
                        </select>
                        <div id="service-type-info" class="service-info"></div>
                    </div>
                    <div class="form-group">
                        <label for="service-date">تاريخ الخدمة: <span style="color: red;">*</span></label>
                        <input type="date" id="service-date" required>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="customer-name">اسم العميل: <span style="color: red;">*</span></label>
                        <input type="text" id="customer-name" required placeholder="مثال: أحمد محمد علي">
                    </div>
                    <div class="form-group">
                        <label for="customer-phone">رقم الهاتف: <span style="color: red;">*</span></label>
                        <input type="tel" id="customer-phone" required placeholder="مثال: 0555123456">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="vehicle-plate">رقم التسجيل: <span style="color: red;">*</span></label>
                        <input type="text" id="vehicle-plate" required placeholder="مثال: 1506615-26">
                    </div>
                    <div class="form-group">
                        <label for="vehicle-type">نوع السيارة: <span style="color: red;">*</span></label>
                        <input type="text" id="vehicle-type" required placeholder="مثال: Toyota Corolla">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="tank-number">رقم خزان الغاز:</label>
                        <input type="text" id="tank-number" placeholder="مثال: 1506623">
                    </div>
                    <div class="form-group">
                        <label for="serial-number">الرقم التسلسلي:</label>
                        <input type="text" id="serial-number" placeholder="مثال: VF32A5FWC12345678">
                    </div>
                </div>

                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-plus"></i> إضافة العميل
                </button>
            </form>
        </div>

        <!-- جدول الإرسال -->
        <div class="section">
            <h2>2️⃣ جدول الإرسال</h2>
            
            <div class="transmission-header">
                <h3>الجمهورية الجزائرية الديمقراطية الشعبية</h3>
                <h4 id="company-name">مركز وقود المستقبل - عزيري عبد الله اسحاق</h4>
                <p>رقم: <span id="company-number">463/2019</span></p>
                <p>إلى السيد: <span id="directorate-name">مدير الصناعة و المناجم لولاية المدية</span></p>
                <p class="highlight"><strong>جدول إرسال</strong></p>
                <p>تجدون طي هذه المراسلة الوثائق الخاصة بالسيارات المجهزة بغاز البترول المميع شهر <span id="current-month-year"></span>.</p>
            </div>

            <div style="margin-bottom: 1rem;">
                <button type="button" class="btn btn-primary" onclick="addSampleEntry()">
                    <i class="fas fa-plus"></i> إضافة عملية تجريبية
                </button>
                <button type="button" class="btn btn-success" onclick="printTable()">
                    <i class="fas fa-print"></i> طباعة الجدول
                </button>
                <button type="button" class="btn btn-warning" onclick="exportToPDF()">
                    <i class="fas fa-file-pdf"></i> تصدير PDF
                </button>
                <button type="button" class="btn btn-danger" onclick="clearTable()">
                    <i class="fas fa-trash"></i> مسح الجدول
                </button>
            </div>

            <table id="transmission-table">
                <thead>
                    <tr>
                        <th>تركيب أو مراقبة</th>
                        <th>رقم خزان الغاز</th>
                        <th>الصنف</th>
                        <th>الرقم التسلسلي</th>
                        <th>رقم التسجيل</th>
                        <th>الإسم و اللقب</th>
                        <th>الرقم</th>
                        <th>تاريخ العملية</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody id="transmission-table-body">
                    <!-- سيتم ملؤها بواسطة JavaScript -->
                </tbody>
            </table>
            
            <div id="empty-state" class="empty-state" style="display: none;">
                <i class="fas fa-table"></i>
                <h3>لا توجد بيانات</h3>
                <p>لم يتم إضافة أي عمليات إلى جدول الإرسال بعد</p>
                <button type="button" class="btn btn-primary" onclick="addSampleEntry()">
                    <i class="fas fa-plus"></i> إضافة أول عملية
                </button>
            </div>

            <div class="summary-cards">
                <div class="summary-card">
                    <h4>إجمالي العمليات</h4>
                    <div class="count" id="total-count">0</div>
                </div>
                <div class="summary-card">
                    <h4>عمليات التركيب</h4>
                    <div class="count" id="installation-count">0</div>
                </div>
                <div class="summary-card">
                    <h4>عمليات المراقبة</h4>
                    <div class="count" id="monitoring-count">0</div>
                </div>
                <div class="summary-card">
                    <h4>عمليات هذا الشهر</h4>
                    <div class="count" id="current-month-count">0</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // بيانات التطبيق
        let appData = {
            customers: [],
            vehicles: [],
            gasCards: [],
            transmissionTable: [],
            settings: {
                company: {
                    name: 'مركز وقود المستقبل - عزيري عبد الله اسحاق',
                    number: '463/2019',
                    directorateName: 'مدير الصناعة و المناجم لولاية المدية'
                }
            }
        };

        // تحميل البيانات من localStorage
        function loadData() {
            const savedData = localStorage.getItem('simpleTransmissionData');
            if (savedData) {
                try {
                    appData = JSON.parse(savedData);
                } catch (e) {
                    console.error('Error loading data:', e);
                }
            }
        }

        // حفظ البيانات في localStorage
        function saveData() {
            localStorage.setItem('simpleTransmissionData', JSON.stringify(appData));
        }

        // إنشاء معرف فريد
        function generateId() {
            return Date.now().toString(36) + Math.random().toString(36).substr(2);
        }

        // تنسيق التاريخ
        function formatDate(dateStr) {
            const date = new Date(dateStr);
            return date.toLocaleDateString('ar-SA');
        }

        // إظهار رسالة
        function showToast(message, isSuccess = true) {
            const toast = document.createElement('div');
            toast.className = `toast ${isSuccess ? '' : 'error'}`;
            toast.textContent = message;
            document.body.appendChild(toast);

            setTimeout(() => toast.classList.add('show'), 100);
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => document.body.removeChild(toast), 300);
            }, 3000);
        }

        // معالجة تغيير نوع الخدمة
        function handleServiceTypeChange() {
            const serviceType = document.getElementById('service-type').value;
            const serviceInfo = document.getElementById('service-type-info');

            if (serviceType) {
                let message = '';
                if (serviceType === 'تركيب') {
                    message = '✅ سيتم إضافة هذه العملية إلى جدول الإرسال';
                } else if (serviceType === 'مراقبة') {
                    message = '✅ سيتم إضافة هذه العملية إلى جدول الإرسال';
                } else if (serviceType === 'تجديد بطاقة') {
                    message = '🎫 سيتم إضافة/تحديث بطاقة الغاز للعميل';
                }

                serviceInfo.textContent = message;
                serviceInfo.style.display = 'block';
            } else {
                serviceInfo.style.display = 'none';
            }
        }

        // تحديث الشهر والسنة الحالية
        function updateCurrentMonthYear() {
            const now = new Date();
            const monthNames = [
                'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
            ];
            const currentMonthYear = `${monthNames[now.getMonth()]} ${now.getFullYear()}`;
            document.getElementById('current-month-year').textContent = currentMonthYear;
        }

        // تحديث جدول الإرسال
        function updateTransmissionTable() {
            const tableBody = document.getElementById('transmission-table-body');
            const emptyState = document.getElementById('empty-state');
            const table = document.getElementById('transmission-table');

            if (appData.transmissionTable.length === 0) {
                table.style.display = 'none';
                emptyState.style.display = 'block';
            } else {
                table.style.display = 'table';
                emptyState.style.display = 'none';

                // ترتيب البيانات حسب تاريخ العملية (الأحدث أولاً)
                const sortedData = [...appData.transmissionTable].sort((a, b) =>
                    new Date(b.operationDate) - new Date(a.operationDate)
                );

                tableBody.innerHTML = sortedData.map((entry, index) => `
                    <tr>
                        <td>${entry.type}</td>
                        <td>${entry.tankNumber}</td>
                        <td>${entry.carType}</td>
                        <td>${entry.serialNumber}</td>
                        <td>${entry.registrationNumber}</td>
                        <td>${entry.ownerName}</td>
                        <td>${index + 1}</td>
                        <td>${formatDate(entry.operationDate)}</td>
                        <td>
                            <button type="button" class="btn btn-danger" onclick="deleteEntry('${entry.id}')" style="padding: 0.25rem 0.5rem; margin: 0;">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `).join('');
            }

            updateSummary();
        }

        // تحديث الملخص
        function updateSummary() {
            const totalCount = appData.transmissionTable.length;
            const installationCount = appData.transmissionTable.filter(entry => entry.type === 'تركيب').length;
            const monitoringCount = appData.transmissionTable.filter(entry => entry.type === 'مراقبة').length;

            // عمليات الشهر الحالي
            const now = new Date();
            const currentMonth = now.getMonth();
            const currentYear = now.getFullYear();
            const currentMonthCount = appData.transmissionTable.filter(entry => {
                const entryDate = new Date(entry.operationDate);
                return entryDate.getMonth() === currentMonth && entryDate.getFullYear() === currentYear;
            }).length;

            document.getElementById('total-count').textContent = totalCount;
            document.getElementById('installation-count').textContent = installationCount;
            document.getElementById('monitoring-count').textContent = monitoringCount;
            document.getElementById('current-month-count').textContent = currentMonthCount;
        }

        // إضافة عملية تجريبية
        function addSampleEntry() {
            const sampleData = {
                id: generateId(),
                type: 'تركيب',
                tankNumber: `150${Math.floor(Math.random() * 10000)}`,
                carType: 'Toyota Corolla',
                serialNumber: `VF${Math.floor(Math.random() * 1000000000)}`,
                registrationNumber: `${Math.floor(Math.random() * 9000000) + 1000000}-26`,
                ownerName: 'عميل تجريبي',
                operationDate: new Date().toISOString().split('T')[0],
                createdAt: new Date().toISOString()
            };

            appData.transmissionTable.push(sampleData);
            saveData();
            updateTransmissionTable();
            showToast('تم إضافة عملية تجريبية بنجاح');
        }

        // حذف عملية
        function deleteEntry(entryId) {
            if (confirm('هل أنت متأكد من حذف هذه العملية؟')) {
                const index = appData.transmissionTable.findIndex(e => e.id === entryId);
                if (index !== -1) {
                    appData.transmissionTable.splice(index, 1);
                    saveData();
                    updateTransmissionTable();
                    showToast('تم حذف العملية بنجاح');
                }
            }
        }

        // طباعة الجدول
        function printTable() {
            window.print();
        }

        // تصدير إلى PDF (محاكاة)
        function exportToPDF() {
            if (appData.transmissionTable.length === 0) {
                showToast('لا توجد بيانات للتصدير', false);
                return;
            }

            let content = 'جدول الإرسال\n\n';
            content += 'الجمهورية الجزائرية الديمقراطية الشعبية\n';
            content += 'مركز وقود المستقبل - عزيري عبد الله اسحاق\n';
            content += 'رقم: 463/2019\n\n';

            appData.transmissionTable.forEach((entry, index) => {
                content += `${index + 1}. ${entry.type} - ${entry.ownerName} - ${entry.registrationNumber} - ${formatDate(entry.operationDate)}\n`;
            });

            const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'جدول_الإرسال.txt';
            a.click();
            URL.revokeObjectURL(url);

            showToast('تم تصدير الجدول بنجاح');
        }

        // مسح الجدول
        function clearTable() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات؟')) {
                appData.transmissionTable = [];
                saveData();
                updateTransmissionTable();
                showToast('تم مسح الجدول بنجاح');
            }
        }

        // إعداد النموذج
        function setupForm() {
            const form = document.getElementById('customer-form');
            const serviceTypeSelect = document.getElementById('service-type');
            const serviceDateInput = document.getElementById('service-date');

            // تعيين تاريخ اليوم
            serviceDateInput.value = new Date().toISOString().split('T')[0];

            // إضافة مستمع أحداث نوع الخدمة
            serviceTypeSelect.addEventListener('change', handleServiceTypeChange);

            // معالجة تقديم النموذج
            form.addEventListener('submit', (e) => {
                e.preventDefault();

                const serviceType = document.getElementById('service-type').value;
                const serviceDate = document.getElementById('service-date').value;
                const customerName = document.getElementById('customer-name').value;
                const customerPhone = document.getElementById('customer-phone').value;
                const vehiclePlate = document.getElementById('vehicle-plate').value;
                const vehicleType = document.getElementById('vehicle-type').value;
                const tankNumber = document.getElementById('tank-number').value;
                const serialNumber = document.getElementById('serial-number').value;

                if (!serviceType || !serviceDate || !customerName || !customerPhone || !vehiclePlate || !vehicleType) {
                    showToast('يرجى ملء جميع الحقول المطلوبة', false);
                    return;
                }

                // إنشاء العميل والسيارة
                const customerId = generateId();
                const vehicleId = generateId();

                const customer = {
                    id: customerId,
                    name: customerName,
                    phone: customerPhone,
                    createdAt: new Date().toISOString()
                };

                const vehicle = {
                    id: vehicleId,
                    plateNumber: vehiclePlate,
                    brand: vehicleType.split(' ')[0] || 'غير محدد',
                    model: vehicleType.split(' ').slice(1).join(' ') || 'غير محدد',
                    customerId: customerId,
                    createdAt: new Date().toISOString()
                };

                appData.customers.push(customer);
                appData.vehicles.push(vehicle);

                // معالجة نوع الخدمة
                if (serviceType === 'تركيب' || serviceType === 'مراقبة') {
                    // إضافة إلى جدول الإرسال
                    const transmissionEntry = {
                        id: generateId(),
                        type: serviceType,
                        tankNumber: tankNumber || 'غير محدد',
                        carType: vehicleType,
                        serialNumber: serialNumber || tankNumber || 'غير محدد',
                        registrationNumber: vehiclePlate,
                        ownerName: customerName,
                        operationDate: serviceDate,
                        customerId: customerId,
                        vehicleId: vehicleId,
                        createdAt: new Date().toISOString()
                    };

                    appData.transmissionTable.push(transmissionEntry);
                    showToast(`تم إضافة عملية ${serviceType} إلى جدول الإرسال`);

                } else if (serviceType === 'تجديد بطاقة') {
                    // إضافة بطاقة غاز
                    const gasCard = {
                        id: generateId(),
                        customerId: customerId,
                        vehicleId: vehicleId,
                        cardNumber: generateCardNumber(),
                        issueDate: serviceDate,
                        expiryDate: new Date(new Date(serviceDate).setFullYear(new Date(serviceDate).getFullYear() + 1)).toISOString().split('T')[0],
                        status: 'active',
                        notes: `بطاقة جديدة - ${serviceType}`,
                        createdAt: new Date().toISOString()
                    };

                    appData.gasCards.push(gasCard);
                    showToast(`تم إصدار بطاقة غاز جديدة رقم: ${gasCard.cardNumber}`);
                }

                saveData();
                updateTransmissionTable();

                // مسح النموذج
                form.reset();
                serviceDateInput.value = new Date().toISOString().split('T')[0];
                document.getElementById('service-type-info').style.display = 'none';
            });
        }

        // إنشاء رقم بطاقة
        function generateCardNumber() {
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
            return `${year}${month}${day}${random}`;
        }

        // التهيئة عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', () => {
            loadData();
            updateCurrentMonthYear();
            updateTransmissionTable();
            setupForm();

            console.log('Simple transmission test loaded successfully!');
        });
    </script>
</body>
</html>
