const { app, BrowserWindow, Menu, dialog, ipcMain } = require('electron');
const path = require('path');

// تعطيل تحذيرات الأمان في بيئة التطوير
process.env.NODE_ENV = 'production';

// المتغير العام للنافذة الرئيسية
let mainWindow;

// التحقق من وجود نسخة واحدة فقط من التطبيق
const gotTheLock = app.requestSingleInstanceLock();

if (!gotTheLock) {
  app.quit();
} else {
  app.on('second-instance', () => {
    // إذا حاول المستخدم فتح نسخة ثانية، ركز على النافذة الموجودة
    if (mainWindow) {
      if (mainWindow.isMinimized()) mainWindow.restore();
      mainWindow.focus();
    }
  });
}

// إنشاء النافذة الرئيسية
function createMainWindow() {
  mainWindow = new BrowserWindow({
    title: 'نظام إدارة مؤسسة وقود المستقبل',
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'),
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false
    },
    icon: path.join(__dirname, 'assets/icons/app-icon.ico'),
    show: false // لا تظهر النافذة حتى تكتمل عملية التحميل
  });

  // تحميل الصفحة الرئيسية
  mainWindow.loadFile('index.html');

  // إظهار النافذة عند اكتمال التحميل
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // إنشاء قائمة التطبيق
  const mainMenu = Menu.buildFromTemplate(menuTemplate);
  Menu.setApplicationMenu(mainMenu);

  // إغلاق النافذة
  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

// إنشاء قائمة التطبيق
const menuTemplate = [
  {
    label: 'ملف',
    submenu: [
      {
        label: 'حفظ البيانات',
        accelerator: process.platform === 'darwin' ? 'Command+S' : 'Ctrl+S',
        click() {
          mainWindow.webContents.executeJavaScript('saveData()');
        }
      },
      {
        label: 'طباعة',
        accelerator: process.platform === 'darwin' ? 'Command+P' : 'Ctrl+P',
        click() {
          mainWindow.webContents.print();
        }
      },
      {
        type: 'separator'
      },
      {
        label: 'خروج',
        accelerator: process.platform === 'darwin' ? 'Command+Q' : 'Ctrl+Q',
        click() {
          app.quit();
        }
      }
    ]
  },
  {
    label: 'عرض',
    submenu: [
      {
        label: 'إعادة تحميل',
        accelerator: 'F5',
        click() {
          mainWindow.reload();
        }
      },
      {
        label: 'تكبير',
        accelerator: process.platform === 'darwin' ? 'Command+Plus' : 'Ctrl+Plus',
        click() {
          mainWindow.webContents.setZoomLevel(mainWindow.webContents.getZoomLevel() + 1);
        }
      },
      {
        label: 'تصغير',
        accelerator: process.platform === 'darwin' ? 'Command+-' : 'Ctrl+-',
        click() {
          mainWindow.webContents.setZoomLevel(mainWindow.webContents.getZoomLevel() - 1);
        }
      },
      {
        label: 'حجم طبيعي',
        accelerator: process.platform === 'darwin' ? 'Command+0' : 'Ctrl+0',
        click() {
          mainWindow.webContents.setZoomLevel(0);
        }
      }
    ]
  },
  {
    label: 'مساعدة',
    submenu: [
      {
        label: 'حول البرنامج',
        click() {
          dialog.showMessageBox(mainWindow, {
            type: 'info',
            title: 'حول البرنامج',
            message: 'نظام إدارة مؤسسة وقود المستقبل',
            detail: 'الإصدار 2.2.0\nنظام متكامل لإدارة مؤسسة وقود المستقبل\n\nالميزات الجديدة:\n• نسخ احتياطية تلقائية\n• تكامل تيليجرام\n• اختصارات لوحة المفاتيح\n• الوضع المظلم'
          });
        }
      }
    ]
  }
];

// معالجة أحداث IPC
const setupIpcHandlers = () => {
  // فتح مربع حوار حفظ الملف
  ipcMain.handle('save-dialog', async (_, options) => {
    const { canceled, filePath } = await dialog.showSaveDialog(options);
    if (canceled) {
      return null;
    }
    return filePath;
  });

  // فتح مربع حوار فتح الملف
  ipcMain.handle('open-dialog', async (_, options) => {
    const { canceled, filePaths } = await dialog.showOpenDialog(options);
    if (canceled) {
      return null;
    }
    return filePaths[0];
  });

  // إنشاء نسخة احتياطية تلقائية
  ipcMain.on('create-auto-backup', () => {
    // يمكن إضافة كود لإنشاء نسخة احتياطية تلقائية هنا
  });
};

// تشغيل التطبيق
app.whenReady().then(() => {
  setupIpcHandlers();
  createMainWindow();
});

// إغلاق التطبيق عند إغلاق جميع النوافذ
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// إعادة إنشاء النافذة عند تفعيل التطبيق (macOS)
app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createMainWindow();
  }
});