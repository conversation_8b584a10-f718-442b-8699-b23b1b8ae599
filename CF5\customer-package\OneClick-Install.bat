@echo off
title Future Fuel Corporation - One Click Installer
color 0A
mode con: cols=90 lines=35

:: تعيين الترميز
chcp 65001 >nul 2>&1

:: التحقق من صلاحيات المدير
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo.
    echo ⚠️  Administrator privileges required - مطلوب صلاحيات المدير
    echo.
    echo Right-click this file and select "Run as administrator"
    echo انقر بالزر الأيمن واختر "تشغيل كمسؤول"
    echo.
    pause
    powershell -Command "Start-Process '%~f0' -Verb RunAs"
    exit /b
)

cls
echo.
echo     ███████╗██╗   ██╗████████╗██╗   ██╗██████╗ ███████╗    ███████╗██╗   ██╗███████╗██╗     
echo     ██╔════╝██║   ██║╚══██╔══╝██║   ██║██╔══██╗██╔════╝    ██╔════╝██║   ██║██╔════╝██║     
echo     █████╗  ██║   ██║   ██║   ██║   ██║██████╔╝█████╗      █████╗  ██║   ██║█████╗  ██║     
echo     ██╔══╝  ██║   ██║   ██║   ██║   ██║██╔══██╗██╔══╝      ██╔══╝  ██║   ██║██╔══╝  ██║     
echo     ██║     ╚██████╔╝   ██║   ╚██████╔╝██║  ██║███████╗    ██║     ╚██████╔╝███████╗███████╗
echo     ╚═╝      ╚═════╝    ╚═╝    ╚═════╝ ╚═╝  ╚═╝╚══════╝    ╚═╝      ╚═════╝ ╚══════╝╚══════╝
echo.
echo ========================================================================================
echo                         🚀 ONE CLICK INSTALLER - مثبت بنقرة واحدة 🚀
echo                              Future Fuel Corporation v2.2.0
echo ========================================================================================
echo.
echo                    ✨ Professional Fuel Management System ✨
echo                    ✨ نظام إدارة الوقود الاحترافي ✨
echo.
echo ========================================================================================

timeout /t 2 /nobreak >nul

echo.
echo 🔄 Starting automatic installation... بدء التثبيت التلقائي
echo.

:: متغيرات التثبيت
set "INSTALL_DIR=C:\FutureFuel"
set "APP_NAME=Future Fuel Corporation"
set "VERSION=2.2.0"

:: إنشاء المجلدات
echo [●○○○○○○○] Creating directories... إنشاء المجلدات
mkdir "%INSTALL_DIR%" 2>nul
mkdir "%INSTALL_DIR%\app" 2>nul
mkdir "%INSTALL_DIR%\assets" 2>nul
mkdir "%INSTALL_DIR%\data" 2>nul
mkdir "%INSTALL_DIR%\backup" 2>nul
mkdir "%INSTALL_DIR%\logs" 2>nul
mkdir "%INSTALL_DIR%\temp" 2>nul
mkdir "%INSTALL_DIR%\updates" 2>nul
timeout /t 1 /nobreak >nul

:: نسخ الملفات
echo [●●○○○○○○] Copying files... نسخ الملفات
xcopy /E /I /Y "app\*" "%INSTALL_DIR%\app\" >nul 2>&1
xcopy /E /I /Y "assets\*" "%INSTALL_DIR%\assets\" >nul 2>&1
copy /Y "*.json" "%INSTALL_DIR%\" >nul 2>&1
copy /Y "*.txt" "%INSTALL_DIR%\" >nul 2>&1
copy /Y "*.vbs" "%INSTALL_DIR%\" >nul 2>&1
echo      ✓ Application files copied with login system
echo      ✓ ملفات التطبيق تم نسخها مع نظام تسجيل الدخول
timeout /t 1 /nobreak >nul

:: إنشاء المشغل
echo [●●●○○○○○] Creating launcher... إنشاء المشغل
(
echo @echo off
echo title %APP_NAME%
echo cd /d "%INSTALL_DIR%"
echo start "" "FutureFuel-Launcher.vbs"
) > "%INSTALL_DIR%\Launch.bat"
timeout /t 1 /nobreak >nul

:: إنشاء اختصار سطح المكتب
echo [●●●●○○○○] Creating shortcuts... إنشاء الاختصارات
powershell -Command ^
"$WshShell = New-Object -comObject WScript.Shell; ^
$Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\%APP_NAME%.lnk'); ^
$Shortcut.TargetPath = '%INSTALL_DIR%\Launch.bat'; ^
$Shortcut.WorkingDirectory = '%INSTALL_DIR%'; ^
$Shortcut.IconLocation = '%INSTALL_DIR%\assets\icon.ico'; ^
$Shortcut.Description = '%APP_NAME% v%VERSION%'; ^
$Shortcut.Save()"
timeout /t 1 /nobreak >nul

:: إنشاء اختصار قائمة ابدأ
echo [●●●●●○○○] Adding to Start Menu... إضافة لقائمة ابدأ
set "START_MENU=%APPDATA%\Microsoft\Windows\Start Menu\Programs"
mkdir "%START_MENU%\%APP_NAME%" 2>nul
powershell -Command ^
"$WshShell = New-Object -comObject WScript.Shell; ^
$Shortcut = $WshShell.CreateShortcut('%START_MENU%\%APP_NAME%\%APP_NAME%.lnk'); ^
$Shortcut.TargetPath = '%INSTALL_DIR%\Launch.bat'; ^
$Shortcut.WorkingDirectory = '%INSTALL_DIR%'; ^
$Shortcut.IconLocation = '%INSTALL_DIR%\assets\icon.ico'; ^
$Shortcut.Description = '%APP_NAME% v%VERSION%'; ^
$Shortcut.Save()"
timeout /t 1 /nobreak >nul

:: تسجيل في النظام
echo [●●●●●●○○] Registering application... تسجيل التطبيق
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FutureFuel" /v "DisplayName" /t REG_SZ /d "%APP_NAME%" /f >nul
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FutureFuel" /v "DisplayVersion" /t REG_SZ /d "%VERSION%" /f >nul
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FutureFuel" /v "Publisher" /t REG_SZ /d "Future Fuel Corporation" /f >nul
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FutureFuel" /v "InstallLocation" /t REG_SZ /d "%INSTALL_DIR%" /f >nul
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FutureFuel" /v "UninstallString" /t REG_SZ /d "%INSTALL_DIR%\uninstall.bat" /f >nul
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FutureFuel" /v "DisplayIcon" /t REG_SZ /d "%INSTALL_DIR%\assets\icon.ico" /f >nul
timeout /t 1 /nobreak >nul

:: إنشاء ملف إلغاء التثبيت
echo [●●●●●●●○] Creating uninstaller... إنشاء إلغاء التثبيت
(
echo @echo off
echo title %APP_NAME% - Uninstaller
echo echo Uninstalling %APP_NAME%...
echo echo إلغاء تثبيت %APP_NAME%...
echo.
echo taskkill /f /im "chrome.exe" /fi "WINDOWTITLE eq %APP_NAME%*" ^>nul 2^>^&1
echo taskkill /f /im "firefox.exe" /fi "WINDOWTITLE eq %APP_NAME%*" ^>nul 2^>^&1
echo taskkill /f /im "msedge.exe" /fi "WINDOWTITLE eq %APP_NAME%*" ^>nul 2^>^&1
echo.
echo del "%USERPROFILE%\Desktop\%APP_NAME%.lnk" ^>nul 2^>^&1
echo rmdir /s /q "%START_MENU%\%APP_NAME%" ^>nul 2^>^&1
echo.
echo reg delete "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FutureFuel" /f ^>nul 2^>^&1
echo.
echo cd /d "%TEMP%"
echo rmdir /s /q "%INSTALL_DIR%" ^>nul 2^>^&1
echo.
echo echo Uninstallation completed تم إلغاء التثبيت
echo pause
) > "%INSTALL_DIR%\uninstall.bat"
timeout /t 1 /nobreak >nul

:: إنهاء التثبيت
echo [●●●●●●●●] Finalizing... الإنهاء
(
echo {
echo   "installDate": "%DATE% %TIME%",
echo   "installPath": "%INSTALL_DIR%",
echo   "version": "%VERSION%",
echo   "installType": "OneClick",
echo   "computerName": "%COMPUTERNAME%",
echo   "userName": "%USERNAME%"
echo }
) > "%INSTALL_DIR%\install-info.json"

cls
echo.
echo     ██╗███╗   ██╗███████╗████████╗ █████╗ ██╗     ██╗     ███████╗██████╗ 
echo     ██║████╗  ██║██╔════╝╚══██╔══╝██╔══██╗██║     ██║     ██╔════╝██╔══██╗
echo     ██║██╔██╗ ██║███████╗   ██║   ███████║██║     ██║     █████╗  ██║  ██║
echo     ██║██║╚██╗██║╚════██║   ██║   ██╔══██║██║     ██║     ██╔══╝  ██║  ██║
echo     ██║██║ ╚████║███████║   ██║   ██║  ██║███████╗███████╗███████╗██████╔╝
echo     ╚═╝╚═╝  ╚═══╝╚══════╝   ╚═╝   ╚═╝  ╚═╝╚══════╝╚══════╝╚══════╝╚═════╝ 
echo.
echo ========================================================================================
echo                              🎉 INSTALLATION COMPLETE! 🎉
echo                              🎉 اكتمل التثبيت! 🎉
echo ========================================================================================
echo.
echo     ✅ Application installed successfully التطبيق تم تثبيته بنجاح
echo     ✅ Desktop shortcut created اختصار سطح المكتب تم إنشاؤه
echo     ✅ Start menu entry added إدخال قائمة ابدأ تم إضافته
echo     ✅ System registration completed تسجيل النظام اكتمل
echo.
echo ========================================================================================
echo                                   🚀 READY TO USE! 🚀
echo                                   🚀 جاهز للاستخدام! 🚀
echo ========================================================================================
echo.
echo     📍 Installation Path: %INSTALL_DIR%
echo     📍 مسار التثبيت: %INSTALL_DIR%
echo.
echo     🖥️ Desktop Shortcut: "%APP_NAME%"
echo     🖥️ اختصار سطح المكتب: "%APP_NAME%"
echo.
echo     📋 Start Menu: Programs ^> %APP_NAME%
echo     📋 قائمة ابدأ: البرامج ^> %APP_NAME%
echo.
echo ========================================================================================
echo                                   📞 SUPPORT 📞
echo                                   📞 الدعم 📞
echo ========================================================================================
echo.
echo     📧 Email: <EMAIL>
echo     📞 Phone: +966-11-123-4567
echo     🌐 Website: www.futurefuel.com
echo.
echo     Working Hours: Sun-Thu 8AM-6PM, Fri-Sat 9AM-3PM
echo     ساعات العمل: الأحد-الخميس 8ص-6م، الجمعة-السبت 9ص-3م
echo.
echo ========================================================================================

echo.
echo 🚀 Launch application now? تشغيل التطبيق الآن؟
echo.
echo Press Y to launch, any other key to exit
echo اضغط Y للتشغيل، أي مفتاح آخر للخروج
echo.
choice /c YN /n /m "Your choice خيارك: "

if errorlevel 2 goto :end
if errorlevel 1 goto :launch

:launch
echo.
echo 🚀 Launching %APP_NAME%...
echo 🚀 تشغيل %APP_NAME%...
start "" "%INSTALL_DIR%\Launch.bat"
timeout /t 2 /nobreak >nul

:end
echo.
echo ========================================================================================
echo.
echo     🎉 Thank you for choosing Future Fuel Corporation! 🎉
echo     🎉 شكراً لاختيارك مؤسسة وقود المستقبل! 🎉
echo.
echo     Your fuel management solution is ready to use!
echo     حل إدارة الوقود الخاص بك جاهز للاستخدام!
echo.
echo ========================================================================================
echo.
timeout /t 3 /nobreak >nul
