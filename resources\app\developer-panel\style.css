/* أنماط لوحة تحكم المطور */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #2ecc71;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --light-color: #ecf0f1;
    --dark-color: #2c3e50;
    --border-radius: 8px;
    --box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    direction: rtl;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* الهيدر */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: 20px;
    padding: 20px;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo-section i {
    font-size: 2rem;
    color: var(--primary-color);
}

.logo-section h1 {
    color: var(--primary-color);
    font-size: 1.8rem;
    margin: 0;
}

.subtitle {
    color: var(--secondary-color);
    font-size: 0.9rem;
    margin-top: 5px;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 20px;
}

.stats-summary {
    display: flex;
    gap: 20px;
}

.stat-item {
    text-align: center;
}

.stat-label {
    display: block;
    font-size: 0.8rem;
    color: #666;
    margin-bottom: 5px;
}

.stat-value {
    display: block;
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--primary-color);
}

.refresh-btn {
    background: var(--secondary-color);
    color: white;
    border: none;
    padding: 12px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    font-size: 1.1rem;
}

.refresh-btn:hover {
    background: #2980b9;
    transform: translateY(-2px);
}

/* التبويبات */
.tabs {
    display: flex;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: 20px;
    overflow: hidden;
}

.tab-btn {
    flex: 1;
    background: transparent;
    border: none;
    padding: 15px 20px;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-size: 1rem;
    color: #666;
    position: relative;
}

.tab-btn:hover {
    background: rgba(52, 152, 219, 0.1);
    color: var(--secondary-color);
}

.tab-btn.active {
    background: var(--secondary-color);
    color: white;
}

.badge {
    background: var(--danger-color);
    color: white;
    border-radius: 50%;
    padding: 2px 6px;
    font-size: 0.7rem;
    min-width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* المحتوى الرئيسي */
.main-content {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 30px;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid #eee;
}

.section-header h2 {
    color: var(--primary-color);
    display: flex;
    align-items: center;
    gap: 10px;
}

.section-actions {
    display: flex;
    gap: 10px;
}

/* الأزرار */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    text-decoration: none;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: #1a252f;
    transform: translateY(-2px);
}

.btn-secondary {
    background: var(--secondary-color);
    color: white;
}

.btn-secondary:hover {
    background: #2980b9;
    transform: translateY(-2px);
}

.btn-success {
    background: var(--success-color);
    color: white;
}

.btn-success:hover {
    background: #27ae60;
    transform: translateY(-2px);
}

.btn-warning {
    background: var(--warning-color);
    color: white;
}

.btn-warning:hover {
    background: #e67e22;
    transform: translateY(-2px);
}

.btn-danger {
    background: var(--danger-color);
    color: white;
}

.btn-danger:hover {
    background: #c0392b;
    transform: translateY(-2px);
}

/* شريط التصفية */
.filter-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: var(--border-radius);
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.filter-group label {
    font-weight: 600;
    color: var(--primary-color);
}

.filter-group select,
.search-group input {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    font-size: 0.9rem;
}

.search-group {
    display: flex;
    gap: 5px;
}

.search-btn {
    background: var(--secondary-color);
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: var(--border-radius);
    cursor: pointer;
}

/* الجداول */
.table-container {
    overflow-x: auto;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

table {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

th, td {
    padding: 12px;
    text-align: right;
    border-bottom: 1px solid #eee;
}

th {
    background: var(--primary-color);
    color: white;
    font-weight: 600;
    position: sticky;
    top: 0;
}

tr:hover {
    background: #f8f9fa;
}

/* النوافذ المنبثقة */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

.modal.show {
    display: flex;
}

.modal-content {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #eee;
}

.modal-header h3 {
    color: var(--primary-color);
    margin: 0;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #999;
}

.close-btn:hover {
    color: var(--danger-color);
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    padding: 20px;
    border-top: 1px solid #eee;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

/* مولد التراخيص */
.generator-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    align-items: start;
}

.generator-form {
    background: #f8f9fa;
    padding: 25px;
    border-radius: var(--border-radius);
    border: 1px solid #eee;
}

.generator-form h3 {
    color: var(--primary-color);
    margin-bottom: 20px;
    text-align: center;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--primary-color);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.custom-duration {
    display: none;
}

.generated-license {
    background: #f8f9fa;
    padding: 25px;
    border-radius: var(--border-radius);
    border: 1px solid #eee;
}

.generated-license h3 {
    color: var(--success-color);
    margin-bottom: 20px;
    text-align: center;
}

.license-display textarea {
    width: 100%;
    height: 200px;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    padding: 15px;
    resize: vertical;
}

.license-actions {
    display: flex;
    gap: 10px;
    margin-top: 15px;
}

/* حالات الطلبات */
.status-pending {
    background: var(--warning-color);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
}

.status-approved {
    background: var(--success-color);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
}

.status-rejected {
    background: var(--danger-color);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
}

.status-active {
    background: var(--success-color);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
}

.status-expired {
    background: var(--danger-color);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
}

/* تفاصيل الطلب */
.request-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.detail-group {
    background: #f8f9fa;
    padding: 15px;
    border-radius: var(--border-radius);
}

.detail-group h4 {
    color: var(--primary-color);
    margin-bottom: 10px;
    border-bottom: 1px solid #ddd;
    padding-bottom: 5px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
}

.detail-label {
    font-weight: 600;
    color: #666;
}

.detail-value {
    color: var(--primary-color);
}

/* الرسائل والتنبيهات */
.alert {
    padding: 15px;
    border-radius: var(--border-radius);
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.alert-success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.alert-warning {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.alert-danger {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.alert-info {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    .header-content {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .tabs {
        flex-direction: column;
    }

    .generator-container {
        grid-template-columns: 1fr;
    }

    .request-details {
        grid-template-columns: 1fr;
    }

    .filter-bar {
        flex-direction: column;
        gap: 15px;
    }

    .section-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .section-actions {
        flex-wrap: wrap;
        justify-content: center;
    }
}
