@echo off
chcp 65001 >nul
title مؤسسة وقود المستقبل - إلغاء التثبيت
color 0C

echo.
echo ================================================================================
echo                    مؤسسة وقود المستقبل - إلغاء التثبيت
echo                    Future Fuel Corporation - Uninstaller
echo ================================================================================
echo.
echo ⚠️ تحذير: سيتم إلغاء تثبيت نظام مؤسسة وقود المستقبل نهائياً
echo.
echo سيتم حذف:
echo - جميع ملفات التطبيق
echo - الاختصارات من سطح المكتب وقائمة ابدأ
echo - إعدادات النظام
echo.
echo ملاحظة: سيتم الاحتفاظ بالبيانات والنسخ الاحتياطية
echo.
echo ================================================================================
echo.

set /p "CONFIRM1=هل أنت متأكد من إلغاء التثبيت؟ (Y/N): "
if /i not "%CONFIRM1%"=="Y" if /i not "%CONFIRM1%"=="نعم" (
    echo تم إلغاء العملية
    pause
    exit /b 0
)

echo.
set /p "CONFIRM2=تأكيد نهائي - سيتم حذف جميع الملفات (Y/N): "
if /i not "%CONFIRM2%"=="Y" if /i not "%CONFIRM2%"=="نعم" (
    echo تم إلغاء العملية
    pause
    exit /b 0
)

echo.
echo ================================================================================
echo                              بدء إلغاء التثبيت
echo ================================================================================
echo.

:: الحصول على مجلد التثبيت
set "INSTALL_DIR=%~dp0"
set "INSTALL_DIR=%INSTALL_DIR:~0,-1%"

echo 📁 مجلد التثبيت: %INSTALL_DIR%
echo.

:: إنشاء نسخة احتياطية من البيانات
echo 💾 إنشاء نسخة احتياطية من البيانات...
set "BACKUP_DIR=%USERPROFILE%\Documents\FutureFuel_Backup_%DATE:~-4%%DATE:~3,2%%DATE:~0,2%"
if exist "%INSTALL_DIR%\data" (
    mkdir "%BACKUP_DIR%" 2>nul
    xcopy /E /I /Y "%INSTALL_DIR%\data\*" "%BACKUP_DIR%\data\" >nul
    xcopy /E /I /Y "%INSTALL_DIR%\backup\*" "%BACKUP_DIR%\backup\" >nul
    echo ✓ تم حفظ البيانات في: %BACKUP_DIR%
) else (
    echo ⚠️ لم يتم العثور على بيانات للنسخ الاحتياطي
)

:: إغلاق التطبيق إذا كان يعمل
echo.
echo 🔄 إغلاق التطبيق...
taskkill /f /im "chrome.exe" /fi "WINDOWTITLE eq مؤسسة وقود المستقبل*" >nul 2>&1
taskkill /f /im "firefox.exe" /fi "WINDOWTITLE eq مؤسسة وقود المستقبل*" >nul 2>&1
taskkill /f /im "msedge.exe" /fi "WINDOWTITLE eq مؤسسة وقود المستقبل*" >nul 2>&1
echo ✓ تم إغلاق التطبيق

:: حذف الاختصارات
echo.
echo 🗑️ حذف الاختصارات...
del "%USERPROFILE%\Desktop\مؤسسة وقود المستقبل.url" >nul 2>&1
del "%USERPROFILE%\Desktop\مؤسسة وقود المستقبل.lnk" >nul 2>&1
rmdir /s /q "%APPDATA%\Microsoft\Windows\Start Menu\Programs\مؤسسة وقود المستقبل" >nul 2>&1
echo ✓ تم حذف الاختصارات

:: إلغاء تسجيل البرنامج من النظام
echo.
echo 📝 إلغاء تسجيل البرنامج من النظام...
reg delete "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FutureFuel" /f >nul 2>&1
echo ✓ تم إلغاء تسجيل البرنامج

:: حذف ملفات التطبيق
echo.
echo 🗂️ حذف ملفات التطبيق...
cd /d "%TEMP%"

:: حذف الملفات والمجلدات
rmdir /s /q "%INSTALL_DIR%\app" >nul 2>&1
rmdir /s /q "%INSTALL_DIR%\assets" >nul 2>&1
rmdir /s /q "%INSTALL_DIR%\logs" >nul 2>&1
rmdir /s /q "%INSTALL_DIR%\updates" >nul 2>&1

del "%INSTALL_DIR%\FutureFuel.bat" >nul 2>&1
del "%INSTALL_DIR%\config.json" >nul 2>&1
del "%INSTALL_DIR%\license.txt" >nul 2>&1
del "%INSTALL_DIR%\README.txt" >nul 2>&1
del "%INSTALL_DIR%\install-info.json" >nul 2>&1

echo ✓ تم حذف ملفات التطبيق

:: سؤال عن حذف البيانات
echo.
set /p "DELETE_DATA=هل تريد حذف البيانات والنسخ الاحتياطية أيضاً؟ (Y/N): "
if /i "%DELETE_DATA%"=="Y" if /i "%DELETE_DATA%"=="نعم" (
    rmdir /s /q "%INSTALL_DIR%\data" >nul 2>&1
    rmdir /s /q "%INSTALL_DIR%\backup" >nul 2>&1
    echo ✓ تم حذف البيانات والنسخ الاحتياطية
) else (
    echo ✓ تم الاحتفاظ بالبيانات والنسخ الاحتياطية
)

:: حذف ملف إلغاء التثبيت نفسه
echo.
echo 🧹 تنظيف نهائي...
(
echo @echo off
echo timeout /t 2 /nobreak ^>nul
echo rmdir /s /q "%INSTALL_DIR%" ^>nul 2^>^&1
echo echo تم إلغاء تثبيت مؤسسة وقود المستقبل بنجاح
echo echo.
echo echo تم حفظ نسخة احتياطية من البيانات في:
echo echo %BACKUP_DIR%
echo echo.
echo echo شكراً لاستخدامك نظام مؤسسة وقود المستقبل
echo echo.
echo pause
echo del "%%~f0"
) > "%TEMP%\cleanup_futurefuel.bat"

echo.
echo ================================================================================
echo                              اكتمل إلغاء التثبيت
echo ================================================================================
echo.
echo ✅ تم إلغاء تثبيت نظام مؤسسة وقود المستقبل بنجاح
echo.
echo 💾 النسخة الاحتياطية محفوظة في:
echo    %BACKUP_DIR%
echo.
echo 📧 للمساعدة أو إعادة التثبيت:
echo    <EMAIL>
echo    +966-11-123-4567
echo.
echo شكراً لاستخدامك نظام مؤسسة وقود المستقبل
echo نأمل أن نراك مرة أخرى قريباً!
echo.
echo ================================================================================
echo.

pause

:: تشغيل ملف التنظيف النهائي
start "" "%TEMP%\cleanup_futurefuel.bat"
