<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مولد التراخيص - License Generator</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            direction: rtl;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #eee;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-label {
            display: block;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
            font-size: 1.1rem;
        }
        .form-input, .form-select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }
        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: #3498db;
            background: white;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }
        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 5px;
        }
        .btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
        .btn-success { background: #27ae60; }
        .btn-success:hover { background: #229954; }
        .btn-warning { background: #f39c12; }
        .btn-warning:hover { background: #e67e22; }
        .result-box {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
        }
        .license-code {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 8px;
            font-size: 1.2rem;
            font-weight: bold;
            text-align: center;
            margin: 10px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .license-code:hover {
            background: #34495e;
            transform: scale(1.02);
        }
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .info-item {
            background: #ecf0f1;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .instructions {
            background: #e8f4fd;
            border-left: 4px solid #3498db;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 10px 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-key"></i> مولد التراخيص</h1>
            <h2>License Generator</h2>
            <p>إنشاء تراخيص جديدة لعملاء مؤسسة وقود المستقبل</p>
        </div>

        <div class="instructions">
            <h3><i class="fas fa-info-circle"></i> تعليمات الاستخدام:</h3>
            <ol>
                <li>املأ بيانات الترخيص المطلوبة</li>
                <li>انقر على "إنشاء ترخيص"</li>
                <li>انسخ كود الترخيص المُنشأ</li>
                <li>أرسله للعميل أو أضفه للنظام</li>
            </ol>
        </div>

        <form id="licenseForm">
            <div class="form-group">
                <label class="form-label">نوع الترخيص</label>
                <select id="licenseType" class="form-select" required>
                    <option value="">اختر نوع الترخيص</option>
                    <option value="demo">تجريبي (30 يوم)</option>
                    <option value="monthly">شهري (30 يوم)</option>
                    <option value="quarterly">ربع سنوي (90 يوم)</option>
                    <option value="yearly">سنوي (365 يوم)</option>
                    <option value="lifetime">مدى الحياة (10 سنوات)</option>
                    <option value="admin">إداري (مدى الحياة)</option>
                </select>
            </div>

            <div class="form-group">
                <label class="form-label">اسم العميل (اختياري)</label>
                <input type="text" id="customerName" class="form-input" placeholder="أدخل اسم العميل">
            </div>

            <div class="form-group">
                <label class="form-label">مدة الصلاحية (بالأيام)</label>
                <input type="number" id="duration" class="form-input" placeholder="سيتم تعيينها تلقائياً حسب النوع" readonly>
            </div>

            <div class="form-group">
                <label class="form-label">ملاحظات (اختياري)</label>
                <input type="text" id="notes" class="form-input" placeholder="أي ملاحظات إضافية">
            </div>

            <div class="form-group">
                <button type="button" class="btn btn-success" onclick="generateLicense()">
                    <i class="fas fa-plus"></i> إنشاء ترخيص
                </button>
                <button type="button" class="btn btn-warning" onclick="generateRandomLicense()">
                    <i class="fas fa-random"></i> إنشاء ترخيص عشوائي
                </button>
                <button type="button" class="btn" onclick="clearForm()">
                    <i class="fas fa-eraser"></i> مسح النموذج
                </button>
            </div>
        </form>

        <div id="result" style="display: none;">
            <h3><i class="fas fa-check-circle"></i> تم إنشاء الترخيص بنجاح!</h3>
            
            <div class="license-code" id="licenseCode" onclick="copyLicenseCode()">
                انقر هنا لنسخ كود الترخيص
            </div>

            <div class="info-grid">
                <div class="info-item">
                    <strong>نوع الترخيص</strong>
                    <div id="displayType"></div>
                </div>
                <div class="info-item">
                    <strong>تاريخ الإنشاء</strong>
                    <div id="displayCreated"></div>
                </div>
                <div class="info-item">
                    <strong>تاريخ الانتهاء</strong>
                    <div id="displayExpiry"></div>
                </div>
                <div class="info-item">
                    <strong>المدة</strong>
                    <div id="displayDuration"></div>
                </div>
            </div>

            <div class="result-box">
                <h4>معلومات الترخيص الكاملة:</h4>
                <pre id="licenseDetails"></pre>
            </div>

            <div class="form-group">
                <button type="button" class="btn btn-success" onclick="addToSystem()">
                    <i class="fas fa-plus-circle"></i> إضافة للنظام
                </button>
                <button type="button" class="btn" onclick="downloadLicense()">
                    <i class="fas fa-download"></i> تحميل كملف
                </button>
            </div>
        </div>

        <div id="systemResult" style="display: none;"></div>
    </div>

    <script>
        let currentLicense = null;

        // تحديث المدة عند تغيير نوع الترخيص
        document.getElementById('licenseType').addEventListener('change', function() {
            const type = this.value;
            const durationInput = document.getElementById('duration');
            
            const durations = {
                'demo': 30,
                'monthly': 30,
                'quarterly': 90,
                'yearly': 365,
                'lifetime': 3650,
                'admin': 3650
            };
            
            durationInput.value = durations[type] || '';
        });

        function generateLicenseCode(type) {
            const prefixes = {
                'demo': 'DEMO',
                'monthly': 'MNTH',
                'quarterly': 'QRTR',
                'yearly': 'YEAR',
                'lifetime': 'LIFE',
                'admin': 'ADMN'
            };
            
            const year = new Date().getFullYear();
            const month = String(new Date().getMonth() + 1).padStart(2, '0');
            const random1 = Math.random().toString(36).substr(2, 4).toUpperCase();
            const random2 = Math.random().toString(36).substr(2, 4).toUpperCase();
            
            return `${prefixes[type]}-${year}${month}-${random1}-${random2}`;
        }

        function generateLicense() {
            const type = document.getElementById('licenseType').value;
            const customerName = document.getElementById('customerName').value;
            const duration = parseInt(document.getElementById('duration').value);
            const notes = document.getElementById('notes').value;

            if (!type) {
                alert('يرجى اختيار نوع الترخيص');
                return;
            }

            const licenseCode = generateLicenseCode(type);
            const createdAt = new Date();
            const expiresAt = new Date(createdAt.getTime() + duration * 24 * 60 * 60 * 1000);

            currentLicense = {
                id: 'LIC-' + Date.now(),
                code: licenseCode,
                type: type,
                status: 'active',
                expiresAt: expiresAt.toISOString(),
                isActive: true,
                deviceId: null,
                activatedAt: null,
                createdAt: createdAt.toISOString(),
                notes: notes || `ترخيص ${getTypeDisplayName(type)}`,
                maxDevices: 1,
                features: getFeaturesByType(type),
                createdBy: 'license-generator',
                lastModified: createdAt.toISOString(),
                customerName: customerName || 'غير محدد',
                duration: duration
            };

            displayLicense();
        }

        function generateRandomLicense() {
            const types = ['demo', 'monthly', 'yearly', 'lifetime'];
            const randomType = types[Math.floor(Math.random() * types.length)];
            
            document.getElementById('licenseType').value = randomType;
            document.getElementById('licenseType').dispatchEvent(new Event('change'));
            document.getElementById('customerName').value = 'عميل تجريبي ' + Math.floor(Math.random() * 1000);
            document.getElementById('notes').value = 'ترخيص تم إنشاؤه تلقائياً';
            
            generateLicense();
        }

        function getTypeDisplayName(type) {
            const names = {
                'demo': 'تجريبي',
                'monthly': 'شهري',
                'quarterly': 'ربع سنوي',
                'yearly': 'سنوي',
                'lifetime': 'مدى الحياة',
                'admin': 'إداري'
            };
            return names[type] || type;
        }

        function getFeaturesByType(type) {
            if (type === 'demo') return ['basic', 'reports'];
            if (type === 'admin') return ['all', 'admin'];
            return ['all'];
        }

        function displayLicense() {
            if (!currentLicense) return;

            document.getElementById('result').style.display = 'block';
            document.getElementById('licenseCode').textContent = currentLicense.code;
            document.getElementById('displayType').textContent = getTypeDisplayName(currentLicense.type);
            document.getElementById('displayCreated').textContent = new Date(currentLicense.createdAt).toLocaleDateString('ar-SA');
            document.getElementById('displayExpiry').textContent = new Date(currentLicense.expiresAt).toLocaleDateString('ar-SA');
            document.getElementById('displayDuration').textContent = currentLicense.duration + ' يوم';

            document.getElementById('licenseDetails').textContent = JSON.stringify(currentLicense, null, 2);
        }

        function copyLicenseCode() {
            if (!currentLicense) return;

            navigator.clipboard.writeText(currentLicense.code).then(() => {
                const element = document.getElementById('licenseCode');
                const originalText = element.textContent;
                element.textContent = '✅ تم النسخ!';
                element.style.background = '#27ae60';
                
                setTimeout(() => {
                    element.textContent = originalText;
                    element.style.background = '#2c3e50';
                }, 2000);
            }).catch(() => {
                alert('فشل في نسخ الكود. يرجى النسخ يدوياً: ' + currentLicense.code);
            });
        }

        function addToSystem() {
            if (!currentLicense) return;

            try {
                // الحصول على التراخيص الحالية
                const existingLicenses = JSON.parse(localStorage.getItem('validLicenses') || '[]');
                
                // إضافة الترخيص الجديد
                existingLicenses.push(currentLicense);
                
                // حفظ التراخيص المحدثة
                localStorage.setItem('validLicenses', JSON.stringify(existingLicenses));
                
                // عرض رسالة نجاح
                const resultDiv = document.getElementById('systemResult');
                resultDiv.innerHTML = `
                    <div class="result-box success">
                        <h4><i class="fas fa-check-circle"></i> تم إضافة الترخيص للنظام بنجاح!</h4>
                        <p>كود الترخيص: <strong>${currentLicense.code}</strong></p>
                        <p>يمكن للعميل الآن استخدام هذا الكود لتسجيل الدخول</p>
                    </div>
                `;
                resultDiv.style.display = 'block';
                
            } catch (error) {
                const resultDiv = document.getElementById('systemResult');
                resultDiv.innerHTML = `
                    <div class="result-box error">
                        <h4><i class="fas fa-exclamation-circle"></i> فشل في إضافة الترخيص</h4>
                        <p>حدث خطأ: ${error.message}</p>
                    </div>
                `;
                resultDiv.style.display = 'block';
            }
        }

        function downloadLicense() {
            if (!currentLicense) return;

            const content = `
# ترخيص مؤسسة وقود المستقبل
# Future Fuel Corporation License

كود الترخيص: ${currentLicense.code}
نوع الترخيص: ${getTypeDisplayName(currentLicense.type)}
اسم العميل: ${currentLicense.customerName}
تاريخ الإنشاء: ${new Date(currentLicense.createdAt).toLocaleDateString('ar-SA')}
تاريخ الانتهاء: ${new Date(currentLicense.expiresAt).toLocaleDateString('ar-SA')}
المدة: ${currentLicense.duration} يوم
الملاحظات: ${currentLicense.notes}

تعليمات الاستخدام:
1. افتح تطبيق مؤسسة وقود المستقبل
2. أدخل كود الترخيص في حقل تسجيل الدخول
3. انقر على تسجيل الدخول

للدعم الفني: <EMAIL>
            `;

            const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `license-${currentLicense.code}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        function clearForm() {
            document.getElementById('licenseForm').reset();
            document.getElementById('result').style.display = 'none';
            document.getElementById('systemResult').style.display = 'none';
            currentLicense = null;
        }
    </script>
</body>
</html>
