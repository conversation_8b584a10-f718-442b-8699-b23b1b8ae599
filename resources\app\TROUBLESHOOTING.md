# دليل استكشاف الأخطاء - نظام الترخيص

## المشاكل الشائعة وحلولها

### 1. طلب التفعيل لا يظهر في لوحة التحكم

#### الأعراض:
- تم إرسال طلب التفعيل بنجاح
- لا يظهر الطلب في لوحة تحكم المطور

#### الحلول:

**أ) تحديث لوحة التحكم:**
1. فتح لوحة التحكم
2. النقر على زر "تحديث" (🔄)
3. التحقق من ظهور الطلب

**ب) فحص localStorage:**
1. فتح أدوات المطور في المتصفح (F12)
2. الذهاب إلى تبويب "Application" أو "Storage"
3. البحث عن `developerPanel_activationRequests`
4. التحقق من وجود البيانات

**ج) استخدام صفحة الاختبار:**
1. فتح `test-license-system.html`
2. النقر على "عرض جميع الطلبات"
3. التحقق من وجود الطلبات

**د) إعادة تشغيل التطبيق:**
1. إغلاق التطبيق بالكامل
2. إعادة تشغيله
3. إرسال طلب تفعيل جديد

---

### 2. مفتاح الترخيص غير صالح

#### الأعراض:
- رسالة "مفتاح الترخيص غير صالح"
- عدم قبول الترخيص المُرسل

#### الحلول:

**أ) التحقق من نسخ المفتاح:**
1. التأكد من نسخ المفتاح كاملاً
2. عدم وجود مسافات إضافية
3. عدم وجود أحرف مفقودة

**ب) التحقق من معرف الجهاز:**
1. نسخ معرف الجهاز من واجهة تسجيل الدخول
2. التأكد من أن الترخيص مُصدر لنفس المعرف
3. مقارنة المعرف في لوحة التحكم

**ج) إنشاء ترخيص جديد:**
1. فتح لوحة التحكم
2. الذهاب إلى "مولد التراخيص"
3. إدخال معرف الجهاز الصحيح
4. توليد ترخيص جديد

---

### 3. الترخيص غير متطابق مع معرف الجهاز

#### الأعراض:
- رسالة "الترخيص غير متطابق مع معرف الجهاز"

#### الحلول:

**أ) التحقق من المعرف:**
```
1. فتح واجهة تسجيل الدخول
2. نسخ معرف الجهاز المعروض
3. مقارنته مع المعرف في الترخيص
```

**ب) إنشاء ترخيص جديد:**
```
1. استخدام معرف الجهاز الصحيح
2. توليد ترخيص جديد من لوحة التحكم
3. إرسال الترخيص الجديد للعميل
```

---

### 4. لوحة التحكم لا تفتح

#### الأعراض:
- خطأ عند فتح `developer-panel/index.html`
- صفحة فارغة أو خطأ JavaScript

#### الحلول:

**أ) التحقق من الملفات:**
```
resources/app/developer-panel/
├── index.html
├── script.js
├── style.css
└── start-panel.bat
```

**ب) فتح من المتصفح مباشرة:**
1. فتح المتصفح
2. سحب ملف `index.html` إلى المتصفح
3. أو استخدام `file://` protocol

**ج) فحص أخطاء JavaScript:**
1. فتح أدوات المطور (F12)
2. فحص تبويب "Console"
3. البحث عن أخطاء JavaScript

---

### 5. البيانات لا تُحفظ

#### الأعراض:
- فقدان البيانات عند إعادة التشغيل
- عدم ظهور الطلبات أو التراخيص

#### الحلول:

**أ) فحص localStorage:**
```javascript
// في console المتصفح
console.log(localStorage.getItem('developerPanel_activationRequests'));
console.log(localStorage.getItem('appLicense'));
```

**ب) التحقق من أذونات المتصفح:**
1. التأكد من تمكين localStorage
2. عدم استخدام وضع التصفح الخاص
3. مسح cache المتصفح إذا لزم الأمر

---

### 6. خطأ في تشغيل التطبيق

#### الأعراض:
- التطبيق لا يبدأ
- خطأ في Electron

#### الحلول:

**أ) التحقق من Node.js:**
```bash
node --version
npm --version
```

**ب) إعادة تثبيت التبعيات:**
```bash
cd resources/app
npm install
```

**ج) تشغيل التطبيق:**
```bash
npm start
```

---

## أدوات التشخيص

### 1. صفحة الاختبار
فتح `test-license-system.html` لاختبار جميع الوظائف

### 2. فحص localStorage
```javascript
// عرض جميع البيانات المحفوظة
for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    console.log(key, localStorage.getItem(key));
}
```

### 3. تصدير البيانات
استخدام زر "تصدير جميع البيانات" في صفحة الاختبار

### 4. إعادة تعيين النظام
استخدام زر "إعادة تعيين النظام" لحذف جميع البيانات

---

## معلومات الاتصال للدعم

**المطور:** ISHQK  
**الهاتف/واتساب:** **********  
**ساعات العمل:** 8:00 صباحاً - 8:00 مساءً

---

## ملفات السجلات

### في Electron:
- `%APPDATA%/gas-shop-management/logs/`

### في المتصفح:
- أدوات المطور > Console

---

## نصائح للوقاية

1. **نسخ احتياطية منتظمة:**
   - تصدير البيانات أسبوعياً
   - حفظ ملفات النسخ الاحتياطي

2. **تحديث منتظم:**
   - تحديث لوحة التحكم دورياً
   - فحص الطلبات الجديدة

3. **توثيق التراخيص:**
   - حفظ نسخة من كل ترخيص مُصدر
   - تسجيل معرف الجهاز لكل عميل

4. **اختبار دوري:**
   - استخدام صفحة الاختبار شهرياً
   - التأكد من عمل جميع الوظائف
