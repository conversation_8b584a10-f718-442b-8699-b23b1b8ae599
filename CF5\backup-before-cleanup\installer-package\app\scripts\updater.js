// Future Fuel Management System - Auto Updater
// Version 2.2.0 - Automatic update management

class AutoUpdater {
    constructor() {
        this.currentVersion = '2.2.0';
        this.updateCheckInterval = 24 * 60 * 60 * 1000; // 24 hours
        this.updateServer = 'https://futurefuel.sa/updates';
        this.lastCheckTime = localStorage.getItem('lastUpdateCheck');
        this.autoCheckEnabled = localStorage.getItem('autoUpdateCheck') !== 'false';
        
        this.init();
    }

    init() {
        if (this.autoCheckEnabled) {
            this.scheduleUpdateCheck();
        }
        
        // Check for updates on startup if it's been more than 24 hours
        if (this.shouldCheckForUpdates()) {
            setTimeout(() => this.checkForUpdates(), 5000);
        }
    }

    shouldCheckForUpdates() {
        if (!this.lastCheckTime) return true;
        
        const lastCheck = new Date(this.lastCheckTime);
        const now = new Date();
        return (now - lastCheck) > this.updateCheckInterval;
    }

    scheduleUpdateCheck() {
        setInterval(() => {
            this.checkForUpdates();
        }, this.updateCheckInterval);
    }

    async checkForUpdates(showNoUpdateMessage = false) {
        try {
            console.log('Checking for updates...');
            
            // Update last check time
            localStorage.setItem('lastUpdateCheck', new Date().toISOString());
            
            // In a real implementation, this would check a remote server
            // For now, we'll simulate the check
            const updateInfo = await this.fetchUpdateInfo();
            
            if (updateInfo && this.isNewerVersion(updateInfo.version)) {
                this.showUpdateAvailable(updateInfo);
            } else if (showNoUpdateMessage) {
                notificationManager.info('التحديثات', 'التطبيق محدث إلى أحدث إصدار');
            }
            
        } catch (error) {
            console.error('Error checking for updates:', error);
            if (showNoUpdateMessage) {
                notificationManager.warning('التحديثات', 'تعذر التحقق من التحديثات');
            }
        }
    }

    async fetchUpdateInfo() {
        // Simulate fetching update info
        // In a real implementation, this would make an HTTP request
        return new Promise((resolve) => {
            setTimeout(() => {
                // Simulate no update available for now
                resolve(null);
                
                // Uncomment to simulate update available:
                // resolve({
                //     version: '2.3.0',
                //     releaseNotes: 'إصدار جديد مع تحسينات وميزات جديدة',
                //     downloadUrl: 'https://futurefuel.sa/downloads/v2.3.0',
                //     mandatory: false,
                //     size: '15.2 MB'
                // });
            }, 1000);
        });
    }

    isNewerVersion(remoteVersion) {
        const current = this.parseVersion(this.currentVersion);
        const remote = this.parseVersion(remoteVersion);
        
        for (let i = 0; i < Math.max(current.length, remote.length); i++) {
            const currentPart = current[i] || 0;
            const remotePart = remote[i] || 0;
            
            if (remotePart > currentPart) return true;
            if (remotePart < currentPart) return false;
        }
        
        return false;
    }

    parseVersion(version) {
        return version.split('.').map(part => parseInt(part, 10));
    }

    showUpdateAvailable(updateInfo) {
        const modal = this.createUpdateModal(updateInfo);
        document.body.appendChild(modal);
        
        // Show notification
        notificationManager.info(
            'تحديث متوفر',
            `الإصدار ${updateInfo.version} متوفر الآن`,
            {
                persistent: true,
                actions: [
                    {
                        label: 'عرض التفاصيل',
                        callback: `document.getElementById('update-modal').style.display = 'flex'`
                    }
                ]
            }
        );
    }

    createUpdateModal(updateInfo) {
        const modal = document.createElement('div');
        modal.id = 'update-modal';
        modal.className = 'update-modal';
        
        modal.innerHTML = `
            <div class="update-modal-content">
                <div class="update-modal-header">
                    <h3>🚀 تحديث متوفر</h3>
                    <button class="update-modal-close" onclick="this.closest('.update-modal').remove()">&times;</button>
                </div>
                
                <div class="update-modal-body">
                    <div class="update-info">
                        <div class="update-version">
                            <strong>الإصدار الجديد:</strong> ${updateInfo.version}
                        </div>
                        <div class="update-current">
                            <strong>الإصدار الحالي:</strong> ${this.currentVersion}
                        </div>
                        <div class="update-size">
                            <strong>حجم التحديث:</strong> ${updateInfo.size || 'غير محدد'}
                        </div>
                    </div>
                    
                    <div class="update-notes">
                        <h4>ملاحظات الإصدار:</h4>
                        <div class="release-notes">
                            ${updateInfo.releaseNotes || 'تحسينات وإصلاحات عامة'}
                        </div>
                    </div>
                    
                    ${updateInfo.mandatory ? 
                        '<div class="update-mandatory">⚠️ هذا التحديث إجباري ومطلوب للاستمرار</div>' : 
                        ''
                    }
                </div>
                
                <div class="update-modal-footer">
                    <button class="btn btn-primary" onclick="autoUpdater.downloadUpdate('${updateInfo.downloadUrl}')">
                        تحديث الآن
                    </button>
                    ${!updateInfo.mandatory ? 
                        '<button class="btn btn-secondary" onclick="autoUpdater.remindLater()">تذكيرني لاحقاً</button>' : 
                        ''
                    }
                    ${!updateInfo.mandatory ? 
                        '<button class="btn btn-text" onclick="autoUpdater.skipVersion(\'${updateInfo.version}\')">تخطي هذا الإصدار</button>' : 
                        ''
                    }
                </div>
            </div>
        `;
        
        // Add styles
        this.addUpdateModalStyles();
        
        return modal;
    }

    addUpdateModalStyles() {
        if (document.getElementById('update-modal-styles')) return;
        
        const styles = document.createElement('style');
        styles.id = 'update-modal-styles';
        styles.textContent = `
            .update-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10001;
            }

            .update-modal-content {
                background: white;
                border-radius: 12px;
                max-width: 500px;
                width: 90%;
                max-height: 80vh;
                overflow-y: auto;
                box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            }

            .update-modal-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 20px;
                border-bottom: 1px solid #eee;
            }

            .update-modal-header h3 {
                margin: 0;
                color: #333;
                font-size: 18px;
            }

            .update-modal-close {
                background: none;
                border: none;
                font-size: 24px;
                cursor: pointer;
                color: #999;
                padding: 0;
                width: 30px;
                height: 30px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
            }

            .update-modal-close:hover {
                background: #f5f5f5;
                color: #666;
            }

            .update-modal-body {
                padding: 20px;
            }

            .update-info {
                background: #f8f9fa;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 20px;
            }

            .update-info > div {
                margin-bottom: 8px;
            }

            .update-info > div:last-child {
                margin-bottom: 0;
            }

            .update-notes h4 {
                margin: 0 0 10px 0;
                color: #333;
                font-size: 14px;
            }

            .release-notes {
                background: #fff;
                border: 1px solid #ddd;
                border-radius: 6px;
                padding: 12px;
                font-size: 13px;
                line-height: 1.5;
                color: #666;
                max-height: 150px;
                overflow-y: auto;
            }

            .update-mandatory {
                background: #fff3cd;
                border: 1px solid #ffeaa7;
                border-radius: 6px;
                padding: 12px;
                margin-top: 15px;
                color: #856404;
                font-size: 13px;
            }

            .update-modal-footer {
                padding: 20px;
                border-top: 1px solid #eee;
                display: flex;
                gap: 10px;
                justify-content: flex-end;
            }

            .update-modal-footer .btn {
                padding: 8px 16px;
                border-radius: 6px;
                border: none;
                cursor: pointer;
                font-size: 13px;
                transition: all 0.2s;
            }

            .update-modal-footer .btn-primary {
                background: #007bff;
                color: white;
            }

            .update-modal-footer .btn-primary:hover {
                background: #0056b3;
            }

            .update-modal-footer .btn-secondary {
                background: #6c757d;
                color: white;
            }

            .update-modal-footer .btn-secondary:hover {
                background: #545b62;
            }

            .update-modal-footer .btn-text {
                background: none;
                color: #6c757d;
                text-decoration: underline;
            }

            .update-modal-footer .btn-text:hover {
                color: #495057;
            }

            /* Dark mode support */
            .dark-mode .update-modal-content {
                background: var(--surface-color);
                color: var(--text-color);
            }

            .dark-mode .update-modal-header {
                border-bottom-color: var(--border-color);
            }

            .dark-mode .update-modal-header h3 {
                color: var(--text-color);
            }

            .dark-mode .update-info {
                background: var(--background-color);
            }

            .dark-mode .release-notes {
                background: var(--background-color);
                border-color: var(--border-color);
                color: var(--text-secondary);
            }

            .dark-mode .update-modal-footer {
                border-top-color: var(--border-color);
            }
        `;
        
        document.head.appendChild(styles);
    }

    async downloadUpdate(downloadUrl) {
        // Close modal
        const modal = document.getElementById('update-modal');
        if (modal) modal.remove();
        
        // Show download progress
        notificationManager.info(
            'جاري التحديث',
            'يتم تحميل التحديث... يرجى عدم إغلاق التطبيق',
            { persistent: true }
        );
        
        try {
            // In a real implementation, this would download and install the update
            // For now, we'll simulate the process
            await this.simulateDownload();
            
            notificationManager.success(
                'تم التحديث',
                'تم تحميل التحديث بنجاح. سيتم إعادة تشغيل التطبيق',
                { persistent: true }
            );
            
            // Simulate restart
            setTimeout(() => {
                window.location.reload();
            }, 3000);
            
        } catch (error) {
            notificationManager.error(
                'فشل التحديث',
                'تعذر تحميل التحديث. يرجى المحاولة لاحقاً'
            );
        }
    }

    async simulateDownload() {
        return new Promise((resolve) => {
            setTimeout(resolve, 3000);
        });
    }

    remindLater() {
        const modal = document.getElementById('update-modal');
        if (modal) modal.remove();
        
        // Set reminder for 24 hours later
        localStorage.setItem('updateReminder', new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString());
        
        notificationManager.info('تذكير', 'سيتم تذكيرك بالتحديث غداً');
    }

    skipVersion(version) {
        const modal = document.getElementById('update-modal');
        if (modal) modal.remove();
        
        // Add to skipped versions
        const skipped = JSON.parse(localStorage.getItem('skippedVersions') || '[]');
        if (!skipped.includes(version)) {
            skipped.push(version);
            localStorage.setItem('skippedVersions', JSON.stringify(skipped));
        }
        
        notificationManager.info('تم التخطي', `تم تخطي الإصدار ${version}`);
    }

    // Manual update check
    manualUpdateCheck() {
        notificationManager.info('التحديثات', 'جاري البحث عن التحديثات...');
        this.checkForUpdates(true);
    }

    // Settings
    enableAutoUpdates() {
        this.autoCheckEnabled = true;
        localStorage.setItem('autoUpdateCheck', 'true');
        this.scheduleUpdateCheck();
        notificationManager.success('الإعدادات', 'تم تفعيل التحديثات التلقائية');
    }

    disableAutoUpdates() {
        this.autoCheckEnabled = false;
        localStorage.setItem('autoUpdateCheck', 'false');
        notificationManager.info('الإعدادات', 'تم إيقاف التحديثات التلقائية');
    }
}

// Initialize auto updater
const autoUpdater = new AutoUpdater();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AutoUpdater;
}
