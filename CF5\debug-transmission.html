<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص جدول الإرسال</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', sans-serif;
            background-color: #f1f5f9;
            margin: 0;
            padding: 20px;
            direction: rtl;
            color: #334155;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .debug-section {
            padding: 2rem;
            border-bottom: 1px solid #e2e8f0;
        }

        .debug-section h2 {
            color: #1e3a8a;
            margin-bottom: 1rem;
        }

        .btn {
            padding: 0.75rem 1.25rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            font-size: 0.9rem;
            margin: 0.5rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
            color: white;
        }

        .btn-success {
            background: #10b981;
            color: white;
        }

        .btn-warning {
            background: #f59e0b;
            color: white;
        }

        .btn-danger {
            background: #ef4444;
            color: white;
        }

        .debug-output {
            background: #f8fafc;
            padding: 1rem;
            border-radius: 8px;
            margin-top: 1rem;
            border: 1px solid #e2e8f0;
            font-family: monospace;
            white-space: pre-wrap;
        }

        .success {
            color: #10b981;
        }

        .error {
            color: #ef4444;
        }

        .info {
            color: #3b82f6;
        }

        .warning {
            color: #f59e0b;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 تشخيص جدول الإرسال</h1>
            <p>فحص وتشخيص مشاكل النظام</p>
        </div>

        <!-- فحص العناصر -->
        <div class="debug-section">
            <h2>1️⃣ فحص العناصر في DOM</h2>
            <p>فحص وجود العناصر المطلوبة في الصفحة</p>
            
            <button type="button" class="btn btn-primary" onclick="checkDOMElements()">
                <i class="fas fa-search"></i> فحص العناصر
            </button>

            <div id="dom-output" class="debug-output" style="display: none;"></div>
        </div>

        <!-- فحص JavaScript -->
        <div class="debug-section">
            <h2>2️⃣ فحص وظائف JavaScript</h2>
            <p>فحص تحميل وتشغيل الوظائف المطلوبة</p>
            
            <button type="button" class="btn btn-success" onclick="checkJavaScriptFunctions()">
                <i class="fas fa-code"></i> فحص الوظائف
            </button>

            <div id="js-output" class="debug-output" style="display: none;"></div>
        </div>

        <!-- فحص CSS -->
        <div class="debug-section">
            <h2>3️⃣ فحص التنسيقات CSS</h2>
            <p>فحص تطبيق التنسيقات المطلوبة</p>
            
            <button type="button" class="btn btn-warning" onclick="checkCSSStyles()">
                <i class="fas fa-paint-brush"></i> فحص التنسيقات
            </button>

            <div id="css-output" class="debug-output" style="display: none;"></div>
        </div>

        <!-- فحص البيانات -->
        <div class="debug-section">
            <h2>4️⃣ فحص البيانات</h2>
            <p>فحص بنية البيانات وحالة التطبيق</p>
            
            <button type="button" class="btn btn-info" onclick="checkAppData()">
                <i class="fas fa-database"></i> فحص البيانات
            </button>

            <div id="data-output" class="debug-output" style="display: none;"></div>
        </div>

        <!-- اختبار الوظائف -->
        <div class="debug-section">
            <h2>5️⃣ اختبار الوظائف</h2>
            <p>اختبار تشغيل الوظائف الأساسية</p>
            
            <button type="button" class="btn btn-primary" onclick="testServiceTypeChange()">
                <i class="fas fa-cogs"></i> اختبار تغيير نوع الخدمة
            </button>
            
            <button type="button" class="btn btn-success" onclick="testTransmissionTable()">
                <i class="fas fa-table"></i> اختبار جدول الإرسال
            </button>

            <div id="test-output" class="debug-output" style="display: none;"></div>
        </div>

        <!-- إصلاح المشاكل -->
        <div class="debug-section">
            <h2>6️⃣ إصلاح المشاكل</h2>
            <p>محاولة إصلاح المشاكل المكتشفة تلقائياً</p>
            
            <button type="button" class="btn btn-danger" onclick="fixIssues()">
                <i class="fas fa-wrench"></i> إصلاح المشاكل
            </button>

            <div id="fix-output" class="debug-output" style="display: none;"></div>
        </div>
    </div>

    <script>
        // فحص العناصر في DOM
        function checkDOMElements() {
            const output = document.getElementById('dom-output');
            output.style.display = 'block';
            
            let result = '🔍 فحص العناصر في DOM:\n\n';
            
            // قائمة العناصر المطلوبة
            const requiredElements = [
                'service-type',
                'service-date',
                'transmission-table',
                'transmission-table-main',
                'transmission-table-body',
                'add-transmission-entry-btn',
                'transmission-entry-modal',
                'transmission-entry-form'
            ];
            
            let foundCount = 0;
            
            requiredElements.forEach(elementId => {
                const element = document.getElementById(elementId);
                if (element) {
                    result += `✅ ${elementId}: موجود\n`;
                    foundCount++;
                } else {
                    result += `❌ ${elementId}: غير موجود\n`;
                }
            });
            
            result += `\n📊 النتيجة: ${foundCount}/${requiredElements.length} عنصر موجود\n`;
            
            if (foundCount === requiredElements.length) {
                result += '\n🎉 جميع العناصر موجودة!';
                output.className = 'debug-output success';
            } else {
                result += '\n⚠️ بعض العناصر مفقودة!';
                output.className = 'debug-output error';
            }
            
            output.textContent = result;
        }

        // فحص وظائف JavaScript
        function checkJavaScriptFunctions() {
            const output = document.getElementById('js-output');
            output.style.display = 'block';
            
            let result = '🔍 فحص وظائف JavaScript:\n\n';
            
            // قائمة الوظائف المطلوبة
            const requiredFunctions = [
                'handleServiceTypeChange',
                'setupTransmissionTable',
                'updateTransmissionTable',
                'showAddTransmissionEntryModal',
                'markTransmissionSent'
            ];
            
            let foundCount = 0;
            
            requiredFunctions.forEach(functionName => {
                if (typeof window[functionName] === 'function') {
                    result += `✅ ${functionName}: موجودة\n`;
                    foundCount++;
                } else {
                    result += `❌ ${functionName}: غير موجودة\n`;
                }
            });
            
            result += `\n📊 النتيجة: ${foundCount}/${requiredFunctions.length} وظيفة موجودة\n`;
            
            if (foundCount === requiredFunctions.length) {
                result += '\n🎉 جميع الوظائف موجودة!';
                output.className = 'debug-output success';
            } else {
                result += '\n⚠️ بعض الوظائف مفقودة!';
                output.className = 'debug-output error';
            }
            
            output.textContent = result;
        }

        // فحص التنسيقات CSS
        function checkCSSStyles() {
            const output = document.getElementById('css-output');
            output.style.display = 'block';
            
            let result = '🔍 فحص التنسيقات CSS:\n\n';
            
            // فحص تحميل ملف CSS
            const stylesheets = document.styleSheets;
            result += `📄 عدد ملفات CSS المحملة: ${stylesheets.length}\n\n`;
            
            // فحص وجود فئات CSS مهمة
            const testElement = document.createElement('div');
            testElement.className = 'transmission-header';
            document.body.appendChild(testElement);
            
            const computedStyle = window.getComputedStyle(testElement);
            const hasStyles = computedStyle.background !== 'rgba(0, 0, 0, 0)' || 
                             computedStyle.backgroundColor !== 'rgba(0, 0, 0, 0)';
            
            if (hasStyles) {
                result += '✅ تنسيقات جدول الإرسال: محملة\n';
            } else {
                result += '❌ تنسيقات جدول الإرسال: غير محملة\n';
            }
            
            document.body.removeChild(testElement);
            
            // فحص فئة active-section
            const sections = document.querySelectorAll('section');
            const activeSections = document.querySelectorAll('section.active-section');
            
            result += `📋 عدد الأقسام: ${sections.length}\n`;
            result += `🎯 الأقسام النشطة: ${activeSections.length}\n`;
            
            output.textContent = result;
            output.className = 'debug-output info';
        }

        // فحص البيانات
        function checkAppData() {
            const output = document.getElementById('data-output');
            output.style.display = 'block';
            
            let result = '🔍 فحص البيانات:\n\n';
            
            // فحص وجود متغير appData
            if (typeof window.appData !== 'undefined') {
                result += '✅ متغير appData: موجود\n';
                
                if (window.appData.transmissionTable) {
                    result += `📋 جدول الإرسال: ${window.appData.transmissionTable.length} عنصر\n`;
                } else {
                    result += '❌ جدول الإرسال: غير موجود\n';
                }
                
                if (window.appData.settings && window.appData.settings.company) {
                    result += '✅ إعدادات المؤسسة: موجودة\n';
                    result += `🏢 اسم المؤسسة: ${window.appData.settings.company.name}\n`;
                } else {
                    result += '❌ إعدادات المؤسسة: غير موجودة\n';
                }
            } else {
                result += '❌ متغير appData: غير موجود\n';
            }
            
            // فحص localStorage
            const savedData = localStorage.getItem('appData');
            if (savedData) {
                result += '✅ البيانات المحفوظة: موجودة\n';
                try {
                    const parsed = JSON.parse(savedData);
                    result += `💾 حجم البيانات: ${JSON.stringify(parsed).length} حرف\n`;
                } catch (e) {
                    result += '❌ البيانات المحفوظة: تالفة\n';
                }
            } else {
                result += '⚠️ البيانات المحفوظة: غير موجودة\n';
            }
            
            output.textContent = result;
            output.className = 'debug-output info';
        }

        // اختبار تغيير نوع الخدمة
        function testServiceTypeChange() {
            const output = document.getElementById('test-output');
            output.style.display = 'block';

            let result = '🧪 اختبار تغيير نوع الخدمة:\n\n';

            // محاولة العثور على عنصر نوع الخدمة
            const serviceTypeElement = document.getElementById('service-type');

            if (serviceTypeElement) {
                result += '✅ عنصر نوع الخدمة: موجود\n';

                // اختبار تغيير القيمة
                try {
                    serviceTypeElement.value = 'تركيب';

                    // محاولة استدعاء الوظيفة
                    if (typeof window.handleServiceTypeChange === 'function') {
                        window.handleServiceTypeChange();
                        result += '✅ وظيفة handleServiceTypeChange: تم تشغيلها\n';
                    } else {
                        result += '❌ وظيفة handleServiceTypeChange: غير موجودة\n';
                    }

                    // فحص تأثير التغيير
                    const serviceInfo = document.getElementById('service-type-info');
                    if (serviceInfo) {
                        result += '✅ رسالة نوع الخدمة: ظهرت\n';
                        result += `📝 النص: ${serviceInfo.textContent}\n`;
                    } else {
                        result += '⚠️ رسالة نوع الخدمة: لم تظهر\n';
                    }

                } catch (error) {
                    result += `❌ خطأ في الاختبار: ${error.message}\n`;
                }
            } else {
                result += '❌ عنصر نوع الخدمة: غير موجود\n';
            }

            output.textContent = result;
            output.className = 'debug-output info';
        }

        // اختبار جدول الإرسال
        function testTransmissionTable() {
            const output = document.getElementById('test-output');
            output.style.display = 'block';

            let result = '🧪 اختبار جدول الإرسال:\n\n';

            // فحص قسم جدول الإرسال
            const transmissionSection = document.getElementById('transmission-table');

            if (transmissionSection) {
                result += '✅ قسم جدول الإرسال: موجود\n';

                // فحص الرؤية
                const computedStyle = window.getComputedStyle(transmissionSection);
                result += `👁️ حالة العرض: ${computedStyle.display}\n`;

                // فحص الفئات
                result += `🏷️ الفئات: ${transmissionSection.className}\n`;

                // محاولة إظهار القسم
                transmissionSection.classList.add('active-section');
                result += '🔄 تم إضافة فئة active-section\n';

                // فحص الجدول
                const table = document.getElementById('transmission-table-main');
                if (table) {
                    result += '✅ جدول الإرسال: موجود\n';
                } else {
                    result += '❌ جدول الإرسال: غير موجود\n';
                }

                // فحص الأزرار
                const addBtn = document.getElementById('add-transmission-entry-btn');
                if (addBtn) {
                    result += '✅ زر الإضافة: موجود\n';
                } else {
                    result += '❌ زر الإضافة: غير موجود\n';
                }

            } else {
                result += '❌ قسم جدول الإرسال: غير موجود\n';
            }

            output.textContent = result;
            output.className = 'debug-output info';
        }

        // إصلاح المشاكل
        function fixIssues() {
            const output = document.getElementById('fix-output');
            output.style.display = 'block';

            let result = '🔧 محاولة إصلاح المشاكل:\n\n';

            try {
                // إصلاح 1: إضافة وظيفة handleServiceTypeChange إذا كانت مفقودة
                if (typeof window.handleServiceTypeChange !== 'function') {
                    window.handleServiceTypeChange = function() {
                        console.log('Service type change handler (fixed)');
                        const serviceType = document.getElementById('service-type').value;
                        const serviceDate = document.getElementById('service-date');

                        if (serviceDate && !serviceDate.value) {
                            serviceDate.value = new Date().toISOString().split('T')[0];
                        }

                        // إظهار رسالة
                        let message = '';
                        if (serviceType === 'تركيب' || serviceType === 'مراقبة') {
                            message = 'سيتم إضافة هذه العملية إلى جدول الإرسال';
                        } else if (serviceType === 'تجديد بطاقة') {
                            message = 'سيتم إضافة/تحديث بطاقة الغاز للعميل';
                        }

                        let serviceInfo = document.getElementById('service-type-info');
                        if (!serviceInfo) {
                            serviceInfo = document.createElement('div');
                            serviceInfo.id = 'service-type-info';
                            serviceInfo.style.cssText = 'margin-top: 0.5rem; padding: 0.5rem; background: #e0f2fe; border-radius: 4px; font-size: 0.9rem; color: #0277bd;';
                            const serviceTypeSelect = document.getElementById('service-type');
                            if (serviceTypeSelect && serviceTypeSelect.parentNode) {
                                serviceTypeSelect.parentNode.appendChild(serviceInfo);
                            }
                        }
                        if (serviceInfo) {
                            serviceInfo.textContent = message;
                            serviceInfo.style.display = 'block';
                        }
                    };
                    result += '✅ تم إضافة وظيفة handleServiceTypeChange\n';
                }

                // إصلاح 2: إضافة مستمع الأحداث لنوع الخدمة
                const serviceTypeSelect = document.getElementById('service-type');
                if (serviceTypeSelect) {
                    serviceTypeSelect.removeEventListener('change', window.handleServiceTypeChange);
                    serviceTypeSelect.addEventListener('change', window.handleServiceTypeChange);
                    result += '✅ تم إضافة مستمع أحداث نوع الخدمة\n';
                }

                // إصلاح 3: إظهار قسم جدول الإرسال
                const transmissionSection = document.getElementById('transmission-table');
                if (transmissionSection) {
                    transmissionSection.style.display = 'block';
                    result += '✅ تم إظهار قسم جدول الإرسال\n';
                }

                // إصلاح 4: إضافة بيانات تجريبية إذا كانت مفقودة
                if (typeof window.appData === 'undefined') {
                    window.appData = {
                        customers: [],
                        vehicles: [],
                        gasCards: [],
                        transmissionTable: [],
                        settings: {
                            company: {
                                name: 'مركز وقود المستقبل - عزيري عبد الله اسحاق',
                                number: '463/2019',
                                directorateName: 'مدير الصناعة و المناجم لولاية المدية',
                                transmissionFrequency: 'both',
                                nextTransmissionDate: '',
                                transmissionReminderDays: 7,
                                lastTransmissionDate: '',
                                transmissionHistory: []
                            }
                        }
                    };
                    result += '✅ تم إنشاء بيانات التطبيق\n';
                }

                result += '\n🎉 تم الانتهاء من الإصلاحات!';
                output.className = 'debug-output success';

            } catch (error) {
                result += `\n❌ خطأ في الإصلاح: ${error.message}`;
                output.className = 'debug-output error';
            }

            output.textContent = result;
        }
    </script>
</body>
</html>
