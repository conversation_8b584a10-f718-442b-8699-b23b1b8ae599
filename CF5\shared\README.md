# مجلد الملفات المشتركة - Shared Files Directory

## الغرض - Purpose
هذا المجلد يحتوي على جميع الملفات المشتركة بين حزم المشروع المختلفة لتجنب التكرار وتسهيل الصيانة.

This directory contains all shared files between different project packages to avoid duplication and ease maintenance.

## البنية - Structure

```
shared/
├── scripts/           # ملفات JavaScript المشتركة
│   ├── updater.js     # نظام التحديثات
│   ├── notifications.js # نظام الإشعارات
│   ├── reports.js     # نظام التقارير
│   └── security.js    # نظام الأمان
├── templates/         # قوالب HTML المشتركة
│   └── admin-control-panel.html
├── assets/           # الأصول المشتركة
│   └── future-fuel-icon.png
├── package.json      # تكوين Node.js المشترك
├── shared-config.json # تكوين الملفات المشتركة
└── README.md         # هذا الملف
```

## الاستخدام - Usage

### للمطورين - For Developers
1. **إضافة ملف جديد**: ضع الملف في المجلد المناسب وحدث `shared-config.json`
2. **تحديث ملف موجود**: عدل الملف هنا وسيؤثر على جميع الحزم
3. **حذف ملف**: احذف من هنا وحدث جميع المراجع

### مسارات الوصول - Access Paths
- **من customer-package**: `../shared/`
- **من installer-package**: `../../shared/`
- **من developer-package**: `../shared/`

## الملفات المحذوفة - Removed Duplicates

### تم حذف النسخ المكررة التالية:
- `developer-package/admin-control-panel.html` → `shared/templates/admin-control-panel.html`
- `installer-package/app/scripts/updater.js` → `shared/scripts/updater.js`
- `installer-package/app/scripts/notifications.js` → `shared/scripts/notifications.js`
- `installer-package/app/scripts/reports.js` → `shared/scripts/reports.js`
- `installer-package/app/scripts/security.js` → `shared/scripts/security.js`
- `installer-package/app/package.json` → `shared/package.json`
- `installer-package/app/assets/future-fuel-icon (8).png` → `shared/assets/future-fuel-icon.png`

## التحديثات المطلوبة - Required Updates

### تم تحديث المراجع في:
- `installer-package/app/index.html` - تحديث مسارات JavaScript

### يحتاج تحديث:
- ملفات أخرى قد تحتوي على مراجع للملفات المحذوفة

## النسخة الاحتياطية - Backup
تم إنشاء نسخة احتياطية كاملة في: `backup-before-cleanup/`

## التحقق من السلامة - Integrity Check
بعد التنظيف، تأكد من:
1. عمل جميع الروابط بشكل صحيح
2. تحميل جميع ملفات JavaScript
3. عرض جميع الصور والأيقونات
4. عمل جميع الوظائف كما هو متوقع

## الصيانة - Maintenance
- فحص دوري للملفات المكررة
- تحديث المراجع عند إضافة ملفات جديدة
- مراجعة التبعيات بانتظام

---
تاريخ الإنشاء: 2025-07-08
آخر تحديث: 2025-07-08
