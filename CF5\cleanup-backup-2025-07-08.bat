@echo off
chcp 65001 >nul
title إنشاء نسخة احتياطية قبل التنظيف - Backup Before Cleanup

echo ================================================================================
echo                    🔄 إنشاء نسخة احتياطية قبل التنظيف
echo                    Creating Backup Before Cleanup
echo ================================================================================
echo.

:: تحديد مجلد النسخة الاحتياطية
set BACKUP_DIR=backup-before-cleanup-%DATE:~-4%-%DATE:~4,2%-%DATE:~7,2%_%TIME:~0,2%-%TIME:~3,2%-%TIME:~6,2%
set BACKUP_DIR=%BACKUP_DIR: =0%

echo 📁 إنشاء مجلد النسخة الاحتياطية...
echo Creating backup directory: %BACKUP_DIR%
mkdir "%BACKUP_DIR%" 2>nul

echo.
echo 📋 قائمة الملفات التي سيتم نسخها احتياطياً:
echo Files to be backed up:
echo.

:: نسخ الملفات المهمة
echo [1/8] نسخ الملفات الجذرية...
xcopy /E /I /Y "*.html" "%BACKUP_DIR%\" >nul 2>&1
xcopy /E /I /Y "*.bat" "%BACKUP_DIR%\" >nul 2>&1
xcopy /E /I /Y "*.png" "%BACKUP_DIR%\" >nul 2>&1
echo      ✓ Root files copied

echo [2/8] نسخ customer-package...
xcopy /E /I /Y "customer-package" "%BACKUP_DIR%\customer-package\" >nul 2>&1
echo      ✓ Customer package copied

echo [3/8] نسخ installer-package...
xcopy /E /I /Y "installer-package" "%BACKUP_DIR%\installer-package\" >nul 2>&1
echo      ✓ Installer package copied

echo [4/8] نسخ developer-package...
xcopy /E /I /Y "developer-package" "%BACKUP_DIR%\developer-package\" >nul 2>&1
echo      ✓ Developer package copied

echo [5/8] نسخ css...
xcopy /E /I /Y "css" "%BACKUP_DIR%\css\" >nul 2>&1
echo      ✓ CSS files copied

echo [6/8] نسخ js...
xcopy /E /I /Y "js" "%BACKUP_DIR%\js\" >nul 2>&1
echo      ✓ JavaScript files copied

echo [7/8] نسخ modules...
xcopy /E /I /Y "modules" "%BACKUP_DIR%\modules\" >nul 2>&1
echo      ✓ Modules copied

echo [8/8] إنشاء ملف معلومات النسخة الاحتياطية...
(
echo # معلومات النسخة الاحتياطية
echo # Backup Information
echo.
echo تاريخ الإنشاء: %DATE% %TIME%
echo Creation Date: %DATE% %TIME%
echo.
echo الغرض: نسخة احتياطية قبل تنظيف الملفات المتكررة
echo Purpose: Backup before cleaning duplicate files
echo.
echo الملفات المنسوخة:
echo - جميع ملفات HTML
echo - جميع ملفات BAT
echo - customer-package كاملة
echo - installer-package كاملة  
echo - developer-package كاملة
echo - مجلدات CSS و JS و modules
echo.
echo لاستعادة النسخة الاحتياطية:
echo 1. احذف المجلدات الحالية
echo 2. انسخ محتويات هذا المجلد إلى المجلد الأصلي
echo.
echo To restore backup:
echo 1. Delete current folders
echo 2. Copy contents of this folder to original location
) > "%BACKUP_DIR%\BACKUP_INFO.txt"
echo      ✓ Backup info created

echo.
echo ✅ تم إنشاء النسخة الاحتياطية بنجاح!
echo ✅ Backup created successfully!
echo.
echo 📁 مجلد النسخة الاحتياطية: %BACKUP_DIR%
echo 📁 Backup directory: %BACKUP_DIR%
echo.

:: حساب حجم النسخة الاحتياطية
for /f "tokens=3" %%a in ('dir /s /-c "%BACKUP_DIR%" ^| find "bytes"') do set BACKUP_SIZE=%%a
set /a BACKUP_SIZE_MB=%BACKUP_SIZE%/1048576
echo 📊 حجم النسخة الاحتياطية: %BACKUP_SIZE_MB% MB
echo 📊 Backup size: %BACKUP_SIZE_MB% MB

echo.
echo ⚠️  احتفظ بهذه النسخة الاحتياطية حتى التأكد من نجاح التنظيف
echo ⚠️  Keep this backup until cleanup is verified successful
echo.
pause
