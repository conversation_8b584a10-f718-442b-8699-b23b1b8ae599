<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار سلامة التنظيف - Cleanup Integrity Test</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            direction: rtl;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #eee;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 10px;
            background: #f9f9f9;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .file-list {
            background: white;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            max-height: 200px;
            overflow-y: auto;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 اختبار سلامة التنظيف</h1>
            <h2>Cleanup Integrity Test</h2>
            <p>فحص شامل للتأكد من سلامة النظام بعد تنظيف الملفات المتكررة</p>
        </div>

        <div class="stats">
            <div class="stat-card">
                <h3>الملفات المحذوفة</h3>
                <div id="deleted-count">7</div>
            </div>
            <div class="stat-card">
                <h3>الملفات المنقولة</h3>
                <div id="moved-count">7</div>
            </div>
            <div class="stat-card">
                <h3>المساحة الموفرة</h3>
                <div id="space-saved">~380 KB</div>
            </div>
            <div class="stat-card">
                <h3>المراجع المحدثة</h3>
                <div id="references-updated">4</div>
            </div>
        </div>

        <div class="test-section">
            <h3>🗂️ فحص بنية المجلد المشترك</h3>
            <button onclick="checkSharedStructure()">فحص البنية</button>
            <div id="structure-results"></div>
        </div>

        <div class="test-section">
            <h3>🔗 فحص المراجع والروابط</h3>
            <button onclick="checkReferences()">فحص المراجع</button>
            <div id="references-results"></div>
        </div>

        <div class="test-section">
            <h3>📁 فحص الملفات المحذوفة</h3>
            <button onclick="checkDeletedFiles()">فحص الملفات المحذوفة</button>
            <div id="deleted-results"></div>
        </div>

        <div class="test-section">
            <h3>💾 فحص النسخة الاحتياطية</h3>
            <button onclick="checkBackup()">فحص النسخة الاحتياطية</button>
            <div id="backup-results"></div>
        </div>

        <div class="test-section">
            <h3>📊 تقرير شامل</h3>
            <button onclick="generateReport()">إنشاء تقرير</button>
            <div id="report-results"></div>
        </div>
    </div>

    <script>
        // قائمة الملفات المتوقع وجودها في shared
        const expectedSharedFiles = [
            'shared/scripts/updater.js',
            'shared/scripts/notifications.js',
            'shared/scripts/reports.js',
            'shared/scripts/security.js',
            'shared/templates/admin-control-panel.html',
            'shared/assets/future-fuel-icon.png',
            'shared/package.json',
            'shared/shared-config.json',
            'shared/README.md'
        ];

        // قائمة الملفات المتوقع حذفها
        const expectedDeletedFiles = [
            'developer-package/admin-control-panel.html',
            'installer-package/app/scripts/updater.js',
            'installer-package/app/scripts/notifications.js',
            'installer-package/app/scripts/reports.js',
            'installer-package/app/scripts/security.js',
            'installer-package/app/package.json',
            'installer-package/app/assets/future-fuel-icon (8).png'
        ];

        function showResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            container.appendChild(div);
        }

        function clearResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }

        async function checkSharedStructure() {
            clearResults('structure-results');
            showResult('structure-results', '🔄 جاري فحص بنية المجلد المشترك...', 'info');

            let successCount = 0;
            let totalCount = expectedSharedFiles.length;

            for (const file of expectedSharedFiles) {
                try {
                    const response = await fetch(file, { method: 'HEAD' });
                    if (response.ok) {
                        showResult('structure-results', `✅ ${file} - موجود`, 'success');
                        successCount++;
                    } else {
                        showResult('structure-results', `❌ ${file} - غير موجود`, 'error');
                    }
                } catch (error) {
                    showResult('structure-results', `❌ ${file} - خطأ في الوصول`, 'error');
                }
            }

            const percentage = Math.round((successCount / totalCount) * 100);
            showResult('structure-results', 
                `📊 النتيجة: ${successCount}/${totalCount} ملف (${percentage}%)`, 
                percentage === 100 ? 'success' : 'warning'
            );
        }

        async function checkReferences() {
            clearResults('references-results');
            showResult('references-results', '🔄 جاري فحص المراجع والروابط...', 'info');

            // فحص ملف installer-package/app/index.html
            try {
                const response = await fetch('installer-package/app/index.html');
                const content = await response.text();
                
                const sharedReferences = [
                    '../../shared/scripts/security.js',
                    '../../shared/scripts/notifications.js',
                    '../../shared/scripts/updater.js',
                    '../../shared/scripts/reports.js'
                ];

                let foundReferences = 0;
                for (const ref of sharedReferences) {
                    if (content.includes(ref)) {
                        showResult('references-results', `✅ مرجع محدث: ${ref}`, 'success');
                        foundReferences++;
                    } else {
                        showResult('references-results', `❌ مرجع مفقود: ${ref}`, 'error');
                    }
                }

                showResult('references-results', 
                    `📊 المراجع المحدثة: ${foundReferences}/${sharedReferences.length}`, 
                    foundReferences === sharedReferences.length ? 'success' : 'warning'
                );

            } catch (error) {
                showResult('references-results', '❌ خطأ في فحص المراجع', 'error');
            }
        }

        async function checkDeletedFiles() {
            clearResults('deleted-results');
            showResult('deleted-results', '🔄 جاري فحص الملفات المحذوفة...', 'info');

            let deletedCount = 0;
            for (const file of expectedDeletedFiles) {
                try {
                    const response = await fetch(file, { method: 'HEAD' });
                    if (response.ok) {
                        showResult('deleted-results', `⚠️ ${file} - لا يزال موجود!`, 'warning');
                    } else {
                        showResult('deleted-results', `✅ ${file} - تم حذفه بنجاح`, 'success');
                        deletedCount++;
                    }
                } catch (error) {
                    showResult('deleted-results', `✅ ${file} - تم حذفه بنجاح`, 'success');
                    deletedCount++;
                }
            }

            const percentage = Math.round((deletedCount / expectedDeletedFiles.length) * 100);
            showResult('deleted-results', 
                `📊 الملفات المحذوفة: ${deletedCount}/${expectedDeletedFiles.length} (${percentage}%)`, 
                percentage === 100 ? 'success' : 'warning'
            );
        }

        async function checkBackup() {
            clearResults('backup-results');
            showResult('backup-results', '🔄 جاري فحص النسخة الاحتياطية...', 'info');

            try {
                const response = await fetch('backup-before-cleanup/', { method: 'HEAD' });
                if (response.ok) {
                    showResult('backup-results', '✅ مجلد النسخة الاحتياطية موجود', 'success');
                    
                    // فحص بعض الملفات المهمة في النسخة الاحتياطية
                    const backupFiles = [
                        'backup-before-cleanup/customer-package/',
                        'backup-before-cleanup/installer-package/',
                        'backup-before-cleanup/developer-package/'
                    ];

                    for (const file of backupFiles) {
                        try {
                            const backupResponse = await fetch(file, { method: 'HEAD' });
                            if (backupResponse.ok) {
                                showResult('backup-results', `✅ ${file} - محفوظ`, 'success');
                            }
                        } catch (error) {
                            showResult('backup-results', `⚠️ ${file} - قد لا يكون محفوظ`, 'warning');
                        }
                    }
                } else {
                    showResult('backup-results', '❌ مجلد النسخة الاحتياطية غير موجود', 'error');
                }
            } catch (error) {
                showResult('backup-results', '❌ خطأ في فحص النسخة الاحتياطية', 'error');
            }
        }

        function generateReport() {
            clearResults('report-results');
            showResult('report-results', '📋 تقرير شامل لعملية التنظيف', 'info');
            
            const report = `
                <div class="file-list">
                    <h4>📊 ملخص العملية:</h4>
                    <ul>
                        <li><strong>الملفات المحذوفة:</strong> 7 ملفات مكررة</li>
                        <li><strong>الملفات المنقولة:</strong> 7 ملفات إلى shared/</li>
                        <li><strong>المراجع المحدثة:</strong> 4 مراجع في HTML</li>
                        <li><strong>المساحة الموفرة:</strong> ~380 KB</li>
                        <li><strong>النسخة الاحتياطية:</strong> متوفرة في backup-before-cleanup/</li>
                    </ul>
                    
                    <h4>✅ الفوائد المحققة:</h4>
                    <ul>
                        <li>إزالة التكرار في الملفات</li>
                        <li>تسهيل الصيانة والتحديث</li>
                        <li>توفير مساحة التخزين</li>
                        <li>تحسين تنظيم المشروع</li>
                        <li>تقليل احتمالية الأخطاء</li>
                    </ul>
                    
                    <h4>📝 التوصيات:</h4>
                    <ul>
                        <li>اختبار جميع الوظائف للتأكد من عملها</li>
                        <li>الاحتفاظ بالنسخة الاحتياطية لفترة</li>
                        <li>تحديث أي مراجع أخرى إذا وجدت</li>
                        <li>استخدام shared/ للملفات الجديدة المشتركة</li>
                    </ul>
                </div>
            `;
            
            showResult('report-results', report, 'success');
        }

        // تشغيل فحص أولي عند تحميل الصفحة
        window.addEventListener('load', () => {
            setTimeout(() => {
                showResult('structure-results', '💡 انقر على "فحص البنية" لبدء الاختبار', 'info');
                showResult('references-results', '💡 انقر على "فحص المراجع" لفحص الروابط', 'info');
                showResult('deleted-results', '💡 انقر على "فحص الملفات المحذوفة" للتحقق', 'info');
                showResult('backup-results', '💡 انقر على "فحص النسخة الاحتياطية" للتحقق', 'info');
            }, 1000);
        });
    </script>
</body>
</html>
