@echo off
title Creating Backup Before Cleanup

echo ================================================================================
echo                    Creating Backup Before Cleanup
echo ================================================================================
echo.

:: Create backup directory
set BACKUP_DIR=backup-before-cleanup-%DATE:~-4%-%DATE:~4,2%-%DATE:~7,2%_%TIME:~0,2%-%TIME:~3,2%-%TIME:~6,2%
set BACKUP_DIR=%BACKUP_DIR: =0%

echo Creating backup directory: %BACKUP_DIR%
mkdir "%BACKUP_DIR%" 2>nul

echo.
echo [1/8] Copying root files...
xcopy /E /I /Y "*.html" "%BACKUP_DIR%\" >nul 2>&1
xcopy /E /I /Y "*.bat" "%BACKUP_DIR%\" >nul 2>&1
xcopy /E /I /Y "*.png" "%BACKUP_DIR%\" >nul 2>&1
echo      Root files copied

echo [2/8] Copying customer-package...
xcopy /E /I /Y "customer-package" "%BACKUP_DIR%\customer-package\" >nul 2>&1
echo      Customer package copied

echo [3/8] Copying installer-package...
xcopy /E /I /Y "installer-package" "%BACKUP_DIR%\installer-package\" >nul 2>&1
echo      Installer package copied

echo [4/8] Copying developer-package...
xcopy /E /I /Y "developer-package" "%BACKUP_DIR%\developer-package\" >nul 2>&1
echo      Developer package copied

echo [5/8] Copying css...
xcopy /E /I /Y "css" "%BACKUP_DIR%\css\" >nul 2>&1
echo      CSS files copied

echo [6/8] Copying js...
xcopy /E /I /Y "js" "%BACKUP_DIR%\js\" >nul 2>&1
echo      JavaScript files copied

echo [7/8] Copying modules...
xcopy /E /I /Y "modules" "%BACKUP_DIR%\modules\" >nul 2>&1
echo      Modules copied

echo [8/8] Creating backup info...
(
echo # Backup Information
echo.
echo Creation Date: %DATE% %TIME%
echo Purpose: Backup before cleaning duplicate files
echo.
echo Files backed up:
echo - All HTML files
echo - All BAT files  
echo - customer-package complete
echo - installer-package complete
echo - developer-package complete
echo - CSS, JS, and modules folders
echo.
echo To restore backup:
echo 1. Delete current folders
echo 2. Copy contents of this folder to original location
) > "%BACKUP_DIR%\BACKUP_INFO.txt"
echo      Backup info created

echo.
echo Backup created successfully!
echo Backup directory: %BACKUP_DIR%
echo.

:: Calculate backup size
for /f "tokens=3" %%a in ('dir /s /-c "%BACKUP_DIR%" ^| find "bytes"') do set BACKUP_SIZE=%%a
set /a BACKUP_SIZE_MB=%BACKUP_SIZE%/1048576
echo Backup size: %BACKUP_SIZE_MB% MB

echo.
echo Keep this backup until cleanup is verified successful
echo.
pause
