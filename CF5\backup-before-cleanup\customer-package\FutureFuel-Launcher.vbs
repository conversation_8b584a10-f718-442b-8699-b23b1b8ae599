' Future Fuel Corporation - Professional Launcher
' مؤسسة وقود المستقبل - المشغل الاحترافي

Dim fso, shell, appPath, configFile, config

Set fso = CreateObject("Scripting.FileSystemObject")
Set shell = CreateObject("WScript.Shell")

' الحصول على مسار التطبيق
appPath = fso.GetParentFolderName(WScript.ScriptFullName)

' التحقق من وجود ملفات التطبيق
If Not fso.FileExists(appPath & "\app\index.html") Then
    MsgBox "خطأ: ملفات التطبيق غير موجودة" & vbCrLf & _
           "Error: Application files not found" & vbCrLf & vbCrLf & _
           "يرجى إعادة تثبيت البرنامج" & vbCrLf & _
           "Please reinstall the application", _
           vbCritical + vbOKOnly, "Future Fuel Corporation"
    WScript.Quit
End If

' إنشاء المجلدات المطلوبة
CreateRequiredFolders()

' التحقق من تشغيل نسخة أخرى
If IsApplicationRunning() Then
    MsgBox "التطبيق يعمل بالفعل" & vbCrLf & _
           "Application is already running", _
           vbInformation + vbOKOnly, "Future Fuel Corporation"
    WScript.Quit
End If

' إنشاء ملف قفل
CreateLockFile()

' تحميل الإعدادات
LoadConfiguration()

' تشغيل التطبيق
LaunchApplication()

' تنظيف ملف القفل بعد 30 ثانية
WScript.Sleep(30000)
CleanupLockFile()

' ==================== الوظائف المساعدة ====================

Sub CreateRequiredFolders()
    Dim folders, i
    folders = Array("temp", "data", "backup", "logs")
    
    For i = 0 To UBound(folders)
        If Not fso.FolderExists(appPath & "\" & folders(i)) Then
            fso.CreateFolder(appPath & "\" & folders(i))
        End If
    Next
End Sub

Function IsApplicationRunning()
    Dim lockFile
    lockFile = appPath & "\temp\app.lock"
    
    If fso.FileExists(lockFile) Then
        Dim lockFileObj, timeDiff
        Set lockFileObj = fso.GetFile(lockFile)
        timeDiff = DateDiff("n", lockFileObj.DateLastModified, Now)
        
        ' إذا كان ملف القفل أقدم من 5 دقائق، احذفه
        If timeDiff > 5 Then
            fso.DeleteFile(lockFile)
            IsApplicationRunning = False
        Else
            IsApplicationRunning = True
        End If
    Else
        IsApplicationRunning = False
    End If
End Function

Sub CreateLockFile()
    Dim lockFile, lockFileObj
    lockFile = appPath & "\temp\app.lock"
    
    Set lockFileObj = fso.CreateTextFile(lockFile, True)
    lockFileObj.WriteLine("Application started: " & Now)
    lockFileObj.WriteLine("Process ID: " & GetCurrentProcessId())
    lockFileObj.WriteLine("User: " & shell.ExpandEnvironmentStrings("%USERNAME%"))
    lockFileObj.WriteLine("Computer: " & shell.ExpandEnvironmentStrings("%COMPUTERNAME%"))
    lockFileObj.Close
End Sub

Sub CleanupLockFile()
    Dim lockFile
    lockFile = appPath & "\temp\app.lock"
    
    If fso.FileExists(lockFile) Then
        fso.DeleteFile(lockFile)
    End If
End Sub

Sub LoadConfiguration()
    configFile = appPath & "\config.json"
    
    If fso.FileExists(configFile) Then
        ' تسجيل تحميل الإعدادات
        LogEvent("Configuration loaded from: " & configFile)
    Else
        ' إنشاء ملف إعدادات افتراضي
        CreateDefaultConfig()
    End If
End Sub

Sub CreateDefaultConfig()
    Dim configContent, configFileObj
    
    configContent = "{" & vbCrLf & _
                   "  ""application"": {" & vbCrLf & _
                   "    ""name"": ""Future Fuel Corporation""," & vbCrLf & _
                   "    ""version"": ""2.2.0""," & vbCrLf & _
                   "    ""language"": ""ar""" & vbCrLf & _
                   "  }," & vbCrLf & _
                   "  ""launcher"": {" & vbCrLf & _
                   "    ""autoStart"": false," & vbCrLf & _
                   "    ""minimizeToTray"": false," & vbCrLf & _
                   "    ""checkUpdates"": true" & vbCrLf & _
                   "  }" & vbCrLf & _
                   "}"
    
    Set configFileObj = fso.CreateTextFile(appPath & "\config.json", True)
    configFileObj.Write(configContent)
    configFileObj.Close
    
    LogEvent("Default configuration created")
End Sub

Sub LaunchApplication()
    Dim htmlFile, browserPath, launchCommand

    ' التحقق من وجود جلسة نشطة
    Dim sessionFile
    sessionFile = appPath & "\data\current_session.json"

    If fso.FileExists(sessionFile) Then
        ' إذا كانت هناك جلسة نشطة، انتقل مباشرة للنظام الرئيسي
        htmlFile = "file:///" & Replace(appPath & "\app\dashboard.html", "\", "/")
        LogEvent("Active session found, launching dashboard")
    Else
        ' إذا لم تكن هناك جلسة، ابدأ بصفحة تسجيل الدخول
        htmlFile = "file:///" & Replace(appPath & "\app\index.html", "\", "/")
        LogEvent("No active session, launching login page")
    End If

    ' محاولة العثور على متصفح مناسب
    browserPath = FindBestBrowser()

    If browserPath <> "" Then
        ' تشغيل مع متصفح محدد
        launchCommand = """" & browserPath & """ """ & htmlFile & """"
        LogEvent("Launching with browser: " & browserPath)
    Else
        ' تشغيل مع المتصفح الافتراضي
        launchCommand = "cmd /c start """" """ & htmlFile & """"
        LogEvent("Launching with default browser")
    End If

    shell.Run launchCommand, 0, False
    LogEvent("Application launched successfully")
End Sub

Function FindBestBrowser()
    Dim browsers, i, browserPath
    
    ' قائمة المتصفحات المفضلة
    browsers = Array( _
        "C:\Program Files\Google\Chrome\Application\chrome.exe", _
        "C:\Program Files (x86)\Google\Chrome\Application\chrome.exe", _
        "C:\Program Files\Microsoft\Edge\Application\msedge.exe", _
        "C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe", _
        "C:\Program Files\Mozilla Firefox\firefox.exe", _
        "C:\Program Files (x86)\Mozilla Firefox\firefox.exe" _
    )
    
    For i = 0 To UBound(browsers)
        If fso.FileExists(browsers(i)) Then
            FindBestBrowser = browsers(i)
            Exit Function
        End If
    Next
    
    FindBestBrowser = ""
End Function

Function GetCurrentProcessId()
    Dim objWMI, colProcesses, objProcess
    
    Set objWMI = GetObject("winmgmts:\\.\root\cimv2")
    Set colProcesses = objWMI.ExecQuery("SELECT ProcessId FROM Win32_Process WHERE Name = 'wscript.exe'")
    
    For Each objProcess in colProcesses
        GetCurrentProcessId = objProcess.ProcessId
        Exit For
    Next
    
    If GetCurrentProcessId = "" Then
        GetCurrentProcessId = "Unknown"
    End If
End Function

Sub LogEvent(message)
    Dim logFile, logFileObj, timestamp
    
    logFile = appPath & "\logs\launcher.log"
    timestamp = Now
    
    ' إنشاء مجلد السجلات إذا لم يكن موجوداً
    If Not fso.FolderExists(appPath & "\logs") Then
        fso.CreateFolder(appPath & "\logs")
    End If
    
    ' كتابة السجل
    Set logFileObj = fso.OpenTextFile(logFile, 8, True) ' 8 = ForAppending
    logFileObj.WriteLine("[" & timestamp & "] " & message)
    logFileObj.Close
End Sub

' معالجة الأخطاء
On Error Resume Next
If Err.Number <> 0 Then
    LogEvent("Error occurred: " & Err.Description & " (Code: " & Err.Number & ")")
    MsgBox "حدث خطأ في تشغيل التطبيق" & vbCrLf & _
           "An error occurred while launching the application" & vbCrLf & vbCrLf & _
           "Error: " & Err.Description & vbCrLf & _
           "Code: " & Err.Number, _
           vbCritical + vbOKOnly, "Future Fuel Corporation"
End If
