<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ترخيص مؤسسة وقود المستقبل</title>
    <style>
        @media print {
            body { margin: 0; }
            .no-print { display: none; }
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            direction: rtl;
        }
        
        .license-document {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .header {
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }
        
        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }
        
        .logo {
            position: relative;
            z-index: 1;
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 2rem;
        }
        
        .company-name {
            position: relative;
            z-index: 1;
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .company-name-en {
            position: relative;
            z-index: 1;
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px;
        }
        
        .license-info {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
            border-left: 5px solid #3498db;
        }
        
        .license-code {
            background: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            font-size: 1.5rem;
            font-weight: bold;
            font-family: 'Courier New', monospace;
            margin: 20px 0;
            letter-spacing: 2px;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .info-item {
            background: #ecf0f1;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        
        .info-label {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .info-value {
            font-size: 1.1rem;
            color: #34495e;
        }
        
        .instructions {
            background: #e8f4fd;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
        }
        
        .step {
            display: flex;
            align-items: center;
            margin: 15px 0;
            padding: 15px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .step-number {
            background: #3498db;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-left: 15px;
            flex-shrink: 0;
        }
        
        .contact-info {
            background: #2c3e50;
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .contact-item {
            margin: 10px 0;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }
        
        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 1rem;
            margin: 10px;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
        
        .btn-success { background: #27ae60; }
        .btn-success:hover { background: #229954; }
        
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .qr-code {
            text-align: center;
            margin: 20px 0;
        }
        
        .footer {
            background: #34495e;
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="license-document">
        <!-- Header -->
        <div class="header">
            <div class="logo">⛽</div>
            <div class="company-name">مؤسسة وقود المستقبل</div>
            <div class="company-name-en">Future Fuel Corporation</div>
        </div>
        
        <!-- Content -->
        <div class="content">
            <h1 style="text-align: center; color: #2c3e50; margin-bottom: 30px;">
                🎫 ترخيص استخدام النظام
            </h1>
            
            <!-- Customer Info -->
            <div class="license-info">
                <h2 style="color: #2c3e50; margin-bottom: 20px;">معلومات العميل</h2>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">اسم العميل</div>
                        <div class="info-value" id="customerName">[اسم العميل]</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">نوع الترخيص</div>
                        <div class="info-value" id="licenseType">[نوع الترخيص]</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">تاريخ الإصدار</div>
                        <div class="info-value" id="issueDate">[تاريخ الإصدار]</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">تاريخ الانتهاء</div>
                        <div class="info-value" id="expiryDate">[تاريخ الانتهاء]</div>
                    </div>
                </div>
            </div>
            
            <!-- License Code -->
            <div style="text-align: center;">
                <h3 style="color: #2c3e50;">كود الترخيص الخاص بك</h3>
                <div class="license-code" id="licenseCode">[كود الترخيص]</div>
                <p style="color: #7f8c8d; font-size: 0.9rem;">
                    ⚠️ احتفظ بهذا الكود في مكان آمن - ستحتاجه لتسجيل الدخول
                </p>
            </div>
            
            <!-- Instructions -->
            <div class="instructions">
                <h3 style="color: #2c3e50; margin-bottom: 20px;">📋 تعليمات التفعيل والاستخدام</h3>
                
                <div class="step">
                    <div class="step-number">1</div>
                    <div>
                        <strong>تحميل التطبيق:</strong>
                        قم بتحميل ملف التطبيق من الرابط المرفق أو من الموقع الرسمي
                    </div>
                </div>
                
                <div class="step">
                    <div class="step-number">2</div>
                    <div>
                        <strong>تثبيت التطبيق:</strong>
                        فك ضغط الملف وشغل ملف "Setup.bat" أو "OneClick-Install.bat"
                    </div>
                </div>
                
                <div class="step">
                    <div class="step-number">3</div>
                    <div>
                        <strong>تشغيل التطبيق:</strong>
                        افتح التطبيق من سطح المكتب أو من قائمة البرامج
                    </div>
                </div>
                
                <div class="step">
                    <div class="step-number">4</div>
                    <div>
                        <strong>إدخال كود الترخيص:</strong>
                        انسخ كود الترخيص أعلاه والصقه في حقل "كود الترخيص"
                    </div>
                </div>
                
                <div class="step">
                    <div class="step-number">5</div>
                    <div>
                        <strong>تسجيل الدخول:</strong>
                        انقر على "تسجيل الدخول" وابدأ استخدام النظام
                    </div>
                </div>
            </div>
            
            <!-- Features -->
            <div class="license-info">
                <h3 style="color: #2c3e50; margin-bottom: 20px;">✨ الميزات المتاحة في ترخيصك</h3>
                <div id="features">
                    <ul style="list-style: none; padding: 0;">
                        <li style="margin: 10px 0; padding: 10px; background: white; border-radius: 5px;">
                            ✅ إدارة العملاء والمركبات
                        </li>
                        <li style="margin: 10px 0; padding: 10px; background: white; border-radius: 5px;">
                            ✅ إصدار وتجديد بطاقات الغاز
                        </li>
                        <li style="margin: 10px 0; padding: 10px; background: white; border-radius: 5px;">
                            ✅ جدولة المواعيد والتذكيرات
                        </li>
                        <li style="margin: 10px 0; padding: 10px; background: white; border-radius: 5px;">
                            ✅ إدارة الديون والمدفوعات
                        </li>
                        <li style="margin: 10px 0; padding: 10px; background: white; border-radius: 5px;">
                            ✅ التقارير والإحصائيات
                        </li>
                        <li style="margin: 10px 0; padding: 10px; background: white; border-radius: 5px;">
                            ✅ النسخ الاحتياطية التلقائية
                        </li>
                    </ul>
                </div>
            </div>
            
            <!-- Warning -->
            <div class="warning-box">
                <h4 style="margin-top: 0;">⚠️ تنبيهات مهمة:</h4>
                <ul>
                    <li>كود الترخيص مرتبط بجهاز واحد فقط</li>
                    <li>لا تشارك كود الترخيص مع أشخاص آخرين</li>
                    <li>احتفظ بنسخة من هذا الملف في مكان آمن</li>
                    <li>في حالة فقدان الكود، تواصل مع الدعم الفني</li>
                </ul>
            </div>
            
            <!-- Download Links -->
            <div style="text-align: center; margin: 30px 0;" class="no-print">
                <h3 style="color: #2c3e50;">📥 روابط التحميل</h3>
                <a href="#" class="btn btn-success" onclick="downloadApp()">
                    تحميل التطبيق
                </a>
                <a href="#" class="btn" onclick="downloadManual()">
                    تحميل دليل المستخدم
                </a>
                <button class="btn" onclick="window.print()">
                    طباعة هذا الملف
                </button>
            </div>
        </div>
        
        <!-- Contact Info -->
        <div class="contact-info">
            <h3 style="margin-bottom: 20px;">📞 معلومات التواصل والدعم الفني</h3>
            <div class="contact-item">
                <span>📧</span>
                <span>البريد الإلكتروني: <EMAIL></span>
            </div>
            <div class="contact-item">
                <span>📱</span>
                <span>الهاتف: +966-11-123-4567</span>
            </div>
            <div class="contact-item">
                <span>💬</span>
                <span>واتساب: +966-50-123-4567</span>
            </div>
            <div class="contact-item">
                <span>🌐</span>
                <span>الموقع الإلكتروني: www.futurefuel.com</span>
            </div>
            <div class="contact-item">
                <span>⏰</span>
                <span>ساعات العمل: الأحد - الخميس (8:00 ص - 6:00 م)</span>
            </div>
        </div>
        
        <!-- Footer -->
        <div class="footer">
            <p>© 2025 مؤسسة وقود المستقبل - جميع الحقوق محفوظة</p>
            <p>Future Fuel Corporation - All Rights Reserved</p>
            <p style="font-size: 0.8rem; margin-top: 10px;">
                هذا المستند تم إنشاؤه تلقائياً بتاريخ <span id="generationDate"></span>
            </p>
        </div>
    </div>

    <script>
        // تعبئة البيانات تلقائياً
        function fillLicenseData(licenseData) {
            if (licenseData) {
                document.getElementById('customerName').textContent = licenseData.customerName || 'عميل مؤسسة وقود المستقبل';
                document.getElementById('licenseType').textContent = getLicenseTypeName(licenseData.type);
                document.getElementById('issueDate').textContent = new Date(licenseData.createdAt).toLocaleDateString('ar-SA');
                document.getElementById('expiryDate').textContent = new Date(licenseData.expiresAt).toLocaleDateString('ar-SA');
                document.getElementById('licenseCode').textContent = licenseData.code;
                
                // تحديث الميزات حسب نوع الترخيص
                updateFeatures(licenseData.type);
            }
            
            // تاريخ الإنشاء
            document.getElementById('generationDate').textContent = new Date().toLocaleDateString('ar-SA');
        }
        
        function getLicenseTypeName(type) {
            const names = {
                'demo': 'تجريبي (30 يوم)',
                'monthly': 'شهري (30 يوم)',
                'quarterly': 'ربع سنوي (90 يوم)',
                'yearly': 'سنوي (365 يوم)',
                'lifetime': 'مدى الحياة',
                'admin': 'إداري'
            };
            return names[type] || type;
        }
        
        function updateFeatures(type) {
            const featuresDiv = document.getElementById('features');
            let featuresHTML = '<ul style="list-style: none; padding: 0;">';
            
            const allFeatures = [
                'إدارة العملاء والمركبات',
                'إصدار وتجديد بطاقات الغاز',
                'جدولة المواعيد والتذكيرات',
                'إدارة الديون والمدفوعات',
                'التقارير والإحصائيات',
                'النسخ الاحتياطية التلقائية'
            ];
            
            const demoFeatures = [
                'إدارة العملاء (محدود)',
                'إصدار بطاقات الغاز (محدود)',
                'التقارير الأساسية'
            ];
            
            const features = type === 'demo' ? demoFeatures : allFeatures;
            
            features.forEach(feature => {
                featuresHTML += `
                    <li style="margin: 10px 0; padding: 10px; background: white; border-radius: 5px;">
                        ✅ ${feature}
                    </li>
                `;
            });
            
            if (type === 'admin') {
                featuresHTML += `
                    <li style="margin: 10px 0; padding: 10px; background: #e8f4fd; border-radius: 5px;">
                        👑 ميزات إدارية متقدمة
                    </li>
                `;
            }
            
            featuresHTML += '</ul>';
            featuresDiv.innerHTML = featuresHTML;
        }
        
        function downloadApp() {
            alert('سيتم توجيهك لرابط تحميل التطبيق');
            // يمكن إضافة رابط التحميل الفعلي هنا
        }
        
        function downloadManual() {
            alert('سيتم تحميل دليل المستخدم');
            // يمكن إضافة رابط دليل المستخدم هنا
        }
        
        // محاولة الحصول على بيانات الترخيص من URL أو localStorage
        function loadLicenseData() {
            // من URL parameters
            const urlParams = new URLSearchParams(window.location.search);
            const licenseCode = urlParams.get('license');
            
            if (licenseCode) {
                // البحث عن الترخيص في localStorage
                const licenses = JSON.parse(localStorage.getItem('validLicenses') || '[]');
                const license = licenses.find(l => l.code === licenseCode);
                if (license) {
                    fillLicenseData(license);
                    return;
                }
            }
            
            // بيانات افتراضية للعرض
            const defaultData = {
                customerName: 'عميل مؤسسة وقود المستقبل',
                type: 'yearly',
                createdAt: new Date().toISOString(),
                expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
                code: 'YEAR-2025-XXXX-XXXX'
            };
            
            fillLicenseData(defaultData);
        }
        
        // تحميل البيانات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', loadLicenseData);
    </script>
</body>
</html>
