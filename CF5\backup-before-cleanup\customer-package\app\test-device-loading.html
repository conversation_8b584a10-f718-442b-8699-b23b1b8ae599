<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تحميل معرف الجهاز</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid #dee2e6;
        }
        .device-id {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 16px;
            border: 2px solid #2196f3;
            margin: 10px 0;
            text-align: center;
            font-weight: bold;
            color: #1976d2;
            min-height: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.success {
            background: #e8f5e8;
            color: #2e7d32;
            border: 1px solid #4caf50;
        }
        .status.error {
            background: #ffebee;
            color: #c62828;
            border: 1px solid #f44336;
        }
        .status.info {
            background: #e3f2fd;
            color: #1976d2;
            border: 1px solid #2196f3;
        }
        button {
            background: #2196f3;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        button:hover {
            background: #1976d2;
        }
        .log {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار تحميل معرف الجهاز</h1>
        
        <div class="test-section">
            <h3>📱 معرف الجهاز الحالي</h3>
            <div class="device-id" id="deviceId">جاري التحميل...</div>
            <div id="deviceStatus" class="status info">⏳ جاري التحقق...</div>
            
            <button onclick="testDeviceIdGeneration()">🔄 اختبار توليد معرف جديد</button>
            <button onclick="testDeviceIdLoading()">📥 اختبار تحميل معرف محفوظ</button>
            <button onclick="clearDeviceId()">🗑️ مسح معرف الجهاز</button>
            <button onclick="copyDeviceId()">📋 نسخ معرف الجهاز</button>
        </div>

        <div class="test-section">
            <h3>🔍 معلومات النظام</h3>
            <div id="systemInfo"></div>
        </div>

        <div class="test-section">
            <h3>📊 سجل الأحداث</h3>
            <div id="eventLog" class="log"></div>
            <button onclick="clearLog()">🗑️ مسح السجل</button>
        </div>
    </div>

    <script>
        let logMessages = [];

        // إضافة رسالة للسجل
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const logMessage = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
            logMessages.push(logMessage);
            
            // عرض في وحدة التحكم أيضاً
            console.log(logMessage);
            
            // تحديث عرض السجل
            updateLogDisplay();
        }

        // تحديث عرض السجل
        function updateLogDisplay() {
            const logElement = document.getElementById('eventLog');
            logElement.textContent = logMessages.join('\n');
            logElement.scrollTop = logElement.scrollHeight;
        }

        // مسح السجل
        function clearLog() {
            logMessages = [];
            updateLogDisplay();
        }

        // توليد معرف الجهاز المحسن
        function generateDeviceId() {
            addLog('بدء توليد معرف الجهاز...');
            
            let deviceId = '';
            
            try {
                // معرف فريد من معلومات النظام
                const platform = navigator.platform || 'UNKNOWN';
                const userAgent = navigator.userAgent || '';
                const language = navigator.language || 'en';
                const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone || 'UTC';
                const screenInfo = `${window.screen ? window.screen.width : 1920}x${window.screen ? window.screen.height : 1080}`;
                
                addLog(`معلومات النظام: ${platform}, ${language}, ${screenInfo}`);
                
                // استخراج معلومات النظام
                let osType = 'UNK';
                if (platform.includes('Win')) osType = 'WIN';
                else if (platform.includes('Mac')) osType = 'MAC';
                else if (platform.includes('Linux')) osType = 'LNX';
                else if (platform.includes('Android')) osType = 'AND';
                else if (platform.includes('iPhone') || platform.includes('iPad')) osType = 'IOS';
                
                // معرف المتصفح
                let browserType = 'UNK';
                if (userAgent.includes('Chrome') && !userAgent.includes('Edge')) browserType = 'CHR';
                else if (userAgent.includes('Firefox')) browserType = 'FFX';
                else if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) browserType = 'SAF';
                else if (userAgent.includes('Edge')) browserType = 'EDG';
                else if (userAgent.includes('Opera')) browserType = 'OPR';
                
                addLog(`تم تحديد النظام: ${osType} والمتصفح: ${browserType}`);
                
                // إنشاء hash فريد من معلومات الجهاز
                const deviceInfo = `${platform}-${userAgent}-${language}-${timezone}-${screenInfo}`;
                let hash = '';
                
                try {
                    hash = btoa(deviceInfo).replace(/[^A-Z0-9]/g, '').substr(0, 8);
                } catch (e) {
                    hash = Math.random().toString(36).substr(2, 8).toUpperCase();
                    addLog('استخدام hash عشوائي بدلاً من btoa');
                }
                
                // السنة الحالية
                const year = new Date().getFullYear();
                
                // تكوين معرف الجهاز
                deviceId = `${osType}-${browserType}-${hash}-${year}`;
                
                addLog(`تم توليد معرف الجهاز بنجاح: ${deviceId}`);
                
            } catch (error) {
                addLog(`فشل في توليد معرف جهاز متقدم: ${error.message}`, 'error');
                
                // طريقة بديلة بسيطة
                const timestamp = Date.now().toString(36);
                const random = Math.random().toString(36).substr(2, 8);
                deviceId = `DEV-GEN-${timestamp.toUpperCase()}-${random.toUpperCase()}`;
                
                addLog(`تم توليد معرف جهاز بديل: ${deviceId}`);
            }
            
            return deviceId.toUpperCase();
        }

        // تحديث معرف الجهاز
        function updateDeviceId() {
            addLog('بدء تحديث معرف الجهاز...');
            
            let deviceId = localStorage.getItem('deviceId');
            addLog(`معرف الجهاز المحفوظ: ${deviceId || 'غير موجود'}`);
            
            if (!deviceId) {
                addLog('توليد معرف جهاز جديد...');
                deviceId = generateDeviceId();
                localStorage.setItem('deviceId', deviceId);
                addLog(`تم حفظ معرف الجهاز الجديد: ${deviceId}`);
            }
            
            const deviceElement = document.getElementById('deviceId');
            const statusElement = document.getElementById('deviceStatus');
            
            if (deviceElement) {
                deviceElement.textContent = deviceId;
                statusElement.className = 'status success';
                statusElement.textContent = '✅ تم تحميل معرف الجهاز بنجاح';
                addLog('تم تحديث عنصر معرف الجهاز في الصفحة');
            } else {
                statusElement.className = 'status error';
                statusElement.textContent = '❌ لم يتم العثور على عنصر معرف الجهاز';
                addLog('لم يتم العثور على عنصر معرف الجهاز في الصفحة', 'error');
            }
            
            addLog(`انتهى تحديث معرف الجهاز: ${deviceId}`);
            return deviceId;
        }

        // اختبار توليد معرف جديد
        function testDeviceIdGeneration() {
            addLog('=== بدء اختبار توليد معرف جديد ===');
            clearDeviceId();
            setTimeout(() => {
                updateDeviceId();
                addLog('=== انتهى اختبار توليد معرف جديد ===');
            }, 100);
        }

        // اختبار تحميل معرف محفوظ
        function testDeviceIdLoading() {
            addLog('=== بدء اختبار تحميل معرف محفوظ ===');
            const savedId = localStorage.getItem('deviceId');
            if (savedId) {
                addLog(`معرف محفوظ موجود: ${savedId}`);
                updateDeviceId();
            } else {
                addLog('لا يوجد معرف محفوظ، سيتم توليد جديد');
                updateDeviceId();
            }
            addLog('=== انتهى اختبار تحميل معرف محفوظ ===');
        }

        // مسح معرف الجهاز
        function clearDeviceId() {
            localStorage.removeItem('deviceId');
            document.getElementById('deviceId').textContent = 'تم المسح';
            document.getElementById('deviceStatus').className = 'status info';
            document.getElementById('deviceStatus').textContent = '🗑️ تم مسح معرف الجهاز';
            addLog('تم مسح معرف الجهاز من التخزين المحلي');
        }

        // نسخ معرف الجهاز
        function copyDeviceId() {
            const deviceId = document.getElementById('deviceId').textContent;
            navigator.clipboard.writeText(deviceId).then(() => {
                addLog('تم نسخ معرف الجهاز بنجاح');
                alert('تم نسخ معرف الجهاز: ' + deviceId);
            }).catch(() => {
                addLog('فشل في نسخ معرف الجهاز', 'error');
            });
        }

        // عرض معلومات النظام
        function displaySystemInfo() {
            const systemInfo = document.getElementById('systemInfo');
            const info = `
                <strong>المنصة:</strong> ${navigator.platform}<br>
                <strong>المتصفح:</strong> ${navigator.userAgent.substring(0, 100)}...<br>
                <strong>اللغة:</strong> ${navigator.language}<br>
                <strong>المنطقة الزمنية:</strong> ${Intl.DateTimeFormat().resolvedOptions().timeZone}<br>
                <strong>الشاشة:</strong> ${window.screen.width}x${window.screen.height}<br>
                <strong>localStorage متاح:</strong> ${typeof(Storage) !== "undefined" ? 'نعم' : 'لا'}<br>
                <strong>معرف محفوظ:</strong> ${localStorage.getItem('deviceId') || 'غير موجود'}
            `;
            systemInfo.innerHTML = info;
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addLog('=== بدء تهيئة صفحة الاختبار ===');
            addLog('DOM تم تحميله بالكامل');
            
            // عرض معلومات النظام
            displaySystemInfo();
            
            // تحديث معرف الجهاز
            updateDeviceId();
            
            addLog('=== انتهت تهيئة صفحة الاختبار ===');
        });

        // مراقبة الأخطاء
        window.addEventListener('error', function(e) {
            addLog(`خطأ في الصفحة: ${e.error}`, 'error');
        });
    </script>
</body>
</html>
