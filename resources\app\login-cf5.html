<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام إدارة مؤسسة وقود المستقبل</title>
    <link rel="icon" type="image/x-icon" href="assets/favicon.ico">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            direction: rtl;
            overflow: hidden;
            position: relative;
        }

        /* خلفية متحركة */
        .background-animation {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 0;
        }

        .floating-shapes {
            position: absolute;
            width: 100%;
            height: 100%;
        }

        .shape {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        .shape:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .shape:nth-child(2) {
            width: 120px;
            height: 120px;
            top: 60%;
            left: 80%;
            animation-delay: 2s;
        }

        .shape:nth-child(3) {
            width: 60px;
            height: 60px;
            top: 80%;
            left: 20%;
            animation-delay: 4s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        /* الحاوية الرئيسية */
        .login-container {
            position: relative;
            z-index: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            padding: 2rem;
        }

        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
            overflow: hidden;
            width: 100%;
            max-width: 500px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            animation: slideUp 0.8s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* رأس البطاقة */
        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 3rem 2rem;
            position: relative;
            overflow: hidden;
        }

        .login-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: rotate 20s linear infinite;
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .login-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            position: relative;
            z-index: 1;
        }

        .login-title {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            position: relative;
            z-index: 1;
        }

        .login-subtitle {
            font-size: 1rem;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }

        /* محتوى النموذج */
        .login-body {
            padding: 2.5rem;
        }

        .form-group {
            margin-bottom: 2rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.8rem;
            font-weight: 600;
            color: #2c3e50;
            font-size: 1rem;
        }

        .form-input-container {
            position: relative;
        }

        .form-input {
            width: 100%;
            padding: 1.2rem 1rem 1.2rem 3rem;
            border: 2px solid #e1e8ed;
            border-radius: 15px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .form-input:focus {
            outline: none;
            border-color: #3498db;
            background: white;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        .form-icon {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
            font-size: 1.1rem;
            transition: color 0.3s ease;
        }

        .form-input:focus + .form-icon {
            color: #3498db;
        }

        /* معرف الجهاز */
        .device-info {
            background: #e8f4fd;
            border-radius: 15px;
            padding: 1.5rem;
            border-left: 4px solid #3498db;
            margin-bottom: 2rem;
            position: relative;
        }

        .device-label {
            font-size: 0.9rem;
            color: #6c757d;
            margin-bottom: 0.5rem;
            font-weight: 600;
        }

        .device-value {
            font-family: 'Courier New', monospace;
            font-size: 1rem;
            color: #2c3e50;
            font-weight: 600;
            word-break: break-all;
            cursor: pointer;
            padding: 0.5rem 3rem 0.5rem 0.5rem;
            background: white;
            border-radius: 8px;
            border: 1px solid #ddd;
            transition: all 0.3s ease;
        }

        .device-value:hover {
            background: #f8f9fa;
            border-color: #3498db;
        }

        .copy-device-btn {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            background: #3498db;
            color: white;
            border: none;
            padding: 0.5rem;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .copy-device-btn:hover {
            background: #2980b9;
            transform: translateY(-50%) scale(1.1);
        }

        /* قسم طلب التفعيل */
        .activation-section {
            text-align: center;
            margin-bottom: 2rem;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .activation-btn {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
        }

        .activation-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(231, 76, 60, 0.3);
        }

        .activation-help {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.85rem;
            display: block;
        }

        /* زر تسجيل الدخول */
        .login-btn {
            width: 100%;
            padding: 1.5rem;
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            border: none;
            border-radius: 15px;
            font-size: 1.2rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 35px rgba(52, 152, 219, 0.3);
        }

        .login-btn:active {
            transform: translateY(0);
        }

        .loading-spinner {
            display: none;
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- خلفية متحركة -->
    <div class="background-animation">
        <div class="floating-shapes">
            <div class="shape"></div>
            <div class="shape"></div>
            <div class="shape"></div>
        </div>
    </div>

    <!-- الحاوية الرئيسية -->
    <div class="login-container">
        <div class="login-card">
            <!-- رأس البطاقة -->
            <div class="login-header">
                <div class="login-icon">
                    <i class="fas fa-gas-pump"></i>
                </div>
                <h1 class="login-title">نظام إدارة مؤسسة وقود المستقبل</h1>
                <p class="login-subtitle">Future Fuel Corporation Management System</p>
            </div>

            <!-- محتوى النموذج -->
            <div class="login-body">
                <form id="loginForm">
                    <div class="form-group">
                        <label class="form-label">كود الترخيص</label>
                        <div class="form-input-container">
                            <input type="text" id="licenseCode" class="form-input" placeholder="أدخل كود الترخيص (مثال: XXXX-XXXX-XXXX-XXXX)" required>
                            <i class="fas fa-key form-icon"></i>
                        </div>
                    </div>

                    <!-- معرف الجهاز -->
                    <div class="device-info">
                        <div class="device-label">معرف الجهاز</div>
                        <div class="device-value" id="deviceId" title="انقر للنسخ">جاري التحميل...</div>
                        <button type="button" class="copy-device-btn" id="copyDeviceBtn" title="نسخ معرف الجهاز">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>

                    <!-- زر طلب التفعيل -->
                    <div class="activation-section">
                        <button type="button" class="activation-btn" id="requestActivationBtn">
                            <i class="fas fa-paper-plane"></i>
                            طلب تفعيل جديد
                        </button>
                        <small class="activation-help">
                            إذا لم يكن لديك كود ترخيص، يمكنك طلب التفعيل من المطور
                        </small>
                    </div>

                    <button type="submit" class="login-btn">
                        <i class="fas fa-sign-in-alt"></i>
                        تسجيل الدخول
                        <div class="loading-spinner" id="loginSpinner"></div>
                    </button>
                </form>
            </div>
        </div>
    </div>

    <script>
        // نظام معرف الجهاز المتقدم
        function generateAdvancedDeviceId() {
            const timestamp = Date.now();
            const random = Math.random().toString(36).substr(2, 9);
            const userAgent = navigator.userAgent;
            const screen = `${window.screen.width}x${window.screen.height}`;
            const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
            const language = navigator.language;

            const data = `${timestamp}-${random}-${userAgent}-${screen}-${timezone}-${language}`;
            const hash = simpleHash(data);

            return `FFC-${hash.substr(0, 4)}-${hash.substr(4, 4)}-${hash.substr(8, 4)}-${hash.substr(12, 4)}`.toUpperCase();
        }

        function simpleHash(str) {
            let hash = 0;
            for (let i = 0; i < str.length; i++) {
                const char = str.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash;
            }
            return Math.abs(hash).toString(16).padStart(16, '0');
        }

        // تحديث معرف الجهاز
        function updateDeviceId() {
            let deviceId = localStorage.getItem('deviceId');
            if (!deviceId) {
                deviceId = generateAdvancedDeviceId();
                localStorage.setItem('deviceId', deviceId);
                console.log('🆔 تم توليد معرف جهاز جديد:', deviceId);
            } else {
                console.log('🆔 تم تحميل معرف الجهاز المحفوظ:', deviceId);
            }
            document.getElementById('deviceId').textContent = deviceId;
            return deviceId;
        }

        // نسخ معرف الجهاز
        function copyDeviceId() {
            const deviceId = document.getElementById('deviceId').textContent;
            navigator.clipboard.writeText(deviceId).then(() => {
                showNotification('تم نسخ معرف الجهاز إلى الحافظة', 'success');

                const copyBtn = document.getElementById('copyDeviceBtn');
                const originalHTML = copyBtn.innerHTML;
                copyBtn.innerHTML = '<i class="fas fa-check"></i>';
                copyBtn.style.background = '#2ecc71';

                setTimeout(() => {
                    copyBtn.innerHTML = originalHTML;
                    copyBtn.style.background = '';
                }, 2000);
            }).catch(() => {
                const textArea = document.createElement('textarea');
                textArea.value = deviceId;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showNotification('تم نسخ معرف الجهاز', 'success');
            });
        }

        // طلب التفعيل
        function requestActivation() {
            const deviceId = document.getElementById('deviceId').textContent;

            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.8);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
                direction: rtl;
            `;

            modal.innerHTML = `
                <div style="background: white; padding: 2rem; border-radius: 15px; max-width: 500px; width: 90%; max-height: 80vh; overflow-y: auto;">
                    <h3 style="color: #2c3e50; margin-bottom: 1rem; text-align: center;">
                        <i class="fas fa-paper-plane" style="color: #e74c3c;"></i>
                        طلب تفعيل جديد
                    </h3>

                    <form id="activationForm">
                        <div style="margin-bottom: 1rem;">
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 600; color: #2c3e50;">الاسم الأول:</label>
                            <input type="text" id="firstName" required style="width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 8px; font-size: 1rem;">
                        </div>

                        <div style="margin-bottom: 1rem;">
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 600; color: #2c3e50;">اللقب:</label>
                            <input type="text" id="lastName" required style="width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 8px; font-size: 1rem;">
                        </div>

                        <div style="margin-bottom: 1rem;">
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 600; color: #2c3e50;">رقم الهاتف:</label>
                            <input type="tel" id="phone" required style="width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 8px; font-size: 1rem;" placeholder="0555123456">
                        </div>

                        <div style="margin-bottom: 1rem;">
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 600; color: #2c3e50;">اسم المؤسسة (اختياري):</label>
                            <input type="text" id="businessName" style="width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 8px; font-size: 1rem;">
                        </div>

                        <div style="margin-bottom: 1rem;">
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 600; color: #2c3e50;">معرف الجهاز:</label>
                            <input type="text" value="${deviceId}" readonly style="width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 8px; font-size: 1rem; background: #f8f9fa; font-family: monospace;">
                        </div>

                        <div style="margin-bottom: 1.5rem;">
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 600; color: #2c3e50;">ملاحظات (اختياري):</label>
                            <textarea id="notes" rows="3" style="width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 8px; font-size: 1rem; resize: vertical;"></textarea>
                        </div>

                        <div style="display: flex; gap: 1rem; justify-content: center;">
                            <button type="submit" style="background: #e74c3c; color: white; border: none; padding: 0.75rem 1.5rem; border-radius: 8px; font-size: 1rem; cursor: pointer; font-weight: 600;">
                                <i class="fas fa-paper-plane"></i> إرسال الطلب
                            </button>
                            <button type="button" onclick="this.closest('div').parentElement.remove()" style="background: #95a5a6; color: white; border: none; padding: 0.75rem 1.5rem; border-radius: 8px; font-size: 1rem; cursor: pointer;">
                                إلغاء
                            </button>
                        </div>
                    </form>
                </div>
            `;

            document.body.appendChild(modal);

            document.getElementById('activationForm').addEventListener('submit', async function(e) {
                e.preventDefault();

                const formData = {
                    firstName: document.getElementById('firstName').value,
                    lastName: document.getElementById('lastName').value,
                    phone: document.getElementById('phone').value,
                    businessName: document.getElementById('businessName').value,
                    deviceId: deviceId,
                    notes: document.getElementById('notes').value,
                    timestamp: new Date().toISOString()
                };

                const submitBtn = e.target.querySelector('button[type="submit"]');
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الإرسال...';
                submitBtn.disabled = true;

                try {
                    await new Promise(resolve => setTimeout(resolve, 2000));

                    // حفظ الطلب للمطور
                    const existingRequests = JSON.parse(localStorage.getItem('developerPanel_activationRequests') || '[]');
                    const newRequest = {
                        id: 'REQ-' + Date.now(),
                        ...formData,
                        status: 'pending'
                    };
                    existingRequests.push(newRequest);
                    localStorage.setItem('developerPanel_activationRequests', JSON.stringify(existingRequests));

                    modal.remove();
                    showNotification('تم إرسال طلب التفعيل بنجاح! سيتم التواصل معك قريباً.', 'success');

                } catch (error) {
                    showNotification('حدث خطأ في إرسال الطلب. يرجى المحاولة مرة أخرى.', 'error');
                    submitBtn.innerHTML = '<i class="fas fa-paper-plane"></i> إرسال الطلب';
                    submitBtn.disabled = false;
                }
            });
        }

        // التحقق من الترخيص
        function validateLicense(licenseCode, deviceId) {
            // التحقق من التراخيص المحفوظة في لوحة التحكم
            const issuedLicenses = JSON.parse(localStorage.getItem('developerPanel_issuedLicenses') || '[]');

            for (const license of issuedLicenses) {
                if (license.licenseKey === licenseCode && license.deviceId === deviceId) {
                    const expiryDate = new Date(license.expiryDate);
                    if (expiryDate > new Date()) {
                        return { valid: true, license: license };
                    } else {
                        return { valid: false, reason: 'انتهت صلاحية الترخيص' };
                    }
                }
            }

            // التحقق من التراخيص المشفرة (النظام القديم)
            try {
                const decoded = atob(licenseCode);
                const licenseData = JSON.parse(decoded);

                if (licenseData.deviceId === deviceId &&
                    licenseData.type === 'FFC_LICENSE' &&
                    new Date(licenseData.expiryDate) > new Date()) {
                    return { valid: true, license: licenseData };
                }
            } catch (e) {
                // فشل في فك التشفير
            }

            return { valid: false, reason: 'كود الترخيص غير صحيح أو غير متطابق مع معرف الجهاز' };
        }

        // تسجيل الدخول
        function handleLogin(event) {
            event.preventDefault();

            const licenseCode = document.getElementById('licenseCode').value.trim();
            const deviceId = document.getElementById('deviceId').textContent;
            const loginBtn = document.querySelector('.login-btn');
            const spinner = document.getElementById('loginSpinner');

            if (!licenseCode) {
                showNotification('يرجى إدخال كود الترخيص', 'error');
                return;
            }

            // تفعيل حالة التحميل
            loginBtn.disabled = true;
            spinner.style.display = 'block';

            setTimeout(() => {
                const validation = validateLicense(licenseCode, deviceId);

                if (validation.valid) {
                    // حفظ معلومات الجلسة
                    localStorage.setItem('currentSession', JSON.stringify({
                        licenseCode: licenseCode,
                        deviceId: deviceId,
                        loginTime: new Date().toISOString(),
                        license: validation.license
                    }));

                    // حفظ الترخيص النشط
                    localStorage.setItem('appLicense', JSON.stringify({
                        key: licenseCode,
                        deviceId: deviceId,
                        activatedAt: new Date().toISOString(),
                        expiryDate: validation.license.expiryDate,
                        status: 'active'
                    }));

                    showNotification('تم تسجيل الدخول بنجاح!', 'success');

                    setTimeout(() => {
                        window.location.href = 'index.html';
                    }, 1500);
                } else {
                    showNotification(validation.reason, 'error');
                    loginBtn.disabled = false;
                    spinner.style.display = 'none';
                }
            }, 2000);
        }

        // عرض التنبيهات
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                ${message}
            `;

            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 1rem 1.5rem;
                border-radius: 10px;
                color: white;
                font-weight: 600;
                z-index: 10000;
                transform: translateX(400px);
                transition: transform 0.3s ease;
                background: ${type === 'success' ? 'linear-gradient(135deg, #2ecc71 0%, #27ae60 100%)' :
                           type === 'error' ? 'linear-gradient(135deg, #e74c3c 0%, #c0392b 100%)' :
                           'linear-gradient(135deg, #3498db 0%, #2980b9 100%)'};
                display: flex;
                align-items: center;
                gap: 0.5rem;
                max-width: 400px;
                word-wrap: break-word;
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);

            setTimeout(() => {
                notification.style.transform = 'translateX(400px)';
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }, 4000);
        }

        // وظائف مساعدة
        function showHelp() {
            showNotification('للحصول على كود الترخيص، يرجى طلب التفعيل أو الاتصال بالمطور على **********', 'info');
        }

        function showSystemInfo() {
            const deviceId = document.getElementById('deviceId').textContent;
            const hasLicense = !!localStorage.getItem('appLicense');
            showNotification(`معرف الجهاز: ${deviceId.substr(0, 20)}... | الترخيص: ${hasLicense ? 'نشط' : 'غير مفعل'}`, 'info');
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateDeviceId();

            // ربط الأحداث
            document.getElementById('copyDeviceBtn').addEventListener('click', copyDeviceId);
            document.getElementById('requestActivationBtn').addEventListener('click', requestActivation);
            document.getElementById('loginForm').addEventListener('submit', handleLogin);

            // نسخ معرف الجهاز عند النقر عليه
            document.getElementById('deviceId').addEventListener('click', copyDeviceId);

            // إضافة وظائف المساعدة للأزرار
            window.showHelp = showHelp;
            window.showSystemInfo = showSystemInfo;

            console.log('✅ تم تهيئة نظام تسجيل الدخول بنجاح');
        });
    </script>
</body>
</html>
