@echo off
title Future Fuel Management System - Test Tool
color 0A

echo =============================================
echo    Future Fuel Management System
echo           Test Tool v2.2.0
echo =============================================
echo.

echo [1/5] Testing essential files...
set MISSING_FILES=0

if not exist "main.js" (
    echo ERROR: main.js missing
    set /a MISSING_FILES+=1
) else (
    echo OK: main.js found
)

if not exist "preload.js" (
    echo ERROR: preload.js missing
    set /a MISSING_FILES+=1
) else (
    echo OK: preload.js found
)

if not exist "index.html" (
    echo ERROR: index.html missing
    set /a MISSING_FILES+=1
) else (
    echo OK: index.html found
)

if not exist "package.json" (
    echo ERROR: package.json missing
    set /a MISSING_FILES+=1
) else (
    echo OK: package.json found
)

if not exist "assets\icons\app-icon.ico" (
    echo ERROR: app-icon.ico missing
    set /a MISSING_FILES+=1
) else (
    echo OK: app-icon.ico found
)

if not exist "src\auth\login.html" (
    echo ERROR: login.html missing
    set /a MISSING_FILES+=1
) else (
    echo OK: login.html found
)

echo.
echo [2/5] Testing Node.js modules...
if exist "node_modules\electron" (
    echo OK: Electron module found
) else (
    echo ERROR: Electron module missing
    set /a MISSING_FILES+=1
)

echo.
echo [3/5] Testing scripts...
if exist "scripts\script.js" (
    echo OK: script.js found
) else (
    echo ERROR: script.js missing
    set /a MISSING_FILES+=1
)

echo.
echo [4/5] Testing styles...
if exist "styles\styles.css" (
    echo OK: styles.css found
) else (
    echo ERROR: styles.css missing
    set /a MISSING_FILES+=1
)

echo.
echo [5/5] Testing application startup...
echo Attempting to start application...
timeout /t 2 /nobreak >nul

echo.
echo =============================================
echo           Test Summary
echo =============================================
if %MISSING_FILES% EQU 0 (
    echo Status: ✅ ALL TESTS PASSED
    echo The application should work correctly.
    echo.
    echo You can now run the application using:
    echo   npm start
    echo.
    echo Or using Electron directly:
    echo   npx electron main.js
) else (
    echo Status: ❌ ISSUES FOUND
    echo Missing files: %MISSING_FILES%
    echo Please check the missing files above.
)

echo.
echo Press any key to exit...
pause >nul
