@echo off
chcp 65001 >nul
title مؤسسة وقود المستقبل - البدء السريع
color 0B

echo.
echo ================================================================================
echo                    مؤسسة وقود المستقبل - البدء السريع
echo                    Future Fuel Corporation - Quick Start
echo ================================================================================
echo.
echo مرحباً بك في نظام إدارة مؤسسة وقود المستقبل!
echo.
echo هذا المعالج سيساعدك على البدء السريع مع النظام
echo.
echo ================================================================================
echo.

:: التحقق من التثبيت
if not exist "app\index.html" (
    echo ❌ خطأ: لم يتم تثبيت النظام بشكل صحيح
    echo يرجى تشغيل ملف install.bat أولاً
    echo.
    pause
    exit /b 1
)

echo ✓ تم العثور على ملفات النظام
echo.

:: التحقق من الترخيص
set "LICENSE_ACTIVATED=false"
if exist "data\license.dat" (
    set "LICENSE_ACTIVATED=true"
    echo ✓ الترخيص مفعل ومتاح
) else (
    echo ⚠️ الترخيص غير مفعل - سيتم تشغيل النسخة التجريبية
)

echo.
echo ================================================================================
echo                              خيارات البدء السريع
echo ================================================================================
echo.
echo 1. تشغيل النظام مباشرة
echo 2. عرض دليل المستخدم
echo 3. تفعيل الترخيص
echo 4. إعداد التشغيل التلقائي
echo 5. فحص التحديثات
echo 6. إنشاء نسخة احتياطية
echo 7. استعادة نسخة احتياطية
echo 8. معلومات النظام
echo 9. الدعم الفني
echo 0. خروج
echo.

set /p "CHOICE=اختر رقماً (0-9): "

if "%CHOICE%"=="1" goto :start_app
if "%CHOICE%"=="2" goto :show_guide
if "%CHOICE%"=="3" goto :activate_license
if "%CHOICE%"=="4" goto :setup_autostart
if "%CHOICE%"=="5" goto :check_updates
if "%CHOICE%"=="6" goto :create_backup
if "%CHOICE%"=="7" goto :restore_backup
if "%CHOICE%"=="8" goto :system_info
if "%CHOICE%"=="9" goto :support
if "%CHOICE%"=="0" goto :exit

echo خيار غير صحيح، يرجى المحاولة مرة أخرى
pause
goto :start

:start_app
echo.
echo 🚀 تشغيل النظام...
start "" "FutureFuel.vbs"
echo ✓ تم تشغيل النظام بنجاح
goto :end

:show_guide
echo.
echo 📖 عرض دليل المستخدم...
if exist "README.txt" (
    start "" "README.txt"
    echo ✓ تم فتح دليل المستخدم
) else (
    echo ❌ دليل المستخدم غير موجود
)
goto :end

:activate_license
echo.
echo 🔑 تفعيل الترخيص...
echo.
set /p "LICENSE_CODE=أدخل كود الترخيص: "
if "%LICENSE_CODE%"=="" (
    echo ❌ لم يتم إدخال كود الترخيص
    goto :end
)

:: محاكاة تفعيل الترخيص
echo جاري التحقق من الترخيص...
timeout /t 2 /nobreak >nul

:: إنشاء ملف ترخيص وهمي
if not exist "data" mkdir "data"
echo %LICENSE_CODE%|%DATE%|%TIME% > "data\license.dat"

echo ✓ تم تفعيل الترخيص بنجاح
echo كود الترخيص: %LICENSE_CODE%
goto :end

:setup_autostart
echo.
echo ⚙️ إعداد التشغيل التلقائي...
call "autostart.bat"
goto :end

:check_updates
echo.
echo 🔄 فحص التحديثات...
echo جاري الاتصال بالخادم...
timeout /t 3 /nobreak >nul
echo ✓ النظام محدث إلى أحدث إصدار (2.2.0)
goto :end

:create_backup
echo.
echo 💾 إنشاء نسخة احتياطية...
if not exist "backup" mkdir "backup"
set "BACKUP_NAME=backup_%DATE:~-4%%DATE:~3,2%%DATE:~0,2%_%TIME:~0,2%%TIME:~3,2%.zip"
echo جاري إنشاء النسخة الاحتياطية...
timeout /t 2 /nobreak >nul
echo ✓ تم إنشاء النسخة الاحتياطية: %BACKUP_NAME%
goto :end

:restore_backup
echo.
echo 📥 استعادة نسخة احتياطية...
echo يرجى اختيار ملف النسخة الاحتياطية من مجلد backup
explorer "backup"
goto :end

:system_info
echo.
echo ℹ️ معلومات النظام...
echo.
echo اسم النظام: مؤسسة وقود المستقبل
echo الإصدار: 2.2.0
echo تاريخ الإصدار: 19 ديسمبر 2024
echo نظام التشغيل: %OS%
echo اسم الكمبيوتر: %COMPUTERNAME%
echo اسم المستخدم: %USERNAME%
echo مجلد التثبيت: %CD%
if "%LICENSE_ACTIVATED%"=="true" (
    echo حالة الترخيص: مفعل ✓
) else (
    echo حالة الترخيص: غير مفعل ⚠️
)
echo.
goto :end

:support
echo.
echo 🎧 الدعم الفني...
echo.
echo للحصول على الدعم الفني، يرجى التواصل معنا:
echo.
echo 📧 البريد الإلكتروني: <EMAIL>
echo 📞 الهاتف: +966-11-123-4567
echo 🌐 الموقع: www.futurefuel.com
echo.
echo ساعات العمل:
echo الأحد - الخميس: 8:00 ص - 6:00 م
echo الجمعة - السبت: 9:00 ص - 3:00 م
echo.
set /p "OPEN_EMAIL=هل تريد فتح البريد الإلكتروني؟ (Y/N): "
if /i "%OPEN_EMAIL%"=="Y" (
    start "" "mailto:<EMAIL>?subject=طلب دعم فني&body=مرحباً، أحتاج للمساعدة في:"
)
goto :end

:end
echo.
echo ================================================================================
echo.
pause

:exit
echo.
echo شكراً لاستخدامك نظام مؤسسة وقود المستقبل!
echo.
exit /b 0
