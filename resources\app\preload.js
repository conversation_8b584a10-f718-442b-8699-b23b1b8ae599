const { contextBridge, ipcRenderer } = require('electron');

// تعريض APIs آمنة للواجهة الأمامية
contextBridge.exposeInMainWorld('electronAPI', {
    // حفظ البيانات (عبر IPC للأمان)
    saveData: (data) => ipcRenderer.invoke('save-data', data),

    // تحميل البيانات (عبر IPC للأمان)
    loadData: () => ipcRenderer.invoke('load-data'),

    // إنشاء نسخة احتياطية (عبر IPC للأمان)
    createBackup: () => ipcRenderer.invoke('create-backup'),

    // معالجة مربعات الحوار
    showSaveDialog: (options) => ipcRenderer.invoke('save-dialog', options),
    showOpenDialog: (options) => ipcRenderer.invoke('open-dialog', options),

    // فتح روابط خارجية
    openExternal: (url) => ipcRenderer.invoke('open-external', url),

    // معلومات النظام
    getSystemInfo: () => ipcRenderer.invoke('get-system-info'),

    // إدارة الثيم
    setTheme: (theme) => ipcRenderer.invoke('set-theme', theme),
    getTheme: () => ipcRenderer.invoke('get-theme'),

    // إشعارات
    createAutoBackup: () => ipcRenderer.send('create-auto-backup'),

    // إشعارات النظام
    showNotification: (title, body, icon) => {
        if ('Notification' in window && Notification.permission === 'granted') {
            new Notification(title, { body, icon });
        }
    },

    // طلب إذن الإشعارات
    requestNotificationPermission: async () => {
        if ('Notification' in window) {
            return await Notification.requestPermission();
        }
        return 'denied';
    },

    // نظام تسجيل الدخول
    login: (credentials) => ipcRenderer.invoke('login', credentials),
    logout: () => ipcRenderer.invoke('logout'),
    reloadAfterLogin: () => ipcRenderer.invoke('reload-after-login'),

    // طلب التفعيل
    submitActivationRequest: (requestData) => ipcRenderer.invoke('submit-activation-request', requestData)
});
