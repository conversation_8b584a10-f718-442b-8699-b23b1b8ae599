// ملف اختبار للتطبيق
// Test file for the application

const fs = require('fs');
const path = require('path');

console.log('🧪 بدء اختبار التطبيق...');

// اختبار 1: التحقق من وجود الملفات الأساسية
function testEssentialFiles() {
    console.log('\n📁 اختبار الملفات الأساسية...');
    
    const essentialFiles = [
        'main.js',
        'preload.js', 
        'index.html',
        'package.json',
        'src/auth/login.html',
        'src/auth/login.js',
        'src/auth/login.css',
        'assets/icons/app-icon.ico',
        'assets/icons/company-logo.svg',
        'styles/styles.css',
        'scripts/script.js'
    ];
    
    let allFilesExist = true;
    
    essentialFiles.forEach(file => {
        const filePath = path.join(__dirname, file);
        if (fs.existsSync(filePath)) {
            console.log(`✅ ${file} - موجود`);
        } else {
            console.log(`❌ ${file} - مفقود`);
            allFilesExist = false;
        }
    });
    
    return allFilesExist;
}

// اختبار 2: التحقق من صحة package.json
function testPackageJson() {
    console.log('\n📦 اختبار package.json...');
    
    try {
        const packagePath = path.join(__dirname, 'package.json');
        const packageData = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
        
        const requiredFields = ['name', 'version', 'main', 'scripts'];
        let isValid = true;
        
        requiredFields.forEach(field => {
            if (packageData[field]) {
                console.log(`✅ ${field}: ${typeof packageData[field] === 'object' ? 'موجود' : packageData[field]}`);
            } else {
                console.log(`❌ ${field}: مفقود`);
                isValid = false;
            }
        });
        
        // التحقق من وجود script start
        if (packageData.scripts && packageData.scripts.start) {
            console.log(`✅ start script: ${packageData.scripts.start}`);
        } else {
            console.log(`❌ start script: مفقود`);
            isValid = false;
        }
        
        return isValid;
    } catch (error) {
        console.log(`❌ خطأ في قراءة package.json: ${error.message}`);
        return false;
    }
}

// اختبار 3: التحقق من صحة main.js
function testMainJs() {
    console.log('\n⚡ اختبار main.js...');
    
    try {
        const mainPath = path.join(__dirname, 'main.js');
        const mainContent = fs.readFileSync(mainPath, 'utf8');
        
        const requiredImports = [
            'require(\'electron\')',
            'BrowserWindow',
            'app',
            'ipcMain'
        ];
        
        let isValid = true;
        
        requiredImports.forEach(importItem => {
            if (mainContent.includes(importItem)) {
                console.log(`✅ ${importItem} - موجود`);
            } else {
                console.log(`❌ ${importItem} - مفقود`);
                isValid = false;
            }
        });
        
        return isValid;
    } catch (error) {
        console.log(`❌ خطأ في قراءة main.js: ${error.message}`);
        return false;
    }
}

// اختبار 4: التحقق من صحة preload.js
function testPreloadJs() {
    console.log('\n🔗 اختبار preload.js...');
    
    try {
        const preloadPath = path.join(__dirname, 'preload.js');
        const preloadContent = fs.readFileSync(preloadPath, 'utf8');
        
        const requiredAPIs = [
            'contextBridge',
            'electronAPI',
            'saveData',
            'loadData',
            'login'
        ];
        
        let isValid = true;
        
        requiredAPIs.forEach(api => {
            if (preloadContent.includes(api)) {
                console.log(`✅ ${api} - موجود`);
            } else {
                console.log(`❌ ${api} - مفقود`);
                isValid = false;
            }
        });
        
        return isValid;
    } catch (error) {
        console.log(`❌ خطأ في قراءة preload.js: ${error.message}`);
        return false;
    }
}

// تشغيل جميع الاختبارات
function runAllTests() {
    console.log('🚀 تشغيل جميع الاختبارات...\n');
    
    const tests = [
        { name: 'الملفات الأساسية', test: testEssentialFiles },
        { name: 'package.json', test: testPackageJson },
        { name: 'main.js', test: testMainJs },
        { name: 'preload.js', test: testPreloadJs }
    ];
    
    let passedTests = 0;
    
    tests.forEach(({ name, test }) => {
        const result = test();
        if (result) {
            passedTests++;
            console.log(`\n✅ اختبار ${name} نجح`);
        } else {
            console.log(`\n❌ اختبار ${name} فشل`);
        }
    });
    
    console.log(`\n📊 النتائج النهائية:`);
    console.log(`✅ اختبارات نجحت: ${passedTests}/${tests.length}`);
    console.log(`❌ اختبارات فشلت: ${tests.length - passedTests}/${tests.length}`);
    
    if (passedTests === tests.length) {
        console.log('\n🎉 جميع الاختبارات نجحت! التطبيق جاهز للتشغيل.');
        return true;
    } else {
        console.log('\n⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.');
        return false;
    }
}

// تشغيل الاختبارات
if (require.main === module) {
    runAllTests();
}

module.exports = {
    testEssentialFiles,
    testPackageJson,
    testMainJs,
    testPreloadJs,
    runAllTests
};
