<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام الترخيص CF5</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            color: #2c3e50;
            padding: 2rem;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .test-section {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .test-title {
            color: #3498db;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .test-result {
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }

        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            margin: 0.5rem;
            transition: all 0.3s ease;
        }

        .btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #2ecc71;
        }

        .btn-success:hover {
            background: #27ae60;
        }

        .btn-danger {
            background: #e74c3c;
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        .info-box {
            background: #e8f4fd;
            border: 1px solid #3498db;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }

        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 1rem;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            overflow-x: auto;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- الرأس -->
        <div class="header">
            <h1><i class="fas fa-vial"></i> اختبار نظام الترخيص CF5</h1>
            <p>اختبار شامل لجميع مكونات نظام الترخيص المحدث</p>
        </div>

        <!-- اختبار معرف الجهاز -->
        <div class="test-section">
            <h2 class="test-title">
                <i class="fas fa-desktop"></i>
                اختبار معرف الجهاز
            </h2>
            <div id="deviceIdTest"></div>
            <button class="btn" onclick="testDeviceId()">تشغيل الاختبار</button>
        </div>

        <!-- اختبار التراخيص -->
        <div class="test-section">
            <h2 class="test-title">
                <i class="fas fa-key"></i>
                اختبار نظام التراخيص
            </h2>
            <div id="licenseTest"></div>
            <button class="btn" onclick="testLicenseSystem()">تشغيل الاختبار</button>
        </div>

        <!-- اختبار طلبات التفعيل -->
        <div class="test-section">
            <h2 class="test-title">
                <i class="fas fa-paper-plane"></i>
                اختبار طلبات التفعيل
            </h2>
            <div id="activationTest"></div>
            <button class="btn" onclick="testActivationRequests()">تشغيل الاختبار</button>
        </div>

        <!-- اختبار التخزين المحلي -->
        <div class="test-section">
            <h2 class="test-title">
                <i class="fas fa-database"></i>
                اختبار التخزين المحلي
            </h2>
            <div id="storageTest"></div>
            <button class="btn" onclick="testLocalStorage()">تشغيل الاختبار</button>
        </div>

        <!-- أدوات الاختبار -->
        <div class="test-section">
            <h2 class="test-title">
                <i class="fas fa-tools"></i>
                أدوات الاختبار
            </h2>
            <div>
                <button class="btn btn-success" onclick="generateTestData()">توليد بيانات تجريبية</button>
                <button class="btn btn-danger" onclick="clearAllData()">مسح جميع البيانات</button>
                <button class="btn" onclick="exportTestResults()">تصدير نتائج الاختبار</button>
            </div>
            <div id="toolsResult"></div>
        </div>

        <!-- روابط سريعة -->
        <div class="test-section">
            <h2 class="test-title">
                <i class="fas fa-external-link-alt"></i>
                روابط سريعة
            </h2>
            <div>
                <button class="btn" onclick="window.open('login-cf5.html', '_blank')">
                    <i class="fas fa-sign-in-alt"></i> واجهة تسجيل الدخول
                </button>
                <button class="btn" onclick="window.open('admin-control-panel.html', '_blank')">
                    <i class="fas fa-cog"></i> لوحة التحكم الإدارية
                </button>
                <button class="btn" onclick="window.open('index.html', '_blank')">
                    <i class="fas fa-home"></i> النظام الرئيسي
                </button>
            </div>
        </div>
    </div>

    <script>
        // نظام معرف الجهاز المتقدم (نسخة من login-cf5.html)
        function generateAdvancedDeviceId() {
            const timestamp = Date.now();
            const random = Math.random().toString(36).substr(2, 9);
            const userAgent = navigator.userAgent;
            const screen = `${window.screen.width}x${window.screen.height}`;
            const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
            const language = navigator.language;
            
            const data = `${timestamp}-${random}-${userAgent}-${screen}-${timezone}-${language}`;
            const hash = simpleHash(data);
            
            return `FFC-${hash.substr(0, 4)}-${hash.substr(4, 4)}-${hash.substr(8, 4)}-${hash.substr(12, 4)}`.toUpperCase();
        }

        function simpleHash(str) {
            let hash = 0;
            for (let i = 0; i < str.length; i++) {
                const char = str.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash;
            }
            return Math.abs(hash).toString(16).padStart(16, '0');
        }

        // اختبار معرف الجهاز
        function testDeviceId() {
            const container = document.getElementById('deviceIdTest');
            container.innerHTML = '';

            try {
                // توليد معرف جديد
                const deviceId = generateAdvancedDeviceId();
                
                // التحقق من الصيغة
                const isValidFormat = /^FFC-[A-F0-9]{4}-[A-F0-9]{4}-[A-F0-9]{4}-[A-F0-9]{4}$/.test(deviceId);
                
                // حفظ واسترجاع
                localStorage.setItem('testDeviceId', deviceId);
                const retrievedId = localStorage.getItem('testDeviceId');
                
                container.innerHTML = `
                    <div class="test-result ${isValidFormat ? 'success' : 'error'}">
                        <i class="fas fa-${isValidFormat ? 'check' : 'times'}"></i>
                        صيغة معرف الجهاز: ${isValidFormat ? 'صحيحة' : 'خاطئة'}
                    </div>
                    <div class="test-result ${deviceId === retrievedId ? 'success' : 'error'}">
                        <i class="fas fa-${deviceId === retrievedId ? 'check' : 'times'}"></i>
                        حفظ واسترجاع المعرف: ${deviceId === retrievedId ? 'نجح' : 'فشل'}
                    </div>
                    <div class="info-box">
                        <strong>معرف الجهاز المُولد:</strong><br>
                        <div class="code-block">${deviceId}</div>
                    </div>
                `;
                
            } catch (error) {
                container.innerHTML = `
                    <div class="test-result error">
                        <i class="fas fa-times"></i>
                        خطأ في توليد معرف الجهاز: ${error.message}
                    </div>
                `;
            }
        }

        // اختبار نظام التراخيص
        function testLicenseSystem() {
            const container = document.getElementById('licenseTest');
            container.innerHTML = '';

            try {
                const deviceId = generateAdvancedDeviceId();
                const now = new Date();
                const expiryDate = new Date(now.getTime() + (365 * 24 * 60 * 60 * 1000));
                
                // توليد ترخيص تجريبي
                const licenseData = {
                    deviceId: deviceId,
                    expiryDate: expiryDate.toISOString(),
                    type: 'FFC_LICENSE',
                    issued: now.toISOString()
                };
                
                const licenseKey = btoa(JSON.stringify(licenseData));
                
                // اختبار فك التشفير
                const decoded = JSON.parse(atob(licenseKey));
                const isValidDecoding = decoded.deviceId === deviceId;
                
                // اختبار التحقق من الصلاحية
                const isNotExpired = new Date(decoded.expiryDate) > new Date();
                
                container.innerHTML = `
                    <div class="test-result ${licenseKey ? 'success' : 'error'}">
                        <i class="fas fa-${licenseKey ? 'check' : 'times'}"></i>
                        توليد الترخيص: ${licenseKey ? 'نجح' : 'فشل'}
                    </div>
                    <div class="test-result ${isValidDecoding ? 'success' : 'error'}">
                        <i class="fas fa-${isValidDecoding ? 'check' : 'times'}"></i>
                        فك تشفير الترخيص: ${isValidDecoding ? 'نجح' : 'فشل'}
                    </div>
                    <div class="test-result ${isNotExpired ? 'success' : 'error'}">
                        <i class="fas fa-${isNotExpired ? 'check' : 'times'}"></i>
                        صلاحية الترخيص: ${isNotExpired ? 'صالح' : 'منتهي'}
                    </div>
                    <div class="info-box">
                        <strong>الترخيص المُولد:</strong><br>
                        <div class="code-block">${licenseKey.substr(0, 100)}...</div>
                    </div>
                `;
                
            } catch (error) {
                container.innerHTML = `
                    <div class="test-result error">
                        <i class="fas fa-times"></i>
                        خطأ في نظام التراخيص: ${error.message}
                    </div>
                `;
            }
        }

        // اختبار طلبات التفعيل
        function testActivationRequests() {
            const container = document.getElementById('activationTest');
            container.innerHTML = '';

            try {
                // إنشاء طلب تجريبي
                const testRequest = {
                    id: 'REQ-' + Date.now(),
                    firstName: 'أحمد',
                    lastName: 'محمد',
                    phone: '**********',
                    deviceId: generateAdvancedDeviceId(),
                    timestamp: new Date().toISOString(),
                    status: 'pending'
                };
                
                // حفظ الطلب
                const existingRequests = JSON.parse(localStorage.getItem('developerPanel_activationRequests') || '[]');
                existingRequests.push(testRequest);
                localStorage.setItem('developerPanel_activationRequests', JSON.stringify(existingRequests));
                
                // استرجاع الطلبات
                const retrievedRequests = JSON.parse(localStorage.getItem('developerPanel_activationRequests') || '[]');
                const foundRequest = retrievedRequests.find(req => req.id === testRequest.id);
                
                container.innerHTML = `
                    <div class="test-result ${foundRequest ? 'success' : 'error'}">
                        <i class="fas fa-${foundRequest ? 'check' : 'times'}"></i>
                        حفظ طلب التفعيل: ${foundRequest ? 'نجح' : 'فشل'}
                    </div>
                    <div class="test-result success">
                        <i class="fas fa-check"></i>
                        عدد الطلبات المحفوظة: ${retrievedRequests.length}
                    </div>
                    <div class="info-box">
                        <strong>الطلب التجريبي:</strong><br>
                        <div class="code-block">${JSON.stringify(testRequest, null, 2)}</div>
                    </div>
                `;
                
            } catch (error) {
                container.innerHTML = `
                    <div class="test-result error">
                        <i class="fas fa-times"></i>
                        خطأ في طلبات التفعيل: ${error.message}
                    </div>
                `;
            }
        }

        // اختبار التخزين المحلي
        function testLocalStorage() {
            const container = document.getElementById('storageTest');
            container.innerHTML = '';

            try {
                // اختبار الكتابة والقراءة
                const testData = { test: 'data', timestamp: Date.now() };
                localStorage.setItem('storageTest', JSON.stringify(testData));
                const retrieved = JSON.parse(localStorage.getItem('storageTest'));
                
                // اختبار المساحة المتاحة
                let storageSize = 0;
                for (let key in localStorage) {
                    if (localStorage.hasOwnProperty(key)) {
                        storageSize += localStorage[key].length;
                    }
                }
                
                const isWriteReadOk = retrieved.test === testData.test;
                
                container.innerHTML = `
                    <div class="test-result ${isWriteReadOk ? 'success' : 'error'}">
                        <i class="fas fa-${isWriteReadOk ? 'check' : 'times'}"></i>
                        كتابة وقراءة البيانات: ${isWriteReadOk ? 'نجح' : 'فشل'}
                    </div>
                    <div class="test-result success">
                        <i class="fas fa-info"></i>
                        حجم البيانات المحفوظة: ${(storageSize / 1024).toFixed(2)} KB
                    </div>
                    <div class="test-result success">
                        <i class="fas fa-info"></i>
                        عدد المفاتيح: ${Object.keys(localStorage).length}
                    </div>
                `;
                
                // تنظيف
                localStorage.removeItem('storageTest');
                
            } catch (error) {
                container.innerHTML = `
                    <div class="test-result error">
                        <i class="fas fa-times"></i>
                        خطأ في التخزين المحلي: ${error.message}
                    </div>
                `;
            }
        }

        // توليد بيانات تجريبية
        function generateTestData() {
            const container = document.getElementById('toolsResult');
            
            try {
                // طلبات تفعيل تجريبية
                const testRequests = [
                    {
                        id: 'REQ-' + (Date.now() - 1000),
                        firstName: 'محمد',
                        lastName: 'أحمد',
                        phone: '0555111222',
                        deviceId: generateAdvancedDeviceId(),
                        timestamp: new Date(Date.now() - 86400000).toISOString(),
                        status: 'pending'
                    },
                    {
                        id: 'REQ-' + (Date.now() - 2000),
                        firstName: 'فاطمة',
                        lastName: 'علي',
                        phone: '**********',
                        deviceId: generateAdvancedDeviceId(),
                        timestamp: new Date(Date.now() - 172800000).toISOString(),
                        status: 'approved'
                    }
                ];
                
                // تراخيص تجريبية
                const testLicenses = [
                    {
                        id: 'LIC-' + (Date.now() - 3000),
                        clientName: 'محمد أحمد',
                        deviceId: testRequests[1].deviceId,
                        issuedDate: new Date(Date.now() - 86400000).toISOString(),
                        expiryDate: new Date(Date.now() + (365 * 24 * 60 * 60 * 1000)).toISOString(),
                        licenseKey: btoa(JSON.stringify({
                            deviceId: testRequests[1].deviceId,
                            expiryDate: new Date(Date.now() + (365 * 24 * 60 * 60 * 1000)).toISOString(),
                            type: 'FFC_LICENSE'
                        }))
                    }
                ];
                
                // حفظ البيانات
                localStorage.setItem('developerPanel_activationRequests', JSON.stringify(testRequests));
                localStorage.setItem('developerPanel_issuedLicenses', JSON.stringify(testLicenses));
                
                container.innerHTML = `
                    <div class="test-result success">
                        <i class="fas fa-check"></i>
                        تم توليد البيانات التجريبية بنجاح
                    </div>
                `;
                
            } catch (error) {
                container.innerHTML = `
                    <div class="test-result error">
                        <i class="fas fa-times"></i>
                        خطأ في توليد البيانات: ${error.message}
                    </div>
                `;
            }
        }

        // مسح جميع البيانات
        function clearAllData() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات؟')) {
                const container = document.getElementById('toolsResult');
                
                try {
                    const keysToRemove = [
                        'deviceId',
                        'currentSession',
                        'appLicense',
                        'developerPanel_activationRequests',
                        'developerPanel_issuedLicenses',
                        'developerPanel_settings'
                    ];
                    
                    keysToRemove.forEach(key => localStorage.removeItem(key));
                    
                    container.innerHTML = `
                        <div class="test-result success">
                            <i class="fas fa-check"></i>
                            تم مسح جميع البيانات بنجاح
                        </div>
                    `;
                    
                } catch (error) {
                    container.innerHTML = `
                        <div class="test-result error">
                            <i class="fas fa-times"></i>
                            خطأ في مسح البيانات: ${error.message}
                        </div>
                    `;
                }
            }
        }

        // تصدير نتائج الاختبار
        function exportTestResults() {
            const results = {
                timestamp: new Date().toISOString(),
                deviceId: localStorage.getItem('deviceId'),
                activationRequests: JSON.parse(localStorage.getItem('developerPanel_activationRequests') || '[]'),
                issuedLicenses: JSON.parse(localStorage.getItem('developerPanel_issuedLicenses') || '[]'),
                browserInfo: {
                    userAgent: navigator.userAgent,
                    language: navigator.language,
                    platform: navigator.platform
                }
            };
            
            const blob = new Blob([JSON.stringify(results, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `cf5-test-results-${Date.now()}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            document.getElementById('toolsResult').innerHTML = `
                <div class="test-result success">
                    <i class="fas fa-check"></i>
                    تم تصدير نتائج الاختبار بنجاح
                </div>
            `;
        }

        // تشغيل جميع الاختبارات عند تحميل الصفحة
        window.addEventListener('load', function() {
            console.log('🧪 صفحة اختبار نظام الترخيص CF5 جاهزة');
        });
    </script>
</body>
</html>
