@echo off
title Building Customer Package

echo Creating customer package...

if not exist "customer-package\data" mkdir "customer-package\data"
if not exist "customer-package\backup" mkdir "customer-package\backup"
if not exist "customer-package\logs" mkdir "customer-package\logs"
if not exist "customer-package\temp" mkdir "customer-package\temp"
if not exist "customer-package\updates" mkdir "customer-package\updates"

echo Package structure created successfully!
echo.
echo Customer package is ready in: customer-package\
echo.
echo Main files:
echo - install.bat (Installation wizard)
echo - uninstall.bat (Uninstaller)
echo - QuickStart.bat (Quick start menu)
echo - FutureFuel.vbs (Application launcher)
echo - README.txt (User guide)
echo - license.txt (License agreement)
echo - config.json (Configuration)
echo.
echo Application files are in: customer-package\app\
echo.
pause
