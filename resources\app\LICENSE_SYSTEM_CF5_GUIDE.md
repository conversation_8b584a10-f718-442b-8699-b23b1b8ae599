# 🔐 دليل نظام الترخيص المحدث - CF5

## 📋 نظرة عامة

تم دمج نظام الترخيص المتقدم من `CF5` مع النظام الحالي لتوفير:
- ✅ نظام معرف جهاز متقدم
- ✅ واجهة تسجيل دخول محسنة
- ✅ لوحة تحكم إدارية شاملة
- ✅ نظام طلب التفعيل التلقائي
- ✅ إدارة التراخيص المتقدمة

## 🚀 الملفات الجديدة

### 1. واجهة تسجيل الدخول الجديدة
**الملف:** `login-cf5.html`

**المميزات:**
- تصميم عصري ومتجاوب
- نظام معرف جهاز متقدم (FFC-XXXX-XXXX-XXXX-XXXX)
- زر طلب التفعيل المباشر
- نسخ معرف الجهاز بنقرة واحدة
- تحقق تلقائي من الترخيص

### 2. لوحة التحكم الإدارية
**الملف:** `admin-control-panel.html`

**الأقسام:**
- 📥 **طلبات التفعيل**: عرض وإدارة طلبات العملاء
- 🔑 **إدارة التراخيص**: عرض التراخيص المُصدرة
- ⚡ **مولد التراخيص**: توليد تراخيص جديدة
- ⚙️ **الإعدادات**: إعدادات النظام

## 🔧 كيفية الاستخدام

### للعملاء:

1. **فتح واجهة تسجيل الدخول:**
   ```
   افتح: login-cf5.html
   ```

2. **نسخ معرف الجهاز:**
   - سيظهر معرف الجهاز تلقائياً
   - انقر على زر النسخ أو على المعرف نفسه

3. **طلب التفعيل:**
   - انقر على "طلب تفعيل جديد"
   - املأ البيانات المطلوبة
   - أرسل الطلب

4. **تسجيل الدخول:**
   - أدخل كود الترخيص المُستلم
   - انقر "تسجيل الدخول"

### للمطور:

1. **فتح لوحة التحكم:**
   ```
   افتح: admin-control-panel.html
   ```

2. **مراجعة طلبات التفعيل:**
   - تبويب "طلبات التفعيل"
   - مراجعة البيانات
   - الموافقة أو الرفض

3. **توليد الترخيص:**
   - تبويب "مولد التراخيص"
   - ملء بيانات العميل
   - اختيار مدة الترخيص
   - توليد ونسخ الترخيص

4. **إرسال الترخيص للعميل:**
   - نسخ كود الترخيص
   - إرساله للعميل عبر الواتساب أو الهاتف

## 🔄 سير العمل

```
1. العميل يفتح login-cf5.html
2. العميل ينسخ معرف الجهاز
3. العميل يطلب التفعيل
4. المطور يراجع الطلب في admin-control-panel.html
5. المطور يوافق على الطلب
6. المطور يولد الترخيص
7. المطور يرسل الترخيص للعميل
8. العميل يدخل الترخيص ويسجل الدخول
9. العميل يصل إلى النظام الرئيسي
```

## 🛡️ الأمان

### معرف الجهاز:
- يتم توليده باستخدام معلومات الجهاز
- فريد لكل جهاز
- مرتبط بالترخيص

### التراخيص:
- مشفرة ومرتبطة بمعرف الجهاز
- لها تاريخ انتهاء صلاحية
- لا يمكن استخدامها على أجهزة أخرى

### التحقق:
- يتم التحقق من الترخيص عند كل تشغيل
- إعادة توجيه تلقائية لصفحة تسجيل الدخول عند انتهاء الصلاحية

## 📊 البيانات المحفوظة

### في localStorage:
- `deviceId`: معرف الجهاز
- `currentSession`: بيانات الجلسة النشطة
- `appLicense`: معلومات الترخيص
- `developerPanel_activationRequests`: طلبات التفعيل
- `developerPanel_issuedLicenses`: التراخيص المُصدرة
- `developerPanel_settings`: إعدادات النظام

## 🔧 التخصيص

### تغيير مدة الترخيص الافتراضية:
```javascript
// في admin-control-panel.html
systemData.settings.defaultDuration = 365; // بالأيام
```

### تغيير بادئة التراخيص:
```javascript
// في admin-control-panel.html
systemData.settings.licensePrefix = 'FFC';
```

## 🚨 استكشاف الأخطاء

### مشاكل شائعة:

1. **معرف الجهاز لا يظهر:**
   - تأكد من تفعيل JavaScript
   - امسح cache المتصفح

2. **الترخيص لا يعمل:**
   - تحقق من معرف الجهاز
   - تأكد من عدم انتهاء صلاحية الترخيص

3. **طلب التفعيل لا يُرسل:**
   - تحقق من ملء جميع الحقول المطلوبة
   - تأكد من اتصال الإنترنت

## 📞 الدعم الفني

للدعم الفني والمساعدة:
- **الهاتف:** **********
- **الواتساب:** **********

## 📝 ملاحظات مهمة

1. **النسخ الاحتياطي:** احرص على عمل نسخة احتياطية من البيانات بانتظام
2. **الأمان:** لا تشارك أكواد التراخيص مع أشخاص غير مخولين
3. **التحديث:** تحقق من التحديثات بانتظام
4. **الصيانة:** راجع طلبات التفعيل والتراخيص المنتهية الصلاحية دورياً

---

**تم التطوير بواسطة:** مؤسسة وقود المستقبل  
**الإصدار:** 2.2.0  
**التاريخ:** 2025-01-10
