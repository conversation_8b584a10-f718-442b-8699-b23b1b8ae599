================================================================================
                    DEVICE ID COMPATIBILITY GUIDE
                    دليل توافق معرفات الأجهزة
================================================================================

📱 UNIVERSAL DEVICE ID SUPPORT - دعم شامل لمعرفات الأجهزة 📱

================================================================================
                              SUPPORTED FORMATS
                              التنسيقات المدعومة
================================================================================

🎯 SUPPORTED DEVICE ID FORMATS التنسيقات المدعومة لمعرفات الأجهزة:

1. 🖥️ WINDOWS DEVICES أجهزة Windows:
   Format: WIN-XXX-XXXXXXXX-YYYY
   Examples أمثلة:
   • WIN-CHR-4F27B665-2025 (Windows + Chrome)
   • WIN-FFX-A1B2C3D4-2024 (Windows + Firefox)
   • WIN-EDG-12345678-2025 (Windows + Edge)
   • WIN-SAF-ABCDEF12-2024 (Windows + Safari)
   • WIN-OPR-************* (Windows + Opera)

2. 🍎 MAC DEVICES أجهزة Mac:
   Format: MAC-XXX-XXXXXXXX-YYYY
   Examples أمثلة:
   • MAC-SAF-ABCDEF12-2024 (Mac + Safari)
   • MAC-CHR-************* (Mac + Chrome)
   • MAC-FFX-FEDCBA98-2024 (Mac + Firefox)
   • MAC-EDG-12345678-2025 (Mac + Edge)

3. 🐧 LINUX DEVICES أجهزة Linux:
   Format: LNX-XXX-XXXXXXXX-YYYY
   Examples أمثلة:
   • LNX-FFX-FEDCBA98-2024 (Linux + Firefox)
   • LNX-CHR-ABCD1234-2025 (Linux + Chrome)
   • LNX-OPR-87654321-2024 (Linux + Opera)

4. 📱 MOBILE DEVICES الأجهزة المحمولة:
   Android Format: AND-XXX-XXXXXXXX-YYYY
   iOS Format: IOS-XXX-XXXXXXXX-YYYY
   Examples أمثلة:
   • AND-CHR-MOBILE01-2025 (Android + Chrome)
   • IOS-SAF-IPHONE12-2024 (iOS + Safari)
   • AND-FFX-TABLET01-2025 (Android + Firefox)

5. 🔧 GENERATED DEVICES الأجهزة المولدة:
   Format: DEV-XXX-XXXXXXX-XXXX
   Examples أمثلة:
   • DEV-GEN-ABCD1234-EFGH (Generated Device)
   • DEV-AUTO-12345678-ABCD (Auto Generated)
   • DEV-CUST-CUSTOM01-2025 (Custom Generated)

6. 🎨 CUSTOM FORMATS التنسيقات المخصصة:
   Format: XXX-XXX-XXXXXXX-XXXX (Minimum 10 characters)
   Examples أمثلة:
   • CUSTOM-ID-123456-ABCD
   • COMPANY-DEV-DEVICE1-2025
   • SPECIAL-SYS-UNIQUE1-2024

================================================================================
                              BROWSER CODES
                              رموز المتصفحات
================================================================================

🌐 BROWSER TYPE CODES رموز أنواع المتصفحات:

CHR = Chrome متصفح كروم
FFX = Firefox متصفح فايرفوكس
SAF = Safari متصفح سفاري
EDG = Edge متصفح إيدج
OPR = Opera متصفح أوبرا
UNK = Unknown متصفح غير معروف

================================================================================
                              OPERATING SYSTEM CODES
                              رموز أنظمة التشغيل
================================================================================

💻 OS TYPE CODES رموز أنظمة التشغيل:

WIN = Windows نظام ويندوز
MAC = macOS نظام ماك
LNX = Linux نظام لينكس
AND = Android نظام أندرويد
IOS = iOS نظام آي أو إس
UNK = Unknown نظام غير معروف

================================================================================
                              VALIDATION RULES
                              قواعد التحقق
================================================================================

✅ DEVICE ID VALIDATION RULES قواعد التحقق من معرف الجهاز:

1. 📏 LENGTH REQUIREMENTS متطلبات الطول:
   • Minimum length: 10 characters الحد الأدنى: 10 أحرف
   • Maximum length: 50 characters الحد الأقصى: 50 حرف
   • Recommended: 15-25 characters المُوصى به: 15-25 حرف

2. 🔤 CHARACTER REQUIREMENTS متطلبات الأحرف:
   • Allowed: A-Z, 0-9, hyphen (-) المسموح: أحرف إنجليزية كبيرة، أرقام، شرطة
   • Case: Uppercase only الحالة: أحرف كبيرة فقط
   • Spaces: Not allowed المسافات: غير مسموحة

3. 📋 FORMAT PATTERNS أنماط التنسيق:
   • Pattern 1: XXX-XXX-XXXXXXXX-YYYY
   • Pattern 2: DEV-XXX-XXXXXXX-XXXX
   • Pattern 3: XXXXX-XXXXX-XXXXX-XXXXX
   • Pattern 4: Any format with 10+ chars and hyphens

4. 🚫 INVALID EXAMPLES أمثلة غير صالحة:
   • Too short: WIN-CHR (أقل من 10 أحرف)
   • Lowercase: win-chr-4f27b665-2025 (أحرف صغيرة)
   • Spaces: WIN CHR 4F27B665 2025 (مسافات)
   • Special chars: WIN@CHR#4F27B665$2025 (رموز خاصة)

================================================================================
                              TESTING YOUR DEVICE ID
                              اختبار معرف جهازك
================================================================================

🧪 HOW TO TEST YOUR DEVICE ID كيفية اختبار معرف جهازك:

STEP 1: Open Testing Page فتح صفحة الاختبار
📁 File: customer-package/app/test-device-ids.html
👆 Action: Double-click to open

STEP 2: Enter Your Device ID إدخال معرف جهازك
📝 Input: Enter your device ID in the test field
🔍 Example: WIN-CHR-4F27B665-2025

STEP 3: Validate Format التحقق من التنسيق
✅ Click: "اختبار معرف الجهاز" button
📊 Result: See if your device ID is valid

STEP 4: Use in System الاستخدام في النظام
🔄 Click: "استخدام كمعرف حالي" if valid
💾 Save: Device ID will be saved for future use

================================================================================
                              CUSTOM DEVICE ID SETUP
                              إعداد معرف جهاز مخصص
================================================================================

🎨 HOW TO SET CUSTOM DEVICE ID كيفية تعيين معرف جهاز مخصص:

METHOD 1: Through Login Page عبر صفحة تسجيل الدخول
1. Open: customer-package/app/index.html
2. Click: "تخصيص معرف الجهاز" button
3. Enter: Your custom device ID
4. Confirm: Click OK to save

METHOD 2: Through Testing Page عبر صفحة الاختبار
1. Open: customer-package/app/test-device-ids.html
2. Enter: Your device ID in the test field
3. Click: "استخدام كمعرف حالي" button
4. Verify: Check that it's saved

METHOD 3: Manual Storage التخزين اليدوي
1. Open: Browser Developer Tools (F12)
2. Console: Type localStorage.setItem('deviceId', 'YOUR-DEVICE-ID')
3. Refresh: Reload the page to see changes

================================================================================
                              REMOTE ACTIVATION COMPATIBILITY
                              توافق التفعيل عن بُعد
================================================================================

🌐 REMOTE ACTIVATION WITH CUSTOM DEVICE IDS التفعيل عن بُعد مع معرفات الأجهزة المخصصة:

✅ FULLY SUPPORTED متوافق بالكامل:
• All device ID formats are supported جميع تنسيقات معرفات الأجهزة مدعومة
• Remote activation works with any valid device ID التفعيل عن بُعد يعمل مع أي معرف صالح
• Admin panel accepts all formats لوحة الإدارة تقبل جميع التنسيقات
• License binding works correctly ربط التراخيص يعمل بشكل صحيح

🎯 WORKFLOW FOR CUSTOM DEVICE IDS سير العمل لمعرفات الأجهزة المخصصة:

1. 📱 Customer provides device ID العميل يوفر معرف الجهاز
   • Any supported format أي تنسيق مدعوم
   • Example: WIN-CHR-4F27B665-2025

2. 🔧 Admin validates device ID المدير يتحقق من معرف الجهاز
   • Admin panel shows validation result لوحة الإدارة تظهر نتيجة التحقق
   • Clear error messages if invalid رسائل خطأ واضحة إذا كان غير صالح

3. ⚡ Remote activation proceeds التفعيل عن بُعد يتم
   • License generated and bound to device ID ترخيص مولد ومرتبط بمعرف الجهاز
   • Customer receives license code العميل يستلم كود الترخيص

4. ✅ Customer activates successfully العميل يفعل بنجاح
   • License code works with custom device ID كود الترخيص يعمل مع معرف الجهاز المخصص
   • System recognizes and validates النظام يتعرف ويتحقق

================================================================================
                              TROUBLESHOOTING
                              استكشاف الأخطاء
================================================================================

❌ COMMON ISSUES المشاكل الشائعة:

ISSUE 1: "معرف الجهاز غير صالح"
SOLUTION الحل:
• Check format against supported patterns تحقق من التنسيق مقابل الأنماط المدعومة
• Ensure uppercase letters only تأكد من الأحرف الكبيرة فقط
• Remove spaces and special characters احذف المسافات والرموز الخاصة
• Minimum 10 characters required الحد الأدنى 10 أحرف مطلوب

ISSUE 2: "Device ID not recognized"
SOLUTION الحل:
• Use custom device ID setup استخدم إعداد معرف الجهاز المخصص
• Test with test-device-ids.html اختبر مع صفحة اختبار معرفات الأجهزة
• Clear browser cache and try again امسح ذاكرة المتصفح وأعد المحاولة

ISSUE 3: "Remote activation fails"
SOLUTION الحل:
• Verify device ID format in admin panel تحقق من تنسيق معرف الجهاز في لوحة الإدارة
• Check validation messages راجع رسائل التحقق
• Try with a known working format جرب مع تنسيق معروف أنه يعمل

================================================================================
                              EXAMPLES FOR TESTING
                              أمثلة للاختبار
================================================================================

🧪 READY-TO-USE DEVICE IDS معرفات أجهزة جاهزة للاستخدام:

✅ VALID EXAMPLES أمثلة صالحة:
• WIN-CHR-4F27B665-2025
• MAC-SAF-A1B2C3D4-2024
• LNX-FFX-FEDCBA98-2025
• AND-CHR-MOBILE01-2024
• IOS-SAF-IPHONE12-2025
• DEV-GEN-ABCD1234-EFGH
• CUSTOM-ID-123456-ABCD

❌ INVALID EXAMPLES أمثلة غير صالحة:
• WIN-CHR (too short قصير جداً)
• win-chr-4f27b665-2025 (lowercase أحرف صغيرة)
• WIN CHR 4F27B665 2025 (spaces مسافات)
• WIN@CHR#2025 (special chars رموز خاصة)

================================================================================
                              SUPPORT INFORMATION
                              معلومات الدعم
================================================================================

📞 TECHNICAL SUPPORT الدعم الفني:

📧 EMAIL: <EMAIL>
📞 PHONE: +966-11-123-4567
🌐 WEBSITE: www.futurefuel.com
💬 SUPPORT HOURS: Sunday-Thursday 8AM-6PM

TESTING TOOLS أدوات الاختبار:
• test-device-ids.html - Device ID testing
• test-login.html - Login testing
• debug-dashboard.html - System diagnostics

DOCUMENTATION التوثيق:
• DEVICE_ID_SUPPORT.txt - This guide
• REMOTE_ACTIVATION_GUIDE.txt - Remote activation
• QUICK_INSTALL.txt - Installation guide

================================================================================

📱 UNIVERSAL • FLEXIBLE • COMPATIBLE 📱
📱 شامل • مرن • متوافق 📱

All Device Types Supported - جميع أنواع الأجهزة مدعومة!
Your Device ID: WIN-CHR-4F27B665-2025 ✅ FULLY SUPPORTED!

================================================================================
