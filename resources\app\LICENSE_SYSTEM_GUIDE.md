# دليل نظام الترخيص - مؤسسة وقود المستقبل

## نظرة عامة

تم تطوير نظام ترخيص متكامل لتطبيق مؤسسة وقود المستقبل يتضمن:

1. **نظام معرف الجهاز الفريد**
2. **واجهة طلب التفعيل للعملاء**
3. **لوحة تحكم المطور لإدارة التراخيص**
4. **نظام التحقق من صحة التراخيص**

---

## كيفية عمل النظام

### 1. معرف الجهاز (Device ID)

- يتم توليد معرف فريد لكل جهاز تلقائياً عند أول تشغيل
- المعرف بصيغة: `FFC-XXXX-XXXX-XXXX-XXXX`
- يتم حفظ المعرف محلياً ولا يتغير
- يظهر المعرف في واجهة تسجيل الدخول مع إمكانية نسخه

### 2. طلب التفعيل

#### للعميل:
1. فتح التطبيق
2. النقر على "طلب تفعيل"
3. ملء البيانات المطلوبة:
   - الاسم الأول واللقب
   - رقم الهاتف
   - الولاية والبلدية
   - اسم المؤسسة (اختياري)
   - ملاحظات (اختياري)
4. إرسال الطلب (يتم إرفاق معرف الجهاز تلقائياً)

### 3. لوحة تحكم المطور

#### الوصول للوحة التحكم:
- فتح الملف: `resources/app/developer-panel/index.html`
- أو تشغيل خادم محلي وفتح اللوحة

#### الميزات المتاحة:

##### أ) إدارة طلبات التفعيل:
- عرض جميع الطلبات الواردة
- تصفية الطلبات حسب الحالة
- البحث في الطلبات
- عرض تفاصيل كل طلب
- الموافقة على الطلبات أو رفضها
- إنشاء ترخيص تلقائياً عند الموافقة

##### ب) إدارة التراخيص:
- عرض جميع التراخيص المُصدرة
- نسخ مفاتيح التراخيص
- تصدير بيانات التراخيص

##### ج) مولد التراخيص:
- إنشاء تراخيص جديدة يدوياً
- تحديد مدة الترخيص
- ربط الترخيص بمعرف جهاز محدد
- نسخ وحفظ مفاتيح التراخيص

### 4. تفعيل الترخيص

#### للعميل:
1. استلام مفتاح الترخيص من المطور
2. فتح التطبيق
3. لصق مفتاح الترخيص في الحقل المخصص
4. النقر على "تحقق"
5. إذا كان المفتاح صحيح، سيتم تفعيل الترخيص
6. يمكن الآن تسجيل الدخول للتطبيق

---

## خطوات التشغيل

### للمطور:

1. **إعداد لوحة التحكم:**
   ```bash
   # فتح لوحة التحكم
   cd resources/app/developer-panel
   # فتح index.html في المتصفح
   ```

2. **مراجعة طلبات التفعيل:**
   - فتح تبويب "طلبات التفعيل"
   - مراجعة الطلبات الجديدة
   - الموافقة على الطلبات الصحيحة

3. **إرسال الترخيص للعميل:**
   - نسخ مفتاح الترخيص من لوحة التحكم
   - إرساله للعميل عبر الواتساب أو الهاتف

### للعميل:

1. **طلب التفعيل:**
   - فتح التطبيق
   - النقر على "طلب تفعيل"
   - ملء البيانات وإرسال الطلب

2. **تفعيل الترخيص:**
   - استلام مفتاح الترخيص
   - لصقه في حقل "مفتاح الترخيص"
   - النقر على "تحقق"

3. **تسجيل الدخول:**
   - إدخال اسم المستخدم وكلمة المرور
   - النقر على "تسجيل الدخول"

---

## بيانات تسجيل الدخول الافتراضية

```
اسم المستخدم: admin
كلمة المرور: admin123

اسم المستخدم: user
كلمة المرور: user123

اسم المستخدم: manager
كلمة المرور: manager123
```

---

## الملفات المهمة

### ملفات العميل:
- `src/auth/login.html` - واجهة تسجيل الدخول
- `src/auth/login.js` - منطق تسجيل الدخول والترخيص
- `src/auth/login.css` - أنماط واجهة تسجيل الدخول

### ملفات المطور:
- `developer-panel/index.html` - لوحة تحكم المطور
- `developer-panel/script.js` - منطق لوحة التحكم
- `developer-panel/style.css` - أنماط لوحة التحكم

### ملفات البيانات (localStorage):
- `deviceId` - معرف الجهاز
- `appLicense` - بيانات الترخيص النشط
- `developerPanel_activationRequests` - طلبات التفعيل
- `developerPanel_issuedLicenses` - التراخيص المُصدرة

---

## الأمان

1. **معرف الجهاز:** فريد لكل جهاز ولا يمكن تغييره
2. **تشفير الترخيص:** مفاتيح التراخيص مشفرة بـ Base64
3. **التحقق من الصحة:** التحقق من معرف الجهاز وتاريخ الانتهاء
4. **عدم إمكانية النسخ:** الترخيص مرتبط بمعرف جهاز محدد

---

## استكشاف الأخطاء

### مشاكل شائعة:

1. **"الترخيص غير متطابق مع معرف الجهاز"**
   - التأكد من نسخ مفتاح الترخيص الصحيح
   - التأكد من أن الترخيص مُصدر لنفس معرف الجهاز

2. **"مفتاح الترخيص غير صالح"**
   - التأكد من نسخ المفتاح كاملاً
   - التأكد من عدم وجود مسافات إضافية

3. **"يجب تفعيل الترخيص أولاً"**
   - إدخال مفتاح ترخيص صحيح قبل تسجيل الدخول

---

## الدعم الفني

للحصول على الدعم الفني:
- **الهاتف:** 0696924176
- **واتساب:** 0696924176
- **ساعات العمل:** 8:00 صباحاً - 8:00 مساءً

---

## إصدار النظام

- **الإصدار:** 2.2.0
- **تاريخ التطوير:** 2024
- **المطور:** ISHQK
