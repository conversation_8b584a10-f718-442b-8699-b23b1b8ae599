@echo off
title Creating Application Icon

echo Creating application icon...

:: إنشاء ملف ICO بسيط باستخدام PowerShell
powershell -Command ^
"Add-Type -AssemblyName System.Drawing; ^
$bitmap = New-Object System.Drawing.Bitmap(256, 256); ^
$graphics = [System.Drawing.Graphics]::FromImage($bitmap); ^
$graphics.SmoothingMode = 'AntiAlias'; ^
$graphics.Clear([System.Drawing.Color]::FromArgb(30, 60, 114)); ^
$brush1 = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(231, 76, 60)); ^
$brush2 = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(241, 196, 15)); ^
$brush3 = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::White); ^
$graphics.FillEllipse($brush1, 50, 80, 80, 100); ^
$graphics.FillRectangle($brush1, 80, 60, 40, 40); ^
$graphics.FillRectangle([System.Drawing.Brushes]::Black, 85, 65, 30, 15); ^
$graphics.FillEllipse($brush2, 140, 120, 20, 30); ^
$font = New-Object System.Drawing.Font('Arial', 16, [System.Drawing.FontStyle]::Bold); ^
$graphics.DrawString('FF', $font, $brush3, 100, 200); ^
$bitmap.Save('icon.png', [System.Drawing.Imaging.ImageFormat]::Png); ^
$graphics.Dispose(); ^
$bitmap.Dispose();"

echo Icon created successfully!
echo ✓ icon.png created

:: تحويل PNG إلى ICO (محاولة)
if exist "icon.png" (
    echo Converting to ICO format...
    powershell -Command ^
    "Add-Type -AssemblyName System.Drawing; ^
    $png = [System.Drawing.Image]::FromFile('icon.png'); ^
    $ico = New-Object System.Drawing.Icon($png.GetHbitmap(), 32, 32); ^
    $fileStream = [System.IO.File]::Create('icon.ico'); ^
    $ico.Save($fileStream); ^
    $fileStream.Close(); ^
    $ico.Dispose(); ^
    $png.Dispose();"
    
    if exist "icon.ico" (
        echo ✓ icon.ico created successfully!
    ) else (
        echo ⚠️ ICO conversion failed, using PNG
        copy "icon.png" "icon.ico" >nul
    )
) else (
    echo ❌ Failed to create icon
)

pause
