<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إظهار جدول الإرسال</title>
    <style>
        body {
            font-family: 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
            margin: 0;
            padding: 20px;
            direction: rtl;
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            background: white;
            color: #334155;
            padding: 3rem;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
            max-width: 600px;
            width: 100%;
        }

        h1 {
            color: #1e3a8a;
            margin-bottom: 2rem;
            font-size: 2.5rem;
        }

        .btn {
            display: inline-block;
            padding: 1rem 2rem;
            margin: 1rem;
            background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
            color: white;
            text-decoration: none;
            border-radius: 10px;
            font-weight: bold;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(30, 58, 138, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(30, 58, 138, 0.4);
        }

        .btn.success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
        }

        .btn.success:hover {
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
        }

        .btn.warning {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
        }

        .btn.warning:hover {
            box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);
        }

        .instructions {
            background: #f8fafc;
            padding: 2rem;
            border-radius: 10px;
            margin: 2rem 0;
            text-align: right;
            border-right: 4px solid #3b82f6;
        }

        .instructions h3 {
            color: #1e3a8a;
            margin-bottom: 1rem;
        }

        .instructions ol {
            margin: 0;
            padding-right: 1.5rem;
        }

        .instructions li {
            margin-bottom: 0.5rem;
            line-height: 1.6;
        }

        .code-block {
            background: #1f2937;
            color: #f9fafb;
            padding: 1rem;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            text-align: left;
            margin: 1rem 0;
            overflow-x: auto;
        }

        .status {
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
            font-weight: bold;
        }

        .status.success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #10b981;
        }

        .status.error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #ef4444;
        }

        .status.info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #3b82f6;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 إصلاح جدول الإرسال</h1>
        
        <div class="instructions">
            <h3>📋 تعليمات الإصلاح:</h3>
            <ol>
                <li>افتح صفحة النظام الأساسي في تبويب جديد</li>
                <li>اضغط F12 لفتح وحدة التحكم</li>
                <li>انسخ والصق الكود أدناه</li>
                <li>اضغط Enter لتشغيل الإصلاح</li>
                <li>انقر على "جدول الإرسال" في القائمة</li>
            </ol>
        </div>

        <div class="code-block" id="fix-code">
// إصلاح فوري لجدول الإرسال
function fixTransmissionTable() {
    console.log('🔧 بدء إصلاح جدول الإرسال...');
    
    // 1. إنشاء/إصلاح بيانات التطبيق
    if (typeof window.appData === 'undefined') {
        window.appData = {
            transmissionTable: [],
            settings: {
                company: {
                    name: 'مركز وقود المستقبل - عزيري عبد الله اسحاق',
                    number: '463/2019',
                    directorateName: 'مدير الصناعة و المناجم لولاية المدية'
                }
            }
        };
    }
    
    // 2. إصلاح وظيفة إظهار جدول الإرسال
    window.showTransmissionTable = function() {
        // إخفاء جميع الأقسام
        document.querySelectorAll('main section').forEach(section => {
            section.classList.remove('active-section');
            section.style.display = 'none';
        });
        
        // إظهار قسم جدول الإرسال
        const transmissionSection = document.getElementById('transmission-table');
        if (transmissionSection) {
            transmissionSection.classList.add('active-section');
            transmissionSection.style.display = 'block';
            
            // تحديث القائمة
            document.querySelectorAll('nav a').forEach(link => {
                link.classList.remove('active');
            });
            const transmissionLink = document.querySelector('a[data-section="transmission-table"]');
            if (transmissionLink) {
                transmissionLink.classList.add('active');
            }
            
            console.log('✅ تم إظهار جدول الإرسال');
        }
    };
    
    // 3. إضافة زر إظهار مباشر
    const showButton = document.createElement('button');
    showButton.innerHTML = '📋 إظهار جدول الإرسال';
    showButton.style.cssText = `
        position: fixed;
        top: 10px;
        right: 10px;
        z-index: 9999;
        background: #10b981;
        color: white;
        border: none;
        padding: 10px 15px;
        border-radius: 5px;
        cursor: pointer;
        font-weight: bold;
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    `;
    showButton.onclick = window.showTransmissionTable;
    document.body.appendChild(showButton);
    
    console.log('🎉 تم إصلاح جدول الإرسال بنجاح!');
}

// تشغيل الإصلاح
fixTransmissionTable();
        </div>

        <div class="status info">
            <strong>💡 نصيحة:</strong> انسخ الكود أعلاه بالكامل والصقه في وحدة التحكم
        </div>

        <button class="btn" onclick="copyCode()">📋 نسخ الكود</button>
        <button class="btn success" onclick="openDashboard()">🚀 فتح النظام</button>
        <button class="btn warning" onclick="openSimpleTest()">🧪 فتح الاختبار المبسط</button>

        <div id="status-message" class="status" style="display: none;"></div>
    </div>

    <script>
        function copyCode() {
            const codeBlock = document.getElementById('fix-code');
            const textArea = document.createElement('textarea');
            textArea.value = codeBlock.textContent;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            
            showStatus('تم نسخ الكود بنجاح! الصقه في وحدة التحكم', 'success');
        }

        function openDashboard() {
            window.open('customer-package/app/dashboard.html', '_blank');
            showStatus('تم فتح النظام في تبويب جديد', 'info');
        }

        function openSimpleTest() {
            window.open('simple-transmission-test.html', '_blank');
            showStatus('تم فتح الاختبار المبسط في تبويب جديد', 'info');
        }

        function showStatus(message, type) {
            const statusDiv = document.getElementById('status-message');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
            statusDiv.style.display = 'block';
            
            setTimeout(() => {
                statusDiv.style.display = 'none';
            }, 3000);
        }

        // إضافة تعليمات تفاعلية
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔧 صفحة إصلاح جدول الإرسال جاهزة');
        });
    </script>
</body>
</html>
