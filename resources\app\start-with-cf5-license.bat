@echo off
chcp 65001 >nul
title نظام إدارة مؤسسة وقود المستقبل - CF5 License System

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                   🚀 نظام الترخيص المحدث CF5                ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🔐 نظام الترخيص المتقدم مع:
echo    ✅ واجهة تسجيل دخول محسنة
echo    ✅ لوحة تحكم إدارية شاملة  
echo    ✅ نظام طلب التفعيل التلقائي
echo    ✅ إدارة التراخيص المتقدمة
echo.

:MENU
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                        🎯 الخيارات المتاحة                   ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo [1] 👤 فتح واجهة تسجيل الدخول للعملاء
echo [2] 🛠️  فتح لوحة التحكم الإدارية للمطور
echo [3] 🌐 تشغيل الخادم المحلي
echo [4] 📖 عرض دليل الاستخدام
echo [5] 🔧 إعدادات النظام
echo [6] ❌ خروج
echo.

set /p choice="اختر رقم الخيار: "

if "%choice%"=="1" goto CLIENT_LOGIN
if "%choice%"=="2" goto ADMIN_PANEL
if "%choice%"=="3" goto START_SERVER
if "%choice%"=="4" goto SHOW_GUIDE
if "%choice%"=="5" goto SETTINGS
if "%choice%"=="6" goto EXIT

echo ❌ خيار غير صحيح! يرجى المحاولة مرة أخرى.
timeout /t 2 >nul
goto MENU

:CLIENT_LOGIN
echo.
echo 🔄 فتح واجهة تسجيل الدخول للعملاء...
start "" "login-cf5.html"
echo ✅ تم فتح واجهة تسجيل الدخول
echo.
pause
goto MENU

:ADMIN_PANEL
echo.
echo 🔄 فتح لوحة التحكم الإدارية...
start "" "admin-control-panel.html"
echo ✅ تم فتح لوحة التحكم الإدارية
echo.
pause
goto MENU

:START_SERVER
echo.
echo 🔄 تشغيل الخادم المحلي...
echo 📡 سيتم تشغيل الخادم على المنفذ 3000
echo 🌐 يمكن الوصول للنظام عبر: http://localhost:3000
echo.

if exist "node_modules" (
    echo ✅ تم العثور على Node.js modules
    start /min cmd /c "node server.js"
    timeout /t 3 >nul
    start "" "http://localhost:3000"
    echo ✅ تم تشغيل الخادم وفتح المتصفح
) else (
    echo ⚠️  لم يتم العثور على Node.js modules
    echo 🔄 تثبيت المتطلبات...
    call npm install
    if errorlevel 1 (
        echo ❌ فشل في تثبيت المتطلبات
        echo 💡 تأكد من تثبيت Node.js أولاً
    ) else (
        start /min cmd /c "node server.js"
        timeout /t 3 >nul
        start "" "http://localhost:3000"
        echo ✅ تم تشغيل الخادم وفتح المتصفح
    )
)
echo.
pause
goto MENU

:SHOW_GUIDE
echo.
echo 📖 عرض دليل الاستخدام...
if exist "LICENSE_SYSTEM_CF5_GUIDE.md" (
    start "" "LICENSE_SYSTEM_CF5_GUIDE.md"
    echo ✅ تم فتح دليل الاستخدام
) else (
    echo ❌ لم يتم العثور على دليل الاستخدام
    echo 💡 تأكد من وجود الملف: LICENSE_SYSTEM_CF5_GUIDE.md
)
echo.
pause
goto MENU

:SETTINGS
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                        ⚙️ إعدادات النظام                    ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo [1] 🔧 فتح مجلد النظام
echo [2] 📁 فتح مجلد البيانات
echo [3] 🗂️  فتح مجلد الأصول
echo [4] 🔙 العودة للقائمة الرئيسية
echo.

set /p settings_choice="اختر رقم الخيار: "

if "%settings_choice%"=="1" (
    echo 🔄 فتح مجلد النظام...
    start "" "%cd%"
    echo ✅ تم فتح مجلد النظام
)
if "%settings_choice%"=="2" (
    echo 🔄 فتح مجلد البيانات...
    if exist "data" (
        start "" "data"
        echo ✅ تم فتح مجلد البيانات
    ) else (
        echo ❌ مجلد البيانات غير موجود
    )
)
if "%settings_choice%"=="3" (
    echo 🔄 فتح مجلد الأصول...
    if exist "assets" (
        start "" "assets"
        echo ✅ تم فتح مجلد الأصول
    ) else (
        echo ❌ مجلد الأصول غير موجود
    )
)
if "%settings_choice%"=="4" goto MENU

echo.
pause
goto MENU

:EXIT
echo.
echo 👋 شكراً لاستخدام نظام إدارة مؤسسة وقود المستقبل
echo 📞 للدعم الفني: 0696924176
echo.
timeout /t 3 >nul
exit

:ERROR
echo.
echo ❌ حدث خطأ في تشغيل النظام
echo 💡 تأكد من:
echo    - وجود جميع الملفات المطلوبة
echo    - تثبيت Node.js (إذا كنت تستخدم الخادم)
echo    - صلاحيات تشغيل الملفات
echo.
echo 📞 للمساعدة اتصل بـ: 0696924176
pause
goto MENU
