<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام إدارة مؤسسة وقود المستقبل</title>
    <link rel="icon" type="image/x-icon" href="assets/favicon.ico">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Database Sync System -->
    <script src="database-sync.js"></script>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            direction: rtl;
            overflow: hidden;
            position: relative;
        }

        /* خلفية متحركة */
        .background-animation {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 0;
        }

        .floating-shapes {
            position: absolute;
            width: 100%;
            height: 100%;
        }

        .shape {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        .shape:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .shape:nth-child(2) {
            width: 120px;
            height: 120px;
            top: 60%;
            left: 80%;
            animation-delay: 2s;
        }

        .shape:nth-child(3) {
            width: 60px;
            height: 60px;
            top: 80%;
            left: 20%;
            animation-delay: 4s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        /* الحاوية الرئيسية */
        .login-container {
            position: relative;
            z-index: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            padding: 2rem;
        }

        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
            overflow: hidden;
            width: 100%;
            max-width: 500px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        /* رأس البطاقة */
        .login-header {
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            color: white;
            padding: 2.5rem;
            text-align: center;
            position: relative;
        }

        .login-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .login-logo {
            position: relative;
            z-index: 1;
            width: 90px;
            height: 90px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            font-size: 2.5rem;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
        }

        .login-title {
            position: relative;
            z-index: 1;
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .login-subtitle {
            position: relative;
            z-index: 1;
            font-size: 1.1rem;
            opacity: 0.9;
        }

        /* محتوى النموذج */
        .login-body {
            padding: 3rem;
        }

        .form-group {
            margin-bottom: 2rem;
            position: relative;
        }

        .form-label {
            display: block;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 0.75rem;
            font-size: 1.1rem;
        }

        .form-input-container {
            position: relative;
        }

        .form-input {
            width: 100%;
            padding: 1.25rem 1.25rem 1.25rem 3.5rem;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            background: #f8f9fa;
            font-family: 'Courier New', monospace;
        }

        .form-input:focus {
            outline: none;
            border-color: #3498db;
            background: white;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        .form-icon {
            position: absolute;
            left: 1.25rem;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
            font-size: 1.3rem;
        }

        .form-input:focus + .form-icon {
            color: #3498db;
        }

        /* معرف الجهاز */
        .device-info {
            background: #e8f4fd;
            border-radius: 15px;
            padding: 1.5rem;
            border-left: 4px solid #3498db;
            margin-bottom: 2rem;
        }

        .device-label {
            font-size: 0.9rem;
            color: #6c757d;
            margin-bottom: 0.5rem;
            font-weight: 600;
        }

        .device-value {
            font-family: 'Courier New', monospace;
            font-size: 1rem;
            color: #2c3e50;
            font-weight: 600;
            word-break: break-all;
            cursor: pointer;
            padding: 0.5rem;
            background: white;
            border-radius: 8px;
            border: 1px solid #ddd;
            transition: all 0.3s ease;
        }

        .device-value:hover {
            background: #f8f9fa;
            border-color: #3498db;
        }

        /* زر تسجيل الدخول */
        .login-btn {
            width: 100%;
            padding: 1.5rem;
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            border: none;
            border-radius: 15px;
            font-size: 1.2rem;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            position: relative;
            overflow: hidden;
        }

        .login-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .login-btn:hover::before {
            left: 100%;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(52, 152, 219, 0.3);
        }

        .login-btn:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
            transform: none;
        }

        /* تأثيرات التحميل */
        .loading-spinner {
            display: none;
            width: 20px;
            height: 20px;
            border: 2px solid #ffffff;
            border-top: 2px solid transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* معلومات إضافية */
        .info-section {
            background: #f8f9fa;
            padding: 1.5rem;
            text-align: center;
            border-top: 1px solid #e9ecef;
        }

        .info-text {
            color: #6c757d;
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }

        .version-info {
            color: #495057;
            font-size: 0.8rem;
            font-weight: 600;
        }

        /* إشعارات */
        .notification {
            position: fixed;
            top: 20px;
            left: 20px;
            background: white;
            border-radius: 12px;
            padding: 1rem 1.5rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            z-index: 10000;
            transform: translateX(-400px);
            opacity: 0;
            transition: all 0.4s ease;
            min-width: 320px;
            border-left: 5px solid #3498db;
        }

        .notification.show {
            transform: translateX(0);
            opacity: 1;
        }

        .notification.success { border-left-color: #27ae60; }
        .notification.error { border-left-color: #e74c3c; }
        .notification.warning { border-left-color: #f39c12; }

        .notification-content {
            display: flex;
            align-items: center;
            gap: 1rem;
            color: #2c3e50;
        }

        /* استجابة للشاشات الصغيرة */
        @media (max-width: 600px) {
            .login-container {
                padding: 1rem;
            }

            .login-card {
                max-width: 100%;
            }

            .login-header {
                padding: 2rem;
            }

            .login-body {
                padding: 2rem;
            }
        }
    </style>
</head>
<body>
    <!-- خلفية متحركة -->
    <div class="background-animation">
        <div class="floating-shapes">
            <div class="shape"></div>
            <div class="shape"></div>
            <div class="shape"></div>
        </div>
    </div>

    <!-- الحاوية الرئيسية -->
    <div class="login-container">
        <div class="login-card">
            <!-- رأس البطاقة -->
            <div class="login-header">
                <div class="login-logo">
                    <i class="fas fa-gas-pump"></i>
                </div>
                <h1 class="login-title">نظام إدارة مؤسسة وقود المستقبل</h1>
                <p class="login-subtitle">Future Fuel Corporation Management System</p>
            </div>

            <!-- محتوى النموذج -->
            <div class="login-body">
                <form id="loginForm">
                    <div class="form-group">
                        <label class="form-label">كود الترخيص</label>
                        <div class="form-input-container">
                            <input type="text" id="licenseCode" class="form-input" placeholder="أدخل كود الترخيص (مثال: XXXX-XXXX-XXXX-XXXX)" required>
                            <i class="fas fa-key form-icon"></i>
                        </div>
                    </div>

                    <!-- معرف الجهاز -->
                    <div class="device-info">
                        <div class="device-label">
                            معرف الجهاز
                            <button type="button" onclick="forceRefreshDeviceId()" style="background: #ff9800; color: white; border: none; padding: 2px 6px; border-radius: 3px; cursor: pointer; font-size: 10px; margin-right: 5px;" title="إعادة تحميل معرف الجهاز">
                                <i class="fas fa-refresh"></i>
                            </button>
                        </div>
                        <div class="device-value" id="deviceId" title="انقر للنسخ">جاري التحميل...</div>
                    </div>

                    <button type="submit" class="login-btn">
                        <i class="fas fa-sign-in-alt"></i>
                        تسجيل الدخول
                        <div class="loading-spinner" id="loginSpinner"></div>
                    </button>
                </form>
            </div>

            <!-- معلومات إضافية -->
            <div class="info-section">
                <div class="info-text">
                    <i class="fas fa-info-circle"></i>
                    يرجى إدخال كود الترخيص الصحيح للوصول إلى النظام
                </div>

                <!-- روابط سريعة -->
                <div style="margin: 1rem 0; display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
                    <button onclick="openDeveloperPanel()" style="background: #e74c3c; color: white; border: none; padding: 0.5rem 1rem; border-radius: 8px; cursor: pointer; font-size: 0.8rem; transition: all 0.3s ease;">
                        <i class="fas fa-code"></i> لوحة المطور
                    </button>
                    <button onclick="showLicenseHelp()" style="background: #f39c12; color: white; border: none; padding: 0.5rem 1rem; border-radius: 8px; cursor: pointer; font-size: 0.8rem; transition: all 0.3s ease;">
                        <i class="fas fa-question-circle"></i> مساعدة
                    </button>
                    <button onclick="showSystemInfo()" style="background: #27ae60; color: white; border: none; padding: 0.5rem 1rem; border-radius: 8px; cursor: pointer; font-size: 0.8rem; transition: all 0.3s ease;">
                        <i class="fas fa-info"></i> معلومات النظام
                    </button>
                    <button onclick="showRemoteActivationHelp()" style="background: #9c27b0; color: white; border: none; padding: 0.5rem 1rem; border-radius: 8px; cursor: pointer; font-size: 0.8rem; transition: all 0.3s ease;">
                        <i class="fas fa-satellite-dish"></i> التفعيل عن بُعد
                    </button>
                </div>

                <div class="version-info">
                    الإصدار 2.2.0 - جميع الحقوق محفوظة
                </div>
            </div>
        </div>
    </div>

    <script>
        // توليد معرف الجهاز المحسن - يدعم جميع أنواع الأجهزة
        function generateDeviceId() {
            console.log('🔧 بدء توليد معرف الجهاز...');

            // محاولة الحصول على معرف الجهاز الحقيقي من النظام
            let deviceId = '';

            try {
                // معرف فريد من معلومات النظام
                const platform = navigator.platform || 'UNKNOWN';
                const userAgent = navigator.userAgent || '';
                const language = navigator.language || 'en';
                const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone || 'UTC';
                const screenInfo = `${window.screen ? window.screen.width : 1920}x${window.screen ? window.screen.height : 1080}`;

                console.log('📊 معلومات النظام:');
                console.log('- المنصة:', platform);
                console.log('- المتصفح:', userAgent.substring(0, 50) + '...');
                console.log('- اللغة:', language);
                console.log('- المنطقة الزمنية:', timezone);
                console.log('- الشاشة:', screenInfo);

                // استخراج معلومات النظام
                let osType = 'UNK';
                if (platform.includes('Win')) osType = 'WIN';
                else if (platform.includes('Mac')) osType = 'MAC';
                else if (platform.includes('Linux')) osType = 'LNX';
                else if (platform.includes('Android')) osType = 'AND';
                else if (platform.includes('iPhone') || platform.includes('iPad')) osType = 'IOS';

                // معرف المتصفح
                let browserType = 'UNK';
                if (userAgent.includes('Chrome') && !userAgent.includes('Edge')) browserType = 'CHR';
                else if (userAgent.includes('Firefox')) browserType = 'FFX';
                else if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) browserType = 'SAF';
                else if (userAgent.includes('Edge')) browserType = 'EDG';
                else if (userAgent.includes('Opera')) browserType = 'OPR';

                console.log('🔍 تم تحديد النظام:', osType, 'والمتصفح:', browserType);

                // إنشاء hash فريد من معلومات الجهاز
                const deviceInfo = `${platform}-${userAgent}-${language}-${timezone}-${screenInfo}`;
                let hash = '';

                try {
                    hash = btoa(deviceInfo).replace(/[^A-Z0-9]/g, '').substr(0, 8);
                } catch (e) {
                    // طريقة بديلة لإنشاء hash
                    hash = Math.random().toString(36).substr(2, 8).toUpperCase();
                }

                // السنة الحالية
                const year = new Date().getFullYear();

                // تكوين معرف الجهاز
                deviceId = `${osType}-${browserType}-${hash}-${year}`;

                console.log('✅ تم توليد معرف الجهاز بنجاح:', deviceId);

            } catch (error) {
                console.error('❌ فشل في توليد معرف جهاز متقدم:', error);
                console.log('🔄 استخدام الطريقة البديلة...');

                // طريقة بديلة بسيطة
                const timestamp = Date.now().toString(36);
                const random = Math.random().toString(36).substr(2, 8);
                deviceId = `DEV-GEN-${timestamp.toUpperCase()}-${random.toUpperCase()}`;

                console.log('✅ تم توليد معرف جهاز بديل:', deviceId);
            }

            const finalDeviceId = deviceId.toUpperCase();
            console.log('🎯 معرف الجهاز النهائي:', finalDeviceId);

            return finalDeviceId;
        }

        // تحديث معرف الجهاز
        function updateDeviceId() {
            console.log('🔄 بدء تحديث معرف الجهاز...');

            let deviceId = localStorage.getItem('deviceId');
            console.log('📱 معرف الجهاز المحفوظ:', deviceId);

            if (!deviceId) {
                console.log('⚡ توليد معرف جهاز جديد...');
                deviceId = generateDeviceId();
                localStorage.setItem('deviceId', deviceId);
                console.log('💾 تم حفظ معرف الجهاز الجديد:', deviceId);
            }

            const deviceElement = document.getElementById('deviceId');
            if (deviceElement) {
                deviceElement.textContent = deviceId;
                console.log('✅ تم تحديث عنصر معرف الجهاز في الصفحة');
            } else {
                console.error('❌ لم يتم العثور على عنصر معرف الجهاز في الصفحة');
                // محاولة إعادة البحث بعد تأخير قصير
                setTimeout(() => {
                    const retryElement = document.getElementById('deviceId');
                    if (retryElement) {
                        retryElement.textContent = deviceId;
                        console.log('✅ تم تحديث معرف الجهاز في المحاولة الثانية');
                    } else {
                        console.error('❌ فشل في العثور على عنصر معرف الجهاز حتى في المحاولة الثانية');
                    }
                }, 500);
            }

            // إضافة زر لتخصيص معرف الجهاز
            setTimeout(() => {
                addCustomDeviceIdOption();
            }, 100);

            console.log('🎯 انتهى تحديث معرف الجهاز:', deviceId);
        }

        // إضافة خيار تخصيص معرف الجهاز
        function addCustomDeviceIdOption() {
            const deviceIdElement = document.getElementById('deviceId');
            if (deviceIdElement && !document.getElementById('customDeviceBtn')) {
                const customBtn = document.createElement('button');
                customBtn.id = 'customDeviceBtn';
                customBtn.innerHTML = '<i class="fas fa-edit"></i> تخصيص معرف الجهاز';
                customBtn.style.cssText = `
                    background: #ff9800;
                    color: white;
                    border: none;
                    padding: 5px 10px;
                    border-radius: 3px;
                    cursor: pointer;
                    font-size: 12px;
                    margin-top: 5px;
                    display: block;
                    width: 100%;
                `;
                customBtn.onclick = showCustomDeviceIdDialog;
                deviceIdElement.parentElement.appendChild(customBtn);
            }
        }

        // عرض نافذة تخصيص معرف الجهاز
        function showCustomDeviceIdDialog() {
            const currentDeviceId = document.getElementById('deviceId').textContent;

            const customId = prompt(`
أدخل معرف الجهاز المخصص:

أمثلة على التنسيقات المدعومة:
• WIN-CHR-4F27B665-2025
• MAC-SAF-A1B2C3D4-2024
• LNX-FFX-12345678-2025
• DEV-GEN-ABCD1234-2024

معرف الجهاز الحالي: ${currentDeviceId}
            `.trim(), currentDeviceId);

            if (customId && customId.trim() && customId !== currentDeviceId) {
                const cleanId = customId.trim().toUpperCase();

                // التحقق من صحة التنسيق
                if (isValidDeviceId(cleanId)) {
                    localStorage.setItem('deviceId', cleanId);
                    document.getElementById('deviceId').textContent = cleanId;
                    showNotification(`تم تحديث معرف الجهاز إلى: ${cleanId}`, 'success');
                } else {
                    showNotification('تنسيق معرف الجهاز غير صالح. يجب أن يكون بالشكل: XXX-XXX-XXXXXXXX-XXXX', 'error');
                }
            }
        }

        // التحقق من صحة معرف الجهاز
        function isValidDeviceId(deviceId) {
            // قبول تنسيقات متعددة
            const patterns = [
                /^[A-Z]{3}-[A-Z]{3}-[A-Z0-9]{8}-[0-9]{4}$/,  // WIN-CHR-4F27B665-2025
                /^DEV-[A-Z0-9]{4}-[A-Z0-9]{4,}-[A-Z0-9]{4,}$/,  // DEV-XXXX-XXXXX-XXXX
                /^[A-Z0-9]{3,}-[A-Z0-9]{3,}-[A-Z0-9]{4,}-[A-Z0-9]{4,}$/,  // عام
                /^[A-Z0-9\-]{10,}$/  // أي معرف يحتوي على أحرف وأرقام وشرطات بطول 10+ أحرف
            ];

            return patterns.some(pattern => pattern.test(deviceId));
        }

        // نسخ معرف الجهاز
        function copyDeviceId() {
            const deviceId = document.getElementById('deviceId').textContent;
            navigator.clipboard.writeText(deviceId).then(() => {
                showNotification('تم نسخ معرف الجهاز', 'success');
            }).catch(() => {
                // طريقة بديلة للنسخ
                const textArea = document.createElement('textarea');
                textArea.value = deviceId;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showNotification('تم نسخ معرف الجهاز', 'success');
            });
        }

        // إعادة تحميل معرف الجهاز بالقوة
        function forceRefreshDeviceId() {
            console.log('🔄 إعادة تحميل معرف الجهاز بالقوة...');

            // مسح معرف الجهاز المحفوظ
            localStorage.removeItem('deviceId');

            // تحديث النص مؤقتاً
            const deviceElement = document.getElementById('deviceId');
            if (deviceElement) {
                deviceElement.textContent = 'جاري إعادة التحميل...';
            }

            // توليد معرف جديد بعد تأخير قصير
            setTimeout(() => {
                updateDeviceId();
                showNotification('تم إعادة تحميل معرف الجهاز بنجاح', 'success');
            }, 500);
        }

        // قاعدة بيانات التراخيص الصالحة - مرتبطة مع لوحة التحكم
        function getValidLicenses() {
            // محاولة تحميل من لوحة التحكم أولاً
            const adminLicenses = localStorage.getItem('validLicenses');
            if (adminLicenses) {
                return JSON.parse(adminLicenses);
            }

            // إذا لم توجد، إنشاء تراخيص افتراضية
            return createDefaultLicenses();
        }

        // إنشاء تراخيص افتراضية
        function createDefaultLicenses() {
            const defaultLicenses = [
                {
                    code: 'DEMO-2024-TEST-0001',
                    type: 'demo',
                    expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
                    isActive: true,
                    deviceId: null,
                    activatedAt: null,
                    createdAt: new Date().toISOString(),
                    notes: 'ترخيص تجريبي افتراضي',
                    maxDevices: 1,
                    features: ['basic', 'reports', 'backup']
                },
                {
                    code: 'FULL-2024-PROD-0001',
                    type: 'yearly',
                    expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
                    isActive: true,
                    deviceId: null,
                    activatedAt: null,
                    createdAt: new Date().toISOString(),
                    notes: 'ترخيص سنوي افتراضي',
                    maxDevices: 1,
                    features: ['all']
                },
                {
                    code: 'ADMIN-2024-CTRL-0001',
                    type: 'admin',
                    expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
                    isActive: true,
                    deviceId: null,
                    activatedAt: null,
                    createdAt: new Date().toISOString(),
                    notes: 'ترخيص إداري للمطورين',
                    maxDevices: 5,
                    features: ['all', 'admin', 'developer']
                }
            ];

            // حفظ في localStorage
            localStorage.setItem('validLicenses', JSON.stringify(defaultLicenses));
            return defaultLicenses;

            // تراخيص افتراضية للاختبار
            const defaultLicenses = [
                {
                    code: 'DEMO-2024-TEST-0001',
                    type: 'demo',
                    expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 يوم
                    isActive: true,
                    deviceId: null,
                    activatedAt: null
                },
                {
                    code: 'FULL-2024-PROD-0001',
                    type: 'full',
                    expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(), // سنة
                    isActive: true,
                    deviceId: null,
                    activatedAt: null
                }
            ];

            localStorage.setItem('validLicenses', JSON.stringify(defaultLicenses));
            return defaultLicenses;
        }

        // التحقق من صحة الترخيص
        function validateLicense(licenseCode, deviceId) {
            console.log('التحقق من الترخيص:', licenseCode);

            const licenses = getValidLicenses();
            console.log('التراخيص المتاحة:', licenses);

            const license = licenses.find(l => l.code === licenseCode);
            console.log('الترخيص الموجود:', license);

            if (!license) {
                console.log('الترخيص غير موجود');
                return { valid: false, message: 'كود الترخيص غير صحيح' };
            }

            if (!license.isActive) {
                console.log('الترخيص غير نشط');
                return { valid: false, message: 'هذا الترخيص معطل' };
            }

            const now = new Date();
            const expiryDate = new Date(license.expiresAt);
            console.log('تاريخ الانتهاء:', expiryDate, 'التاريخ الحالي:', now);

            if (now > expiryDate) {
                console.log('الترخيص منتهي الصلاحية');
                return { valid: false, message: 'انتهت صلاحية هذا الترخيص' };
            }

            // التحقق من ربط الجهاز
            if (license.deviceId && license.deviceId !== deviceId) {
                console.log('الترخيص مرتبط بجهاز آخر');
                return { valid: false, message: 'هذا الترخيص مرتبط بجهاز آخر' };
            }

            console.log('الترخيص صالح');
            return {
                valid: true,
                license: license,
                message: 'تم التحقق من الترخيص بنجاح'
            };
        }

        // ربط الترخيص بالجهاز
        function bindLicenseToDevice(licenseCode, deviceId) {
            const licenses = getValidLicenses();
            const licenseIndex = licenses.findIndex(l => l.code === licenseCode);

            if (licenseIndex !== -1) {
                licenses[licenseIndex].deviceId = deviceId;
                licenses[licenseIndex].activatedAt = new Date().toISOString();
                localStorage.setItem('validLicenses', JSON.stringify(licenses));
            }
        }

        // معالجة نموذج تسجيل الدخول
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const licenseCode = document.getElementById('licenseCode').value.trim();
            const deviceId = document.getElementById('deviceId').textContent;

            if (!licenseCode) {
                showNotification('يرجى إدخال كود الترخيص', 'warning');
                return;
            }

            // التحقق من تنسيق كود الترخيص (مرن أكثر)
            const licensePattern = /^[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4,}-[A-Z0-9]{4,}$/;
            if (!licensePattern.test(licenseCode)) {
                showNotification('تنسيق كود الترخيص غير صحيح. يجب أن يكون بالشكل: XXXX-XXXX-XXXX-XXXX', 'error');
                return;
            }

            // التحقق من صحة معرف الجهاز
            if (!isValidDeviceId(deviceId)) {
                showNotification('معرف الجهاز غير صالح. يرجى تحديث معرف الجهاز.', 'error');
                return;
            }

            // إظهار مؤشر التحميل
            const spinner = document.getElementById('loginSpinner');
            const loginBtn = document.querySelector('.login-btn');

            spinner.style.display = 'inline-block';
            loginBtn.disabled = true;

            try {
                console.log('بدء التحقق من الترخيص...');
                console.log('كود الترخيص المدخل:', licenseCode);
                console.log('معرف الجهاز:', deviceId);

                // محاكاة تأخير الشبكة
                await new Promise(resolve => setTimeout(resolve, 1500));

                // التحقق من صحة الترخيص
                const validation = validateLicense(licenseCode, deviceId);
                console.log('نتيجة التحقق:', validation);

                if (!validation.valid) {
                    console.log('فشل التحقق:', validation.message);
                    showNotification(validation.message, 'error');
                    return;
                }

                // ربط الترخيص بالجهاز إذا لم يكن مرتبطاً
                if (!validation.license.deviceId) {
                    bindLicenseToDevice(licenseCode, deviceId);
                }

                // حفظ معلومات الجلسة
                const sessionData = {
                    licenseCode: licenseCode,
                    deviceId: deviceId,
                    loginTime: new Date().toISOString(),
                    isActivated: true,
                    licenseType: validation.license.type,
                    expiresAt: validation.license.expiresAt,
                    sessionId: generateSessionId(),
                    userAgent: navigator.userAgent,
                    ipAddress: 'local'
                };

                localStorage.setItem('currentSession', JSON.stringify(sessionData));

                // تسجيل الجلسة في قاعدة بيانات لوحة التحكم
                if (window.DB && window.DB.registerSession) {
                    window.DB.registerSession(sessionData, validation.license);
                } else {
                    // النظام القديم كاحتياطي
                    registerSessionInDatabase(sessionData, validation.license);
                }

                showNotification(validation.message, 'success');

                // الانتقال للنظام الرئيسي بعد 2 ثانية
                console.log('سيتم الانتقال إلى dashboard.html خلال ثانيتين...');
                setTimeout(() => {
                    console.log('محاولة الانتقال إلى dashboard.html...');
                    try {
                        window.location.href = 'dashboard.html';
                    } catch (error) {
                        console.error('خطأ في الانتقال:', error);
                        showNotification('خطأ في فتح النظام الرئيسي', 'error');

                        // محاولة بديلة
                        setTimeout(() => {
                            window.location.replace('dashboard.html');
                        }, 1000);
                    }
                }, 2000);

            } catch (error) {
                showNotification('حدث خطأ في التحقق من الترخيص', 'error');
            } finally {
                // إخفاء مؤشر التحميل
                spinner.style.display = 'none';
                loginBtn.disabled = false;
            }
        });

        // عرض الإشعارات
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.innerHTML = `
                <div class="notification-content">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
                    <span>${message}</span>
                </div>
            `;

            document.body.appendChild(notification);

            // إظهار الإشعار
            setTimeout(() => {
                notification.classList.add('show');
            }, 100);

            // إزالة الإشعار تلقائياً
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    if (notification.parentElement) {
                        notification.remove();
                    }
                }, 400);
            }, 4000);
        }

        // إضافة ترخيص جديد (للاستخدام من لوحة المطور)
        function addNewLicense(licenseCode, type = 'full', durationDays = 365) {
            const licenses = getValidLicenses();

            // التحقق من عدم وجود الترخيص مسبقاً
            if (licenses.find(l => l.code === licenseCode)) {
                return { success: false, message: 'هذا الترخيص موجود مسبقاً' };
            }

            const newLicense = {
                code: licenseCode,
                type: type,
                expiresAt: new Date(Date.now() + durationDays * 24 * 60 * 60 * 1000).toISOString(),
                isActive: true,
                deviceId: null,
                activatedAt: null,
                createdAt: new Date().toISOString()
            };

            licenses.push(newLicense);
            localStorage.setItem('validLicenses', JSON.stringify(licenses));

            return { success: true, message: 'تم إضافة الترخيص بنجاح' };
        }

        // عرض التراخيص المتاحة (للمطورين)
        function showAvailableLicenses() {
            const licenses = getValidLicenses();
            console.log('التراخيص المتاحة:');
            licenses.forEach(license => {
                const status = license.deviceId ? `مرتبط بالجهاز: ${license.deviceId}` : 'غير مرتبط';
                const expiry = new Date(license.expiresAt).toLocaleDateString('ar-SA');
                console.log(`- ${license.code} (${license.type}) - انتهاء: ${expiry} - ${status}`);
            });
            return licenses;
        }

        // تهيئة النظام عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 بدء تهيئة النظام...');
            console.log('📄 DOM تم تحميله بالكامل');

            // التحقق من وجود العناصر المطلوبة
            const deviceElement = document.getElementById('deviceId');
            const licenseElement = document.getElementById('licenseCode');
            const formElement = document.getElementById('loginForm');

            console.log('🔍 فحص العناصر:');
            console.log('- عنصر معرف الجهاز:', deviceElement ? '✅ موجود' : '❌ مفقود');
            console.log('- عنصر كود الترخيص:', licenseElement ? '✅ موجود' : '❌ مفقود');
            console.log('- نموذج تسجيل الدخول:', formElement ? '✅ موجود' : '❌ مفقود');

            if (!deviceElement) {
                console.error('❌ عنصر معرف الجهاز غير موجود! سيتم إنشاؤه...');
                // محاولة إنشاء العنصر إذا لم يكن موجوداً
                const deviceInfo = document.querySelector('.device-info');
                if (deviceInfo) {
                    const newDeviceElement = document.createElement('div');
                    newDeviceElement.id = 'deviceId';
                    newDeviceElement.className = 'device-value';
                    newDeviceElement.title = 'انقر للنسخ';
                    newDeviceElement.textContent = 'جاري التحميل...';
                    deviceInfo.appendChild(newDeviceElement);
                    console.log('✅ تم إنشاء عنصر معرف الجهاز');
                }
            }

            updateDeviceId();

            // إضافة حدث النقر لنسخ معرف الجهاز
            document.getElementById('deviceId').addEventListener('click', copyDeviceId);

            // تنسيق كود الترخيص تلقائياً
            document.getElementById('licenseCode').addEventListener('input', function(e) {
                let value = e.target.value.replace(/[^A-Z0-9]/g, '').toUpperCase();
                let formatted = value.match(/.{1,4}/g)?.join('-') || value;
                if (formatted.length > 19) formatted = formatted.substr(0, 19);
                e.target.value = formatted;
            });

            // إضافة اختصارات لوحة المفاتيح للمطورين
            document.addEventListener('keydown', function(e) {
                // Ctrl + Shift + L لعرض التراخيص
                if (e.ctrlKey && e.shiftKey && e.key === 'L') {
                    e.preventDefault();
                    showAvailableLicenses();
                    showNotification('تم عرض التراخيص في وحدة التحكم (F12)', 'info');
                }

                // Ctrl + Shift + D لعرض معرف الجهاز
                if (e.ctrlKey && e.shiftKey && e.key === 'D') {
                    e.preventDefault();
                    const deviceId = document.getElementById('deviceId').textContent;
                    console.log('معرف الجهاز:', deviceId);
                    showNotification('تم عرض معرف الجهاز في وحدة التحكم', 'info');
                }

                // Ctrl + Shift + A لفتح لوحة التحكم الإدارية
                if (e.ctrlKey && e.shiftKey && e.key === 'A') {
                    e.preventDefault();
                    window.open('admin-control-panel.html', '_blank');
                    showNotification('تم فتح لوحة التحكم الإدارية', 'info');
                }
            });

            // رسالة ترحيب مع معلومات التراخيص التجريبية
            setTimeout(() => {
                showNotification('مرحباً بك في نظام إدارة مؤسسة وقود المستقبل', 'info');

                // عرض التراخيص التجريبية للمطورين
                setTimeout(() => {
                    const licenses = getValidLicenses();
                    console.log('التراخيص المتاحة عند التحميل:', licenses);

                    const demoLicense = licenses.find(l => l.type === 'demo');
                    if (demoLicense) {
                        showNotification(`ترخيص تجريبي متاح: ${demoLicense.code}`, 'warning');
                        console.log('ترخيص تجريبي:', demoLicense.code);
                    }

                    const yearlyLicense = licenses.find(l => l.type === 'yearly');
                    if (yearlyLicense) {
                        setTimeout(() => {
                            showNotification(`ترخيص سنوي متاح: ${yearlyLicense.code}`, 'info');
                            console.log('ترخيص سنوي:', yearlyLicense.code);
                        }, 1000);
                    }

                    const adminLicense = licenses.find(l => l.type === 'admin');
                    if (adminLicense) {
                        setTimeout(() => {
                            showNotification(`ترخيص إداري متاح: ${adminLicense.code}`, 'info');
                            console.log('ترخيص إداري:', adminLicense.code);
                        }, 2000);
                    }
                }, 2000);
            }, 1000);
        });

        // توليد معرف جلسة فريد
        function generateSessionId() {
            return 'SES-' + Date.now().toString(36) + '-' + Math.random().toString(36).substr(2, 9);
        }

        // تسجيل الجلسة في قاعدة بيانات لوحة التحكم
        function registerSessionInDatabase(sessionData, licenseInfo) {
            try {
                // تحديث بيانات الترخيص بمعلومات الجلسة
                const licenses = getValidLicenses();
                const licenseIndex = licenses.findIndex(l => l.code === sessionData.licenseCode);

                if (licenseIndex !== -1) {
                    licenses[licenseIndex].deviceId = sessionData.deviceId;
                    licenses[licenseIndex].activatedAt = sessionData.loginTime;
                    licenses[licenseIndex].lastActivity = sessionData.loginTime;
                    licenses[licenseIndex].sessionId = sessionData.sessionId;
                    licenses[licenseIndex].userAgent = sessionData.userAgent;

                    // حفظ التحديثات
                    localStorage.setItem('validLicenses', JSON.stringify(licenses));
                }

                // تسجيل الجلسة النشطة
                const activeSessions = JSON.parse(localStorage.getItem('activeSessions') || '[]');
                activeSessions.push({
                    sessionId: sessionData.sessionId,
                    licenseCode: sessionData.licenseCode,
                    deviceId: sessionData.deviceId,
                    startTime: sessionData.loginTime,
                    lastActivity: sessionData.loginTime,
                    isActive: true,
                    userAgent: sessionData.userAgent,
                    ipAddress: sessionData.ipAddress
                });

                localStorage.setItem('activeSessions', JSON.stringify(activeSessions));

                // تحديث إحصائيات النظام
                updateSystemStatistics();

                console.log('تم تسجيل الجلسة في قاعدة البيانات:', sessionData.sessionId);

            } catch (error) {
                console.error('خطأ في تسجيل الجلسة:', error);
            }
        }

        // تحديث إحصائيات النظام
        function updateSystemStatistics() {
            const licenses = getValidLicenses();
            const sessions = JSON.parse(localStorage.getItem('activeSessions') || '[]');

            const stats = {
                totalLicenses: licenses.length,
                activeLicenses: licenses.filter(l => l.deviceId).length,
                activeSessions: sessions.filter(s => s.isActive).length,
                expiredLicenses: licenses.filter(l => new Date(l.expiresAt) < new Date()).length,
                lastUpdate: new Date().toISOString()
            };

            localStorage.setItem('systemStatistics', JSON.stringify(stats));
        }

        // فتح لوحة التحكم الإدارية
        function openDeveloperPanel() {
            // التحقق من وجود ملف لوحة التحكم
            const adminPanelUrl = '../admin-control-panel.html';

            // محاولة فتح لوحة التحكم
            try {
                const adminWindow = window.open(adminPanelUrl, '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');

                if (adminWindow) {
                    showNotification('تم فتح لوحة التحكم الإدارية', 'success');

                    // إرسال بيانات النظام للوحة التحكم
                    setTimeout(() => {
                        try {
                            adminWindow.postMessage({
                                type: 'SYSTEM_DATA',
                                data: {
                                    licenses: getValidLicenses(),
                                    sessions: JSON.parse(localStorage.getItem('activeSessions') || '[]'),
                                    statistics: JSON.parse(localStorage.getItem('systemStatistics') || '{}')
                                }
                            }, '*');
                        } catch (error) {
                            console.log('لا يمكن إرسال البيانات للوحة التحكم:', error);
                        }
                    }, 1000);
                } else {
                    // إذا فشل فتح النافذة، عرض رسالة للمطورين
                    showDeveloperPanelAlternative();
                }
            } catch (error) {
                // إذا لم توجد لوحة التحكم، عرض بديل
                showDeveloperPanelAlternative();
            }
        }

        // عرض بديل للوحة التحكم للمطورين
        function showDeveloperPanelAlternative() {
            const developerInfo = `
                <div style="text-align: right; line-height: 1.6;">
                    <h3>🔧 معلومات للمطورين</h3>
                    <br>
                    <div style="background: #fff3cd; padding: 15px; border-radius: 8px; border-left: 4px solid #ffc107; margin-bottom: 20px;">
                        <h4 style="margin: 0 0 10px 0; color: #856404;">📋 لوحة التحكم غير متاحة</h4>
                        <p style="margin: 0;">لوحة التحكم الإدارية غير مضمنة في حزمة العميل لأسباب أمنية.</p>
                    </div>

                    <h4>🎯 للوصول للوحة التحكم:</h4>
                    <ol style="line-height: 1.8;">
                        <li><strong>استخدم النسخة المخصصة للمطورين</strong></li>
                        <li><strong>افتح ملف admin-control-panel.html</strong> من مجلد المطور</li>
                        <li><strong>أو استخدم أدوات وحدة التحكم</strong> أدناه</li>
                    </ol>

                    <h4>⌨️ أوامر وحدة التحكم:</h4>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; font-family: monospace; font-size: 0.9rem;">
                        window.DB.getLicenses() // عرض التراخيص<br>
                        window.DB.getSessions() // عرض الجلسات<br>
                        window.DB.getDevices() // عرض الأجهزة<br>
                        window.DB.getStatistics() // عرض الإحصائيات
                    </div>

                    <h4>🔑 تراخيص سريعة للاختبار:</h4>
                    <div style="background: #e8f5e8; padding: 15px; border-radius: 8px;">
                        <p><strong>تجريبي:</strong> DEMO-2024-TEST-0001</p>
                        <p><strong>كامل:</strong> FULL-2024-PROD-0001</p>
                        <p><strong>إداري:</strong> ADMIN-2024-CTRL-0001</p>
                    </div>

                    <div style="background: #f3e5f5; padding: 15px; border-radius: 8px; margin-top: 20px;">
                        <h4 style="margin: 0 0 10px 0; color: #7b1fa2;">📞 للحصول على لوحة التحكم:</h4>
                        <p style="margin: 5px 0;"><strong>البريد:</strong> <EMAIL></p>
                        <p style="margin: 5px 0;"><strong>الهاتف:</strong> +966-11-123-4567</p>
                        <p style="margin: 5px 0;"><strong>الموقع:</strong> www.futurefuel.com/developer</p>
                    </div>
                </div>
            `;

            const devWindow = window.open('', '_blank', 'width=700,height=600,scrollbars=yes');
            devWindow.document.write(`
                <html dir="rtl">
                <head>
                    <title>معلومات المطورين</title>
                    <style>
                        body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
                        .dev-container { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                        ol { padding-right: 20px; }
                        li { margin-bottom: 8px; }
                    </style>
                </head>
                <body>
                    <div class="dev-container">${developerInfo}</div>
                </body>
                </html>
            `);

            showNotification('تم عرض معلومات المطورين - لوحة التحكم غير متاحة في حزمة العميل', 'info');
        }

        // عرض مساعدة الترخيص
        function showLicenseHelp() {
            const helpContent = `
                <div style="text-align: right; line-height: 1.6;">
                    <h3>🔑 مساعدة التراخيص</h3>
                    <br>
                    <strong>التراخيص المتاحة:</strong><br>
                    • DEMO-2024-TEST-0001 (تجريبي - 30 يوم)<br>
                    • FULL-2024-PROD-0001 (سنوي كامل)<br>
                    • ADMIN-2024-CTRL-0001 (إداري للمطورين)<br>
                    <br>
                    <strong>تنسيق كود الترخيص:</strong><br>
                    XXXX-XXXX-XXXX-XXXX<br>
                    <br>
                    <strong>للحصول على ترخيص:</strong><br>
                    📧 <EMAIL><br>
                    📞 +966-11-123-4567<br>
                    <br>
                    <strong>اختصارات المطورين:</strong><br>
                    • Ctrl+Shift+L: عرض التراخيص<br>
                    • Ctrl+Shift+D: عرض معرف الجهاز<br>
                    • Ctrl+Shift+A: فتح لوحة التحكم<br>
                </div>
            `;

            const helpWindow = window.open('', '_blank', 'width=500,height=600,scrollbars=yes');
            helpWindow.document.write(`
                <html dir="rtl">
                <head>
                    <title>مساعدة التراخيص</title>
                    <style>
                        body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
                        .help-container { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                    </style>
                </head>
                <body>
                    <div class="help-container">${helpContent}</div>
                </body>
                </html>
            `);
        }

        // عرض معلومات النظام
        function showSystemInfo() {
            const licenses = getValidLicenses();
            const sessions = JSON.parse(localStorage.getItem('activeSessions') || '[]');
            const stats = JSON.parse(localStorage.getItem('systemStatistics') || '{}');

            const systemInfo = `
                <div style="text-align: right; line-height: 1.6; font-family: monospace;">
                    <h3>📊 معلومات النظام</h3>
                    <br>
                    <strong>الإصدار:</strong> 2.2.0<br>
                    <strong>تاريخ البناء:</strong> ${new Date().toLocaleDateString('ar-SA')}<br>
                    <strong>معرف الجهاز:</strong> ${document.getElementById('deviceId').textContent}<br>
                    <br>
                    <strong>إحصائيات التراخيص:</strong><br>
                    • إجمالي التراخيص: ${licenses.length}<br>
                    • التراخيص النشطة: ${licenses.filter(l => l.deviceId).length}<br>
                    • التراخيص المنتهية: ${licenses.filter(l => new Date(l.expiresAt) < new Date()).length}<br>
                    <br>
                    <strong>الجلسات النشطة:</strong> ${sessions.filter(s => s.isActive).length}<br>
                    <br>
                    <strong>المتصفح:</strong> ${navigator.userAgent.split(' ')[0]}<br>
                    <strong>نظام التشغيل:</strong> ${navigator.platform}<br>
                    <strong>اللغة:</strong> ${navigator.language}<br>
                </div>
            `;

            const infoWindow = window.open('', '_blank', 'width=600,height=500,scrollbars=yes');
            infoWindow.document.write(`
                <html dir="rtl">
                <head>
                    <title>معلومات النظام</title>
                    <style>
                        body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
                        .info-container { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                    </style>
                </head>
                <body>
                    <div class="info-container">${systemInfo}</div>
                </body>
                </html>
            `);
        }

        // عرض دليل التفعيل عن بُعد
        function showRemoteActivationHelp() {
            const deviceId = document.getElementById('deviceId').textContent;

            const helpContent = `
                <div style="text-align: right; line-height: 1.6;">
                    <h3>🌐 دليل التفعيل عن بُعد</h3>
                    <br>
                    <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; border-left: 4px solid #2196f3; margin-bottom: 20px;">
                        <h4 style="margin: 0 0 10px 0; color: #1976d2;">📱 معرف جهازك:</h4>
                        <div style="font-family: monospace; font-size: 1.1rem; background: white; padding: 10px; border-radius: 5px; border: 1px solid #ddd; word-break: break-all;">
                            ${deviceId}
                        </div>
                        <button onclick="copyDeviceIdForRemote('${deviceId}')" style="background: #2196f3; color: white; border: none; padding: 8px 16px; border-radius: 5px; cursor: pointer; margin-top: 10px;">
                            <i class="fas fa-copy"></i> نسخ معرف الجهاز
                        </button>
                    </div>

                    <h4>🎯 للعملاء - كيفية الحصول على التفعيل عن بُعد:</h4>
                    <ol style="line-height: 1.8;">
                        <li><strong>انسخ معرف الجهاز</strong> الظاهر أعلاه</li>
                        <li><strong>اتصل بالدعم الفني</strong> على: +966-11-123-4567</li>
                        <li><strong>أرسل معرف الجهاز</strong> للمطور/المدير</li>
                        <li><strong>انتظر كود الترخيص</strong> عبر البريد/الواتساب</li>
                        <li><strong>أدخل الكود</strong> في حقل "كود الترخيص" أعلاه</li>
                        <li><strong>اضغط تسجيل الدخول</strong> وسيتم التفعيل فوراً</li>
                    </ol>

                    <h4>🔧 للمطورين/المديرين:</h4>
                    <ol style="line-height: 1.8;">
                        <li><strong>احصل على معرف الجهاز</strong> من العميل</li>
                        <li><strong>افتح لوحة التحكم الإدارية</strong></li>
                        <li><strong>انتقل لقسم "التفعيل عن بُعد"</strong></li>
                        <li><strong>أدخل معرف الجهاز</strong> واختر نوع الترخيص</li>
                        <li><strong>اضغط "تفعيل مباشرة"</strong></li>
                        <li><strong>أرسل كود الترخيص</strong> للعميل</li>
                    </ol>

                    <div style="background: #fff3cd; padding: 15px; border-radius: 8px; border-left: 4px solid #ffc107; margin-top: 20px;">
                        <h4 style="margin: 0 0 10px 0; color: #856404;">⚡ مزايا التفعيل عن بُعد:</h4>
                        <ul style="margin: 0; padding-right: 20px;">
                            <li>تفعيل فوري بدون انتظار</li>
                            <li>لا حاجة لإرسال كود مسبقاً</li>
                            <li>ترخيص مخصص لجهازك فقط</li>
                            <li>دعم فني مباشر</li>
                            <li>ضمان التفعيل الصحيح</li>
                        </ul>
                    </div>

                    <div style="background: #f3e5f5; padding: 15px; border-radius: 8px; border-left: 4px solid #9c27b0; margin-top: 20px;">
                        <h4 style="margin: 0 0 10px 0; color: #7b1fa2;">📞 معلومات التواصل:</h4>
                        <p style="margin: 5px 0;"><strong>الهاتف:</strong> +966-11-123-4567</p>
                        <p style="margin: 5px 0;"><strong>البريد:</strong> <EMAIL></p>
                        <p style="margin: 5px 0;"><strong>الواتساب:</strong> +966-50-123-4567</p>
                        <p style="margin: 5px 0;"><strong>ساعات العمل:</strong> الأحد-الخميس 8ص-6م</p>
                    </div>
                </div>
            `;

            const helpWindow = window.open('', '_blank', 'width=700,height=700,scrollbars=yes');
            helpWindow.document.write(`
                <html dir="rtl">
                <head>
                    <title>دليل التفعيل عن بُعد</title>
                    <style>
                        body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
                        .help-container { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                        ol, ul { padding-right: 20px; }
                        li { margin-bottom: 8px; }
                    </style>
                </head>
                <body>
                    <div class="help-container">${helpContent}</div>
                    <script>
                        function copyDeviceIdForRemote(deviceId) {
                            navigator.clipboard.writeText(deviceId).then(() => {
                                alert('تم نسخ معرف الجهاز! أرسله للمطور/المدير.');
                            }).catch(() => {
                                const textArea = document.createElement('textarea');
                                textArea.value = deviceId;
                                document.body.appendChild(textArea);
                                textArea.select();
                                document.execCommand('copy');
                                document.body.removeChild(textArea);
                                alert('تم نسخ معرف الجهاز! أرسله للمطور/المدير.');
                            });
                        }
                    </script>
                </body>
                </html>
            `);
        }

        // إتاحة الوظائف للاستخدام العام (للربط مع لوحة المطور)
        window.LicenseManager = {
            addLicense: addNewLicense,
            showLicenses: showAvailableLicenses,
            validateLicense: validateLicense,
            getValidLicenses: getValidLicenses,
            registerSession: registerSessionInDatabase,
            updateStatistics: updateSystemStatistics,
            openDeveloperPanel: openDeveloperPanel
        };
    </script>
</body>
</html>
