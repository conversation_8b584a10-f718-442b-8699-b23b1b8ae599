/* إعدادات عامة */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --accent-color: #e74c3c;
    --light-color: #ecf0f1;
    --dark-color: #2c3e50;
    --success-color: #2ecc71;
    --warning-color: #1fd261;
    --danger-color: #e74c3c;
    --border-radius: 5px;
    --box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);

    /* ألوان الوضع الفاتح */
    --bg-color: #f5f5f5;
    --card-bg-color: #ffffff;
    --text-color: #2c3e50;
    --border-color: #ddd;
    --hover-bg-color: #f9f9f9;
    --header-bg-color: #2c3e50;
    --header-text-color: #ffffff;
    --nav-bg-color: #2c3e50;
    --nav-text-color: #ffffff;
    --table-header-bg-color: #f2f2f2;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    background-color: var(--bg-color);
    color: var(--text-color);
    direction: rtl;
}

/* الوضع المظلم - التعريف الموحد */
body.dark-mode {
    --bg-color: #1a1a2e;
    --card-bg-color: #16213e;
    --text-color: #e6e6e6;
    --border-color: #444;
    --hover-bg-color: #0f3460;
    --header-bg-color: #0f3460;
    --header-text-color: #ffffff;
    --nav-bg-color: #0f3460;
    --nav-text-color: #ffffff;
    --table-header-bg-color: #16213e;
    --box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);

    /* متغيرات إضافية للوضع المظلم */
    --surface-color: #2d2d2d;
    --text-secondary: #b0b0b0;
    --shadow-color: rgba(0, 0, 0, 0.3);
    --header-bg: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --header-text: #ffffff;
    --sidebar-bg: #1e1e1e;
    --sidebar-text: #e0e0e0;
    --sidebar-hover: #333333;
}

.container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

/* الهيدر */
header {
    background-color: var(--header-bg-color);
    color: var(--header-text-color);
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: var(--box-shadow);
}

.header-left {
    display: flex;
    align-items: center;
}

.logo-container {
    display: flex;
    align-items: center;
    margin-left: 1rem;
}

.header-logo {
    height: 60px;
    width: auto;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.date-time {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    font-size: 0.9rem;
}

/* أزرار الهيدر */
#notifications-btn,
#dark-mode-toggle {
    background-color: transparent;
    color: var(--header-text-color);
    border: 1px solid rgba(255, 255, 255, 0.2);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

#notifications-btn:hover,
#dark-mode-toggle:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: scale(1.05);
}

/* شارة الإشعارات */
#notifications-btn .badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
    color: white;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 0.7rem;
    font-weight: bold;
    border: 2px solid white;
    animation: pulse 2s infinite;
}

#notifications-btn .badge.hidden {
    display: none;
}

/* مؤشر حالة الاتصال */
.connection-status {
    background: rgba(255, 255, 255, 0.1);
    color: var(--header-text-color);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.connection-status.connected {
    background: rgba(46, 204, 113, 0.2);
    border-color: rgba(46, 204, 113, 0.5);
    color: #2ecc71;
}

.connection-status.disconnected {
    background: rgba(231, 76, 60, 0.2);
    border-color: rgba(231, 76, 60, 0.5);
    color: #e74c3c;
}

.connection-status i {
    font-size: 0.9rem;
}

/* القائمة الجانبية */
nav {
    background-color: var(--nav-bg-color);
    color: var(--nav-text-color);
    padding: 0.5rem 0;
    box-shadow: var(--box-shadow);
}

nav ul {
    display: flex;
    list-style: none;
    justify-content: center;
}

nav ul li {
    margin: 0 0.5rem;
}

nav ul li a {
    color: var(--nav-text-color);
    text-decoration: none;
    padding: 0.5rem 1rem;
    display: flex;
    align-items: center;
    border-radius: var(--border-radius);
    transition: background-color 0.3s;
}

nav ul li a i {
    margin-left: 0.5rem;
}

nav ul li a:hover, nav ul li a.active {
    background-color: var(--secondary-color);
}

/* المحتوى الرئيسي */
main {
    flex: 1;
    padding: 1.5rem;
    background-color: var(--bg-color);
}

section {
    display: none;
    animation: fadeIn 0.5s;
}

section.active-section {
    display: block;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        will-change: opacity;
    }
    to {
        opacity: 1;
        will-change: auto;
    }
}

h2 {
    margin-bottom: 1.5rem;
    color: var(--primary-color);
    border-bottom: 2px solid var(--secondary-color);
    padding-bottom: 0.5rem;
}

h3 {
    margin-bottom: 1rem;
    color: var(--primary-color);
}

/* بطاقات لوحة التحكم المحسنة */
.dashboard-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    padding: 1.5rem;
    display: flex;
    align-items: center;
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    position: relative;
    overflow: hidden;
    color: white;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.card:hover::before {
    opacity: 1;
}

/* ألوان مختلفة للبطاقات */
.card.customers {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.card.vehicles {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}



.card.gas-cards {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.card.debts {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.card.appointments {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    color: #333;
}

.card-icon {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    width: 70px;
    height: 70px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 1.8rem;
    margin-left: 1rem;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.card-info {
    flex: 1;
}

.card-info h3 {
    margin-bottom: 0.5rem;
    font-size: 1.2rem;
    font-weight: 600;
}

.card-info p {
    margin-bottom: 0.25rem;
    opacity: 0.9;
    font-size: 0.95rem;
}

.highlight {
    color: #fff;
    font-weight: bold;
    font-size: 1.1rem;
}

/* جداول البيانات */
.dashboard-tables {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
    gap: 1.5rem;
}

.table-container {
    background-color: var(--card-bg-color);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    overflow-x: auto;
}

table {
    width: 100%;
    border-collapse: collapse;
}

th, td {
    padding: 0.75rem;
    text-align: right;
    border-bottom: 1px solid var(--border-color);
}

th {
    background-color: var(--table-header-bg-color);
    font-weight: bold;
}

tr:hover {
    background-color: var(--hover-bg-color);
}

/* شريط الإجراءات */
.action-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.search-container {
    display: flex;
    align-items: center;
}

.search-container input {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: var(--border-radius) 0 0 var(--border-radius);
    width: 250px;
}

.search-container button {
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
    margin-right: -1px;
}

/* الأزرار */
.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s;
    background-color: #f2f2f2;
    color: var(--dark-color);
}

.btn i {
    margin-left: 0.5rem;
}

.btn:hover {
    background-color: #e0e0e0;
}

.btn.primary {
    background-color: var(--secondary-color);
    color: white;
}

.btn.primary:hover {
    background-color: #2980b9;
}

.btn.danger {
    background-color: var(--danger-color);
    color: white;
}

.btn.danger:hover {
    background-color: #c0392b;
}

.btn.success {
    background-color: var(--success-color);
    color: white;
}

.btn.success:hover {
    background-color: #27ae60;
}

/* التقويم */
.calendar-container {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.calendar {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 0.5rem;
}

.calendar-day {
    border: 1px solid #ddd;
    padding: 0.5rem;
    min-height: 80px;
    border-radius: var(--border-radius);
    position: relative;
}

.calendar-day-header {
    text-align: center;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.calendar-day.today {
    background-color: #f0f8ff;
    border-color: var(--secondary-color);
    border-width: 2px;
}

.calendar-day.has-appointments {
    background-color: #f0fff0;
}

.calendar-day.past-day {
    opacity: 0.7;
}

.calendar-day.selected-day {
    background-color: #e6f7ff;
    border: 2px solid var(--secondary-color);
    box-shadow: 0 0 5px rgba(52, 152, 219, 0.5);
}

#today-btn {
    margin: 0 10px;
}

.appointment-dot {
    width: 8px;
    height: 8px;
    background-color: var(--secondary-color);
    border-radius: 50%;
    display: inline-block;
    margin-left: 2px;
}

/* النماذج */
.form-group {
    margin-bottom: 1rem;
}

.hidden-input {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: bold;
}

.form-group input, .form-group select, .form-group textarea {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    background-color: var(--card-bg-color);
    color: var(--text-color);
}

.form-group textarea {
    min-height: 100px;
    resize: vertical;
}

.checkbox-group {
    display: flex;
    align-items: center;
}

.checkbox-group label {
    display: flex;
    align-items: center;
    cursor: pointer;
    margin-bottom: 0;
}

.checkbox-group input[type="checkbox"] {
    width: auto;
    margin-left: 0.5rem;
}

.form-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
    margin-top: 1.5rem;
}

/* النوافذ المنبثقة */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    animation: fadeIn 0.3s;
}

.modal-content {
    background-color: var(--card-bg-color);
    margin: 5% auto;
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    width: 80%;
    max-width: 700px;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
    color: var(--text-color);
}

.close {
    position: absolute;
    top: 1rem;
    left: 1rem;
    font-size: 1.5rem;
    cursor: pointer;
}

/* نافذة معلومات الزبون */
.customer-info-container {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.customer-info-header {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #eee;
}

.customer-info-avatar {
    font-size: 4rem;
    color: var(--secondary-color);
}

.customer-info-details {
    flex: 1;
}

.customer-info-details h3 {
    margin-bottom: 0.5rem;
    font-size: 1.5rem;
}

.customer-info-details p {
    margin-bottom: 0.5rem;
    color: #666;
}

.customer-info-details p i {
    width: 20px;
    color: var(--secondary-color);
    margin-left: 0.5rem;
}

.customer-info-tabs {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.customer-info-actions {
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #eee;
}

#customer-info-modal .modal-content {
    max-width: 800px;
}

#customer-info-modal .tab-content {
    padding: 1rem 0;
}

#customer-info-modal .table-container {
    margin-bottom: 0;
    padding: 0;
    box-shadow: none;
}

#customer-info-modal table {
    margin-bottom: 0;
}

.text-success {
    color: var(--success-color);
}

.text-danger {
    color: var(--danger-color);
}

.text-warning {
    color: var(--warning-color);
}

/* الإعدادات */
.settings-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 1.5rem;
}

.settings-card {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 1.5rem;
}

.backup-controls {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.danger-zone {
    border-top: 1px solid #ddd;
    padding-top: 1.5rem;
    margin-top: 1.5rem;
}

.danger-zone h4 {
    color: var(--danger-color);
    margin-bottom: 1rem;
}

/* الإشعارات */
.toast {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    width: 300px;
    overflow: hidden;
    display: none;
    z-index: 1001;
}

.toast-content {
    display: flex;
    align-items: center;
    padding: 1rem;
}

.toast-icon {
    margin-left: 1rem;
    font-size: 1.5rem;
    color: var(--success-color);
}

.toast-message {
    flex: 1;
}

.toast-progress {
    height: 4px;
    background-color: var(--success-color);
    width: 100%;
    animation: progress 3s linear forwards;
}

@keyframes progress {
    from {
        transform: scaleX(1);
        transform-origin: left;
        will-change: transform;
    }
    to {
        transform: scaleX(0);
        transform-origin: left;
        will-change: auto;
    }
}

/* تنسيقات علامات التبويب */
.tabs {
    margin-bottom: 1.5rem;
}

.tab-buttons {
    display: flex;
    margin-bottom: 1rem;
    border-bottom: 1px solid #ddd;
}

.tab-btn {
    padding: 0.75rem 1.5rem;
    background-color: #f5f5f5;
    border: none;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    cursor: pointer;
    margin-right: 0.5rem;
    font-weight: bold;
    transition: all 0.3s;
}

.tab-btn.active {
    background-color: var(--secondary-color);
    color: white;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
    animation: fadeIn 0.5s;
}

/* تنسيقات الديون */
.debt-info {
    background-color: #f9f9f9;
    border-radius: var(--border-radius);
    padding: 1rem;
    margin: 1rem 0;
    border: 1px solid #ddd;
}

.debt-info p {
    margin-bottom: 0.5rem;
}

.debt-info p:last-child {
    margin-bottom: 0;
}

.text-center {
    text-align: center;
}

.text-danger {
    color: var(--danger-color);
    font-weight: bold;
}

.text-warning {
    color: #f39c12;
    font-weight: bold;
}

/* تنسيقات السيارات */
.vehicle-item {
    background-color: #f9f9f9;
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-bottom: 1rem;
    position: relative;
    border: 1px solid #ddd;
}

.vehicle-item .remove-vehicle {
    position: absolute;
    top: 0.5rem;
    left: 0.5rem;
    cursor: pointer;
    color: var(--danger-color);
    font-size: 1.2rem;
}

.vehicle-item .form-group {
    margin-bottom: 0.75rem;
}

.vehicle-item .form-group:last-child {
    margin-bottom: 0;
}

/* تصميم متجاوب */
@media (max-width: 768px) {
    nav ul {
        flex-direction: column;
    }

    nav ul li {
        margin: 0.25rem 0;
    }

    .dashboard-cards, .dashboard-tables {
        grid-template-columns: 1fr;
    }

    .modal-content {
        width: 95%;
    }
}

/* حالات البطاقات */
.status-active {
    color: var(--success-color);
}

.status-expiring {
    color: var(--warning-color);
}

.status-expired {
    color: var(--danger-color);
}

/* إضافة سيارة */
.vehicle-item {
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-bottom: 1rem;
    position: relative;
}

.remove-vehicle {
    position: absolute;
    top: 0.5rem;
    left: 0.5rem;
    cursor: pointer;
    color: var(--danger-color);
}

/* تخصيص الجدول */
.action-cell {
    display: flex;
    gap: 0.5rem;
}

.action-btn {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 1rem;
    color: var(--dark-color);
    transition: color 0.3s;
}

.action-btn.edit:hover {
    color: var(--secondary-color);
}

.action-btn.delete:hover {
    color: var(--danger-color);
}

.action-btn.view:hover {
    color: var(--primary-color);
}

/* تحميل البيانات */
.loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1002;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid var(--secondary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
        will-change: transform;
    }
    100% {
        transform: rotate(360deg);
        will-change: auto;
    }
}

/* نظام الإشعارات المحسن */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px 25px;
    border-radius: 12px;
    z-index: 10000;
    opacity: 0;
    transform: translateX(100%) scale(0.8);
    transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    min-width: 300px;
    max-width: 500px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 12px;
}

.toast.show {
    opacity: 1;
    transform: translateX(0) scale(1);
}

.toast.success {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
    border-left: 4px solid #28a745;
}

.toast.success::before {
    content: "✅";
    font-size: 18px;
}

.toast.error {
    background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
    border-left: 4px solid #dc3545;
}

.toast.error::before {
    content: "❌";
    font-size: 18px;
}

.toast.info {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-left: 4px solid #007bff;
}

.toast.info::before {
    content: "ℹ️";
    font-size: 18px;
}

.toast.warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    border-left: 4px solid #ffc107;
}

.toast.warning::before {
    content: "⚠️";
    font-size: 18px;
}

.notifications-container {
    position: fixed;
    top: 0;
    left: -400px;
    width: 350px;
    height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    box-shadow: 0 0 30px rgba(0, 0, 0, 0.3);
    z-index: 1003;
    transition: left 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
    display: flex;
    flex-direction: column;
    color: white;
}

.notifications-container.active {
    left: 0;
}

.notifications-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.1);
}

.notifications-header h3 {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
}

.notifications-list {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
}

.notification-item {
    padding: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    margin-bottom: 0.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.05);
}

.notification-item:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateX(5px);
}

.notification-item.unread {
    border-right: 3px solid #ffd700;
    background: rgba(255, 215, 0, 0.1);
}

.notification-title {
    font-weight: bold;
    margin-bottom: 0.5rem;
    font-size: 1rem;
}

.notification-message {
    opacity: 0.9;
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.notification-date {
    font-size: 0.8rem;
    opacity: 0.7;
    text-align: left;
}

.notification-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 0.5rem;
    gap: 0.5rem;
}

.notification-actions button {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    cursor: pointer;
    font-size: 0.8rem;
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    transition: all 0.3s ease;
}

.notification-actions button:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
}

.floating-btn {
    position: fixed;
    bottom: 2rem;
    left: 2rem;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    cursor: pointer;
    border: none;
    font-size: 1.5rem;
    z-index: 1002;
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.floating-btn:hover {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.4);
}

.badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 0.8rem;
    font-weight: bold;
    border: 2px solid white;
    animation: pulse 2s infinite;
}

.badge.hidden {
    display: none;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* تنسيق لوحة الإشعارات */
.notifications-footer {
    padding: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    gap: 0.5rem;
    justify-content: space-between;
}

.notifications-footer .btn {
    flex: 1;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 0.5rem;
    border-radius: 5px;
    font-size: 0.8rem;
    transition: all 0.3s ease;
}

.notifications-footer .btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

/* أيقونة التنبيهات العائمة */
.floating-notifications-btn {
    position: fixed;
    bottom: 30px;
    left: 30px;
    width: 70px;
    height: 70px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    cursor: pointer;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    z-index: 1000;
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    overflow: hidden;
    position: relative;
}

.floating-notifications-btn:hover {
    transform: scale(1.1) rotate(10deg);
    box-shadow: 0 12px 35px rgba(102, 126, 234, 0.6);
    background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
}

.floating-notifications-btn:active {
    transform: scale(0.95);
}

/* تأثير الموجة عند النقر */
.floating-btn-ripple {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.floating-notifications-btn.ripple .floating-btn-ripple {
    width: 140px;
    height: 140px;
}

/* شارة العدد للأيقونة العائمة */
.floating-badge {
    position: absolute;
    top: -5px;
    left: -5px;
    background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
    color: white;
    width: 28px;
    height: 28px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 12px;
    font-weight: bold;
    border: 3px solid white;
    animation: pulse-badge 2s infinite;
    box-shadow: 0 4px 12px rgba(255, 65, 108, 0.4);
}

.floating-badge.hidden {
    display: none;
}

/* تحريك الشارة */
@keyframes pulse-badge {
    0% {
        transform: scale(1);
        box-shadow: 0 4px 12px rgba(255, 65, 108, 0.4);
    }
    50% {
        transform: scale(1.2);
        box-shadow: 0 6px 20px rgba(255, 65, 108, 0.6);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 4px 12px rgba(255, 65, 108, 0.4);
    }
}

/* تحريك الأيقونة عند وجود تنبيهات */
.floating-notifications-btn.has-notifications {
    animation: shake-float 3s infinite;
}

@keyframes shake-float {
    0%, 100% { transform: translateY(0) rotate(0deg); }
    10% { transform: translateY(-3px) rotate(2deg); }
    20% { transform: translateY(3px) rotate(-2deg); }
    30% { transform: translateY(-2px) rotate(1deg); }
    40% { transform: translateY(2px) rotate(-1deg); }
    50% { transform: translateY(-1px) rotate(0deg); }
    60% { transform: translateY(1px) rotate(0deg); }
    70% { transform: translateY(0) rotate(0deg); }
}

/* تأثيرات للوضع المظلم */
.dark-mode .floating-notifications-btn {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    box-shadow: 0 8px 25px rgba(79, 172, 254, 0.4);
}

.dark-mode .floating-notifications-btn:hover {
    background: linear-gradient(135deg, #00f2fe 0%, #4facfe 100%);
    box-shadow: 0 12px 35px rgba(79, 172, 254, 0.6);
}

/* استجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .floating-notifications-btn {
        bottom: 20px;
        left: 20px;
        width: 60px;
        height: 60px;
        font-size: 20px;
    }

    .floating-badge {
        width: 24px;
        height: 24px;
        font-size: 10px;
        border: 2px solid white;
    }
}

/* ===============================
   تحسينات الهيدر والأيقونة
   =============================== */

/* تحسين الهيدر */
.header-left {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo-container {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 50%;
    padding: 5px;
    box-shadow: 0 4px 15px rgba(39, 174, 96, 0.2);
    border: 3px solid #27ae60;
    transition: all 0.3s ease;
}

.logo-container:hover {
    transform: scale(1.08) rotate(3deg);
    box-shadow: 0 6px 25px rgba(39, 174, 96, 0.4);
    border-color: #2ecc71;
}

.header-logo {
    width: 100%;
    height: 100%;
    object-fit: contain;
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
    transition: all 0.3s ease;
}

.logo-container:hover .header-logo {
    filter: drop-shadow(0 4px 8px rgba(39, 174, 96, 0.3));
}

/* تأثير نبضة للأيقونة محسن */
@keyframes logo-pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.02);
        opacity: 0.9;
    }
}

.logo-container {
    animation: logo-pulse 4s ease-in-out infinite;
    will-change: transform, opacity;
}

.header-text h1 {
    margin: 0;
    font-size: 24px;
    color: #2c3e50;
    font-weight: 700;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.header-subtitle {
    margin: 2px 0 0 0;
    font-size: 12px;
    color: #7f8c8d;
    font-weight: 400;
    font-style: italic;
}

/* ===============================
   نظام الوضع المظلم المحسن
   =============================== */

/* متغيرات CSS للألوان */
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #56ab2f;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --info-color: #3498db;

    /* ألوان الوضع الفاتح */
    --bg-color: #f8f9fa;
    --surface-color: #ffffff;
    --text-color: #2c3e50;
    --text-secondary: #7f8c8d;
    --border-color: #dee2e6;
    --shadow-color: rgba(0, 0, 0, 0.1);

    /* ألوان الهيدر */
    --header-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --header-text: #ffffff;

    /* ألوان الشريط الجانبي */
    --sidebar-bg: #2c3e50;
    --sidebar-text: #ecf0f1;
    --sidebar-hover: #34495e;
}

/* تم حذف التعريف المكرر للوضع المظلم - استخدم التعريف الموحد أعلاه */

/* تطبيق المتغيرات */
body {
    background-color: var(--bg-color);
    color: var(--text-color);
    transition: background-color 0.3s ease, color 0.3s ease;
}

.container {
    background-color: var(--bg-color);
}

header {
    background: var(--header-bg);
    color: var(--header-text);
    box-shadow: 0 2px 10px var(--shadow-color);
}

nav {
    background-color: var(--sidebar-bg);
}

nav ul li a {
    color: var(--sidebar-text);
    transition: all 0.3s ease;
}

nav ul li a:hover,
nav ul li a.active {
    background-color: var(--sidebar-hover);
    color: var(--header-text);
}

main {
    background-color: var(--bg-color);
}

.card {
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    box-shadow: 0 2px 8px var(--shadow-color);
    transition: all 0.3s ease;
}

.table-container {
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    box-shadow: 0 2px 8px var(--shadow-color);
}

table {
    background-color: var(--surface-color);
    color: var(--text-color);
}

table th {
    background-color: var(--primary-color);
    color: white;
}

table td {
    border-bottom: 1px solid var(--border-color);
}

table tr:hover {
    background-color: var(--border-color);
}

.btn {
    transition: all 0.3s ease;
}

.modal-content {
    background-color: var(--surface-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
    box-shadow: 0 4px 20px var(--shadow-color);
}

/* تحسين زر تبديل الوضع المظلم */
#dark-mode-toggle {
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
    border: none;
    color: white;
    transition: all 0.3s ease;
}

#dark-mode-toggle:hover {
    background: linear-gradient(135deg, #e67e22 0%, #d35400 100%);
    transform: scale(1.05);
}

.dark-mode #dark-mode-toggle {
    background: linear-gradient(135deg, #f1c40f 0%, #f39c12 100%);
}

.dark-mode #dark-mode-toggle:hover {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
}

/* تأثير انتقال سلس للوضع المظلم */
* {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* تحسين الأيقونة العائمة في الوضع المظلم */
.dark-mode .floating-notifications-btn {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    box-shadow: 0 8px 25px rgba(79, 172, 254, 0.4);
}

.dark-mode .floating-notifications-btn:hover {
    background: linear-gradient(135deg, #00f2fe 0%, #4facfe 100%);
    box-shadow: 0 12px 35px rgba(79, 172, 254, 0.6);
}

/* تحسين الهيدر في الوضع المظلم */
.dark-mode .header-text h1 {
    color: var(--header-text);
    text-shadow: 0 1px 3px rgba(0,0,0,0.3);
}

.dark-mode .header-subtitle {
    color: rgba(255, 255, 255, 0.8);
}

.dark-mode .logo-container {
    background: linear-gradient(135deg, #2d2d2d 0%, #404040 100%);
    box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
    border-color: #2ecc71;
}

.dark-mode .logo-container:hover {
    box-shadow: 0 6px 25px rgba(39, 174, 96, 0.5);
    border-color: #27ae60;
}

/* تمييز الصفوف */
.highlight-row {
    background-color: #fffacd !important;
    transition: background-color 0.5s;
}

/* رسالة فارغة */
.empty-notifications {
    padding: 2rem;
    text-align: center;
    color: #999;
}

/* علامات التبويب */
.dashboard-tabs {
    margin-top: 2rem;
}

.tab-buttons {
    display: flex;
    border-bottom: 1px solid #ddd;
    margin-bottom: 1.5rem;
}

.tab-btn {
    padding: 0.75rem 1.5rem;
    background: none;
    border: none;
    border-bottom: 3px solid transparent;
    cursor: pointer;
    font-size: 1rem;
    font-weight: bold;
    color: #666;
    transition: all 0.3s;
}

.tab-btn:hover {
    color: var(--secondary-color);
}

.tab-btn.active {
    color: var(--secondary-color);
    border-bottom-color: var(--secondary-color);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
    animation: fadeIn 0.5s;
}

/* إحصائيات */
.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
    gap: 1.5rem;
}

.stats-card {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 1.5rem;
}

.chart-container {
    height: 300px;
    margin-bottom: 1.5rem;
}

.stats-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.stat-label {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 0.5rem;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--primary-color);
}

/* أنماط إضافية للأقسام الجديدة */

/* شريط التصفية */
.filter-bar {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background-color: var(--card-bg-color);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.filter-group label {
    font-weight: bold;
    font-size: 0.9rem;
}

.filter-group select,
.filter-group input {
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: var(--card-bg-color);
    color: var(--text-color);
}

/* بطاقات الملخص */
.sales-summary,
.purchases-summary {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.summary-card {
    flex: 1;
    background-color: var(--card-bg-color);
    padding: 1rem;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    text-align: center;
}

.summary-card h4 {
    margin-bottom: 0.5rem;
    color: var(--primary-color);
    font-size: 0.9rem;
}

.summary-card span {
    font-size: 1.2rem;
    font-weight: bold;
    color: var(--secondary-color);
}

/* نوافذ الفواتير الكبيرة */
.modal-content.large {
    width: 90%;
    max-width: 1000px;
    max-height: 90vh;
    overflow-y: auto;
}

/* صفوف النماذج */
.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

/* قسم الأصناف في الفواتير */
.items-section {
    margin-bottom: 1.5rem;
}

.add-item-row {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
    padding: 1rem;
    background-color: var(--hover-bg-color);
    border-radius: var(--border-radius);
    align-items: end;
}

.add-item-row select,
.add-item-row input {
    flex: 1;
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: var(--card-bg-color);
    color: var(--text-color);
}

.add-item-row button {
    white-space: nowrap;
}

/* ملخص الفاتورة */
.sale-summary,
.purchase-summary,
.invoice-summary {
    background-color: var(--hover-bg-color);
    padding: 1rem;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    padding: 0.25rem 0;
}

.summary-row.total {
    border-top: 2px solid var(--border-color);
    padding-top: 0.5rem;
    margin-top: 0.5rem;
    font-weight: bold;
    font-size: 1.1rem;
}

/* تفاصيل الفاتورة */
.invoice-details-content {
    padding: 1rem;
}

.invoice-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--border-color);
}

.invoice-info h3 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.party-info h4 {
    color: var(--secondary-color);
    margin-bottom: 0.5rem;
}

.invoice-notes {
    margin-top: 1rem;
    padding: 1rem;
    background-color: var(--hover-bg-color);
    border-radius: var(--border-radius);
}

.invoice-notes h4 {
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

/* معلومات الصنف والدين */
.item-info,
.debt-info {
    background-color: var(--hover-bg-color);
    padding: 1rem;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
}

.item-info p,
.debt-info p {
    margin-bottom: 0.5rem;
}

/* حالات المخزون */
.status-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: bold;
    text-align: center;
}

.status-in-stock {
    background-color: #d4edda;
    color: #155724;
}

.status-low-stock {
    background-color: #fff3cd;
    color: #856404;
}

.status-out-of-stock {
    background-color: #f8d7da;
    color: #721c24;
}

.status-active {
    background-color: #d1ecf1;
    color: #0c5460;
}

.status-inactive {
    background-color: #f8d7da;
    color: #721c24;
}

/* أزرار إضافية */
.btn.warning {
    background-color: var(--warning-color);
    color: white;
}

.btn.warning:hover {
    background-color: #e67e22;
}

/* تحسينات الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .container {
        padding: 0.5rem;
    }

    header {
        padding: 0.5rem 1rem;
        flex-direction: column;
        gap: 1rem;
    }

    .header-left {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    .header-left h1 {
        font-size: 1.2rem;
        margin-top: 0.5rem;
    }

    nav ul {
        flex-direction: column;
        gap: 0.5rem;
    }

    nav ul li {
        margin: 0;
    }

    nav ul li a {
        justify-content: center;
        padding: 0.75rem;
    }

    .dashboard-cards {
        grid-template-columns: 1fr;
    }

    .dashboard-tables {
        grid-template-columns: 1fr;
    }

    .action-bar {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .action-buttons {
        justify-content: center;
        flex-wrap: wrap;
    }

    .search-container {
        justify-content: center;
    }

    .search-container input {
        width: 100%;
        max-width: 300px;
    }

    .table-container {
        padding: 1rem;
    }

    table {
        font-size: 0.9rem;
    }

    th, td {
        padding: 0.5rem;
    }

    .modal-content {
        width: 95%;
        margin: 2% auto;
        max-height: 95vh;
    }

    .modal-content.large {
        width: 98%;
        margin: 1% auto;
    }

    .form-buttons {
        flex-direction: column;
    }

    .form-buttons .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }

    .customer-info-header {
        flex-direction: column;
        text-align: center;
    }

    .customer-info-avatar {
        margin-bottom: 1rem;
    }

    .tab-buttons {
        flex-direction: column;
    }

    .tab-btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }

    .stats-container {
        grid-template-columns: 1fr;
    }

    .chart-container {
        height: 250px;
    }

    .floating-btn {
        bottom: 20px;
        left: 20px;
    }

    .notifications-container {
        width: 95%;
        left: 2.5%;
        right: 2.5%;
    }

    /* تحسينات للأقسام الجديدة */
    .filter-bar {
        flex-direction: column;
        gap: 0.5rem;
    }

    .sales-summary,
    .purchases-summary {
        flex-direction: column;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .add-item-row {
        flex-direction: column;
        align-items: stretch;
    }

    .invoice-header {
        flex-direction: column;
        gap: 1rem;
    }
}

/* أنماط السيارات والخزانات في نموذج الزبون */
.vehicle-item {
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    background-color: #f9f9f9;
    position: relative;
}

.vehicle-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e0e0e0;
}

.vehicle-header h4 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.1em;
}

.vehicle-header .remove-vehicle {
    background-color: #e74c3c;
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: background-color 0.3s;
}

.vehicle-header .remove-vehicle:hover {
    background-color: #c0392b;
}

.vehicle-section, .tank-section {
    margin-bottom: 15px;
    padding: 10px;
    background-color: white;
    border-radius: 6px;
    border: 1px solid #e8e8e8;
}

.vehicle-section h5, .tank-section h5 {
    margin: 0 0 10px 0;
    color: #34495e;
    font-size: 1em;
    display: flex;
    align-items: center;
    gap: 8px;
}

.vehicle-section h5 i {
    color: #3498db;
}

.tank-section h5 i {
    color: #e67e22;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
    margin-bottom: 10px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    margin-bottom: 5px;
    font-weight: 500;
    color: #555;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.form-group textarea {
    resize: vertical;
    min-height: 60px;
}





/* ===============================
   أنماط الأقسام المخصصة
   =============================== */

/* قسم البحث السريع الموحد */
.unified-search-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 20px;
    margin-bottom: 20px;
    border-radius: 12px;
    color: white;
}

.unified-search-section h3 {
    margin-bottom: 15px;
    color: white;
    display: flex;
    align-items: center;
}

.unified-search-section h3 i {
    margin-left: 10px;
}

.unified-search-grid {
    display: grid;
    grid-template-columns: 2fr 1fr auto;
    gap: 15px;
    align-items: end;
}

.search-input-container {
    position: relative;
}

.search-input-container label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: white;
}

.search-input-container input {
    width: 100%;
    padding: 12px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
}

.filter-select-container label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: white;
}

.filter-select-container select {
    width: 100%;
    padding: 12px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
}

/* اقتراحات البحث */
#customer-suggestions {
    position: absolute;
    background: white;
    border: 1px solid #ddd;
    border-radius: 6px;
    max-height: 300px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
    width: 100%;
    margin-top: 2px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

/* معلومات الزبون المختار */
.selected-customer-info {
    display: none;
    margin-top: 20px;
    padding: 15px;
    background: rgba(255,255,255,0.1);
    border-radius: 8px;
    border: 1px solid rgba(255,255,255,0.2);
}

.customer-info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 20px;
}

.customer-info-section h4 {
    color: white;
    margin-bottom: 10px;
}

.customer-info-section p {
    margin: 5px 0;
    color: #f0f0f0;
}

/* أزرار الإجراءات السريعة */
.quick-actions {
    margin-top: 15px;
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.quick-action-btn {
    padding: 8px 15px;
    border: none;
    border-radius: 4px;
    font-size: 13px;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.quick-action-btn.info {
    background: #17a2b8;
}

.quick-action-btn.success {
    background: #28a745;
}

.quick-action-btn.primary {
    background: #6f42c1;
}

.quick-action-btn.warning {
    background: #fd7e14;
}

.quick-action-btn.print {
    background: #28a745;
    padding: 12px 20px;
    font-weight: bold;
}

/* نماذج الزبائن */
.customer-form-section {
    margin-bottom: 30px;
}

.customer-form-section h3 {
    padding-bottom: 10px;
    margin-bottom: 20px;
    border-bottom: 2px solid;
}

.customer-form-section h3.personal-info {
    color: #007bff;
    border-bottom-color: #007bff;
}

.customer-form-section h3.vehicle-info {
    color: #28a745;
    border-bottom-color: #28a745;
    margin-top: 30px;
}

.customer-form-section h3.tank-info {
    color: #fd7e14;
    border-bottom-color: #fd7e14;
    margin-top: 30px;
}

/* تنسيق الحقول المطلوبة */
.required {
    color: #dc3545;
    font-weight: bold;
}

/* تحسين تنسيق النموذج */
.modal-content.large {
    max-width: 900px;
    width: 90%;
}

.form-group label {
    font-weight: 500;
    margin-bottom: 5px;
    display: block;
}

.form-group input,
.form-group select,
.form-group textarea {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    border-color: #007bff;
    outline: none;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.form-group input:required:invalid {
    border-color: #dc3545;
}

.form-group input:required:valid {
    border-color: #28a745;
}

.add-vehicle-btn {
    margin-bottom: 20px;
}



/* فلاتر التاريخ المخصصة */
.custom-date-range {
    display: none;
}

/* جدولة المراقبة */
.monitoring-schedule {
    display: none;
}

/* العناصر المخفية */
.hidden-element {
    display: none;
}

/* أنماط قسم الشهادات */
.certificates-tabs {
    margin-top: 20px;
}

.certificates-tabs .tab-buttons {
    display: flex;
    border-bottom: 2px solid #e0e0e0;
    margin-bottom: 20px;
}

.certificates-tabs .tab-btn {
    background: none;
    border: none;
    padding: 12px 24px;
    cursor: pointer;
    font-size: 14px;
    color: #666;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
    font-weight: 500;
}

.certificates-tabs .tab-btn:hover {
    color: #2196F3;
    background-color: rgba(33, 150, 243, 0.05);
}

.certificates-tabs .tab-btn.active {
    color: #2196F3;
    border-bottom-color: #2196F3;
    background-color: rgba(33, 150, 243, 0.1);
}

.certificates-tabs .tab-content {
    display: none;
}

.certificates-tabs .tab-content.active {
    display: block;
}

.reminders-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-top: 20px;
}

.reminder-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #4CAF50;
}

.reminder-card.urgent {
    border-left-color: #f44336;
}

.reminder-card.warning {
    border-left-color: #ff9800;
}

.reminder-card h4 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.reminder-card.urgent h4 {
    color: #f44336;
}

.reminder-card.warning h4 {
    color: #ff9800;
}

/* أنماط نافذة البحث عن الزبون */
.search-section {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    display: flex;
    gap: 15px;
    align-items: end;
}

.search-section .form-group {
    flex: 1;
    margin-bottom: 0;
}

.search-results {
    margin-top: 20px;
}

.search-results h3 {
    color: #333;
    margin-bottom: 15px;
    font-size: 18px;
}

/* أنماط نافذة طباعة الشهادة */
.certificate-type-selection {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.radio-group {
    display: flex;
    gap: 20px;
    margin-top: 10px;
}

.radio-label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    font-weight: 500;
    color: #333;
}

.radio-label input[type="radio"] {
    display: none;
}

.radio-custom {
    width: 20px;
    height: 20px;
    border: 2px solid #ddd;
    border-radius: 50%;
    position: relative;
    transition: all 0.3s ease;
}

.radio-label input[type="radio"]:checked + .radio-custom {
    border-color: #2196F3;
    background-color: #2196F3;
}

.radio-label input[type="radio"]:checked + .radio-custom::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 8px;
    height: 8px;
    background: white;
    border-radius: 50%;
}

.certificate-preview {
    background: white;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    padding: 30px;
    margin: 20px 0;
    min-height: 400px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* أنماط الشهادة المحدثة */
.certificate {
    max-width: 210mm;
    min-height: 297mm;
    margin: 0 auto;
    background: white;
    padding: 20mm;
    border: 2px solid #000;
    font-family: 'Arial', sans-serif;
    position: relative;
    box-sizing: border-box;
    page-break-inside: avoid;
}

/* رأس الشهادة */
.certificate-header {
    border-bottom: 3px solid #000;
    padding-bottom: 20px;
    margin-bottom: 25px;
}

.header-top {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
}

.company-info {
    flex: 1;
}

.company-name {
    font-size: 24px;
    font-weight: bold;
    color: #000;
    margin: 0 0 5px 0;
    text-align: right;
}

.company-name-fr {
    font-size: 20px;
    font-weight: bold;
    color: #000;
    margin: 0 0 10px 0;
    text-align: right;
}

.company-details {
    font-size: 14px;
    color: #333;
    margin: 0 0 3px 0;
    text-align: right;
}

.company-details-fr {
    font-size: 12px;
    color: #666;
    margin: 0 0 8px 0;
    text-align: right;
}

.company-contact {
    font-size: 11px;
    color: #333;
    margin: 0 0 3px 0;
    text-align: right;
}

.company-contact-fr {
    font-size: 10px;
    color: #666;
    margin: 0;
    text-align: right;
}

.certificate-number-box {
    border: 2px solid #000;
    padding: 15px;
    text-align: center;
    min-width: 120px;
}

.cert-number {
    font-size: 12px;
    font-weight: bold;
    margin-bottom: 3px;
}

.cert-number-fr {
    font-size: 10px;
    color: #666;
    margin-bottom: 8px;
}

.cert-number-value {
    font-size: 16px;
    font-weight: bold;
    color: #000;
}

.certificate-title-section {
    text-align: center;
    padding: 15px 0;
    background: #f8f9fa;
    border: 1px solid #ddd;
}

.certificate-main-title {
    font-size: 22px;
    font-weight: bold;
    color: #000;
    margin: 0 0 8px 0;
    text-transform: uppercase;
}

.certificate-main-title-fr {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    margin: 0;
    text-transform: uppercase;
}

/* جسم الشهادة */
.certificate-body {
    margin-bottom: 30px;
}

.certificate-intro {
    margin-bottom: 25px;
    padding: 15px;
    background: #f9f9f9;
    border-left: 4px solid #000;
}

.intro-text {
    font-size: 14px;
    line-height: 1.6;
    margin: 0 0 10px 0;
    text-align: justify;
}

.intro-text-fr {
    font-size: 12px;
    line-height: 1.5;
    margin: 0;
    color: #666;
    text-align: justify;
    font-style: italic;
}

/* جداول الشهادة */
.certificate-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    font-size: 12px;
}

.certificate-table th,
.certificate-table td {
    border: 1px solid #000;
    padding: 8px;
    text-align: right;
    vertical-align: top;
}

.certificate-table .table-header {
    background: #000;
    color: white;
    font-weight: bold;
    text-align: center;
    font-size: 14px;
    padding: 12px;
}

.certificate-table .label {
    background: #f0f0f0;
    font-weight: bold;
    width: 25%;
    font-size: 11px;
}

.certificate-table .label-fr {
    font-size: 9px;
    color: #666;
    font-weight: normal;
    font-style: italic;
}

.certificate-table .value {
    background: white;
    font-weight: 500;
    font-size: 12px;
}

.certificate-table .value-fr {
    font-size: 10px;
    color: #666;
    font-style: italic;
}

/* ملاحظات الشهادة */
.certificate-notes {
    margin: 25px 0;
    padding: 15px;
    border: 1px solid #ddd;
    background: #fafafa;
}

.certificate-notes h4 {
    margin: 0 0 10px 0;
    font-size: 14px;
    color: #000;
    border-bottom: 1px solid #ddd;
    padding-bottom: 5px;
}

.certificate-notes ul {
    margin: 0;
    padding-right: 20px;
}

.certificate-notes li {
    margin-bottom: 8px;
    font-size: 11px;
    line-height: 1.4;
}

/* تذييل الشهادة */
.certificate-footer {
    border-top: 2px solid #000;
    padding-top: 20px;
    margin-top: 30px;
}

.footer-section {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
}

.signature-area {
    display: flex;
    gap: 40px;
    flex: 1;
}

.signature-box {
    text-align: center;
    flex: 1;
}

.signature-line {
    border-top: 1px solid #000;
    width: 150px;
    margin: 40px auto 10px;
}

.signature-label {
    font-size: 11px;
    font-weight: bold;
    color: #000;
    margin-bottom: 3px;
}

.signature-label-fr {
    font-size: 9px;
    color: #666;
    font-style: italic;
}

.stamp-box {
    text-align: center;
    flex: 1;
}

.stamp-area {
    border: 2px dashed #666;
    width: 120px;
    height: 80px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: #f9f9f9;
}

.stamp-text {
    font-size: 11px;
    font-weight: bold;
    color: #333;
    margin-bottom: 3px;
}

.stamp-text-fr {
    font-size: 9px;
    color: #666;
    font-style: italic;
}

.issue-date {
    text-align: left;
    min-width: 150px;
}

.issue-date p {
    margin: 0 0 5px 0;
    font-size: 11px;
    color: #333;
}

/* أنماط الطباعة المحدثة */
@media print {
    body {
        margin: 0;
        padding: 0;
        background: white;
    }

    .certificate {
        max-width: none;
        width: 210mm;
        min-height: 297mm;
        margin: 0;
        padding: 15mm;
        border: 2px solid #000;
        box-shadow: none;
        page-break-inside: avoid;
        font-size: 12px;
    }

    .modal-content {
        box-shadow: none;
        border: none;
        background: transparent;
    }

    .form-buttons,
    .certificate-type-selection,
    .modal-content h2,
    .modal-content .close {
        display: none !important;
    }

    .certificate-preview {
        border: none;
        padding: 0;
        margin: 0;
        box-shadow: none;
        background: transparent;
    }

    /* تحسين الجداول للطباعة */
    .certificate-table {
        border-collapse: collapse;
        width: 100%;
    }

    .certificate-table th,
    .certificate-table td {
        border: 1px solid #000 !important;
        padding: 6px !important;
        font-size: 10px !important;
    }

    .certificate-table .table-header {
        background: #000 !important;
        color: white !important;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
    }

    .certificate-table .label {
        background: #f0f0f0 !important;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
    }

    /* تحسين الألوان للطباعة */
    .certificate-intro {
        background: #f9f9f9 !important;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
    }

    .certificate-notes {
        background: #fafafa !important;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
    }

    .certificate-title-section {
        background: #f8f9fa !important;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
    }

    .stamp-area {
        background: #f9f9f9 !important;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
    }

    /* تحسين الخطوط للطباعة */
    .company-name {
        font-size: 20px !important;
    }

    .company-name-fr {
        font-size: 16px !important;
    }

    .certificate-main-title {
        font-size: 18px !important;
    }

    .certificate-main-title-fr {
        font-size: 14px !important;
    }

    /* إخفاء عناصر غير ضرورية */
    .modal,
    .sidebar,
    .header,
    .floating-notifications-btn,
    .toast {
        display: none !important;
    }
}

/* ===============================
   أنماط نافذة المساعدة والتحسينات الجديدة
   =============================== */

/* أنماط نافذة المساعدة */
.help-shortcuts {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 15px;
}

.shortcut-group {
    background: var(--surface-color);
    padding: 15px;
    border-radius: 8px;
    border: 1px solid var(--border-color);
    box-shadow: 0 2px 8px var(--shadow-color);
}

.shortcut-group h4 {
    color: var(--primary-color);
    margin: 0 0 10px 0;
    font-size: 14px;
    font-weight: 600;
}

.shortcut-group p {
    margin: 8px 0;
    font-size: 13px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

kbd {
    background: var(--secondary-color);
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 11px;
    font-family: monospace;
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
    margin: 0 2px;
}

/* تحسينات للتوست المحسن */
.toast.enhanced {
    max-width: 500px;
    width: auto;
    padding: 20px;
    text-align: right;
}

.toast.enhanced h3 {
    margin: 0 0 15px 0;
    color: var(--primary-color);
    font-size: 16px;
}

/* تحسينات الأداء للرسوم المتحركة */
.card, .table-container, .modal-content {
    will-change: auto;
    transform: translateZ(0);
}

.card:hover, .table-container:hover {
    will-change: transform, box-shadow;
}

.card:not(:hover), .table-container:not(:hover) {
    will-change: auto;
}

/* تحسينات إمكانية الوصول */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* تحسين التركيز للوحة المفاتيح */
button:focus-visible,
input:focus-visible,
select:focus-visible,
textarea:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* تحسين مؤشر التحميل */
.loading-spinner {
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
    margin: 20px auto;
    will-change: transform;
}

/* تحسين الجداول للشاشات الصغيرة */
@media (max-width: 768px) {
    .table-container {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    table {
        min-width: 600px;
    }

    .help-shortcuts {
        grid-template-columns: 1fr;
    }

    .shortcut-group {
        margin-bottom: 15px;
    }

    kbd {
        font-size: 10px;
        padding: 1px 4px;
    }
}

/* تحسينات الوضع المظلم للعناصر الجديدة */
.dark-mode .shortcut-group {
    background: var(--surface-color);
    border-color: var(--border-color);
}

.dark-mode kbd {
    background: var(--primary-color);
    color: white;
}

.dark-mode .toast.enhanced {
    background: var(--surface-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
}

/* تحسينات الأداء العامة */
* {
    box-sizing: border-box;
}

img {
    max-width: 100%;
    height: auto;
}

/* تحسين الانتقالات */
.smooth-transition {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* تحسين الظلال */
.enhanced-shadow {
    box-shadow:
        0 1px 3px rgba(0, 0, 0, 0.12),
        0 1px 2px rgba(0, 0, 0, 0.24);
}

.enhanced-shadow:hover {
    box-shadow:
        0 3px 6px rgba(0, 0, 0, 0.16),
        0 3px 6px rgba(0, 0, 0, 0.23);
}

/* ===============================
   أنماط إعدادات تيليجرام
   =============================== */

.telegram-controls {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    margin: 15px 0;
}

.telegram-controls .btn {
    flex: 1;
    min-width: 150px;
    font-size: 13px;
    padding: 8px 12px;
}

.telegram-help {
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 15px;
    margin-top: 20px;
}

.telegram-help h4 {
    color: var(--primary-color);
    margin: 0 0 10px 0;
    font-size: 14px;
}

.telegram-help ol {
    margin: 0;
    padding-right: 20px;
}

.telegram-help li {
    margin-bottom: 8px;
    font-size: 13px;
    line-height: 1.4;
}

.form-group small {
    display: block;
    margin-top: 5px;
    font-size: 12px;
    color: var(--text-secondary);
    font-style: italic;
}

.form-group input[type="checkbox"] {
    margin-left: 8px;
    transform: scale(1.2);
}

.form-group label {
    display: flex;
    align-items: center;
    cursor: pointer;
}

/* تحسينات للوضع المظلم */
.dark-mode .telegram-help {
    background: var(--surface-color);
    border-color: var(--border-color);
}

.dark-mode .form-group small {
    color: var(--text-secondary);
}

/* استجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .telegram-controls {
        flex-direction: column;
    }

    .telegram-controls .btn {
        width: 100%;
        margin-bottom: 8px;
    }

    .telegram-help {
        padding: 12px;
    }

    .telegram-help ol {
        padding-right: 15px;
    }
}

/* ===============================
   أنماط التطبيق الويب
   =============================== */

/* مؤشر حالة الاتصال بالخادم */
.connection-status {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
    z-index: 1000;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.connection-status.connected {
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
    border: 2px solid #4CAF50;
}

.connection-status.disconnected {
    background: linear-gradient(135deg, #ff9800, #f57c00);
    color: white;
    border: 2px solid #ff9800;
}

.connection-status i {
    margin-left: 5px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* تحسينات للتطبيق الويب */
.web-app-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px 20px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.web-app-header h1 {
    margin: 0;
    font-size: 24px;
    font-weight: bold;
}

.web-app-header .version {
    font-size: 12px;
    opacity: 0.8;
    margin-top: 5px;
}

/* تحسينات للأزرار في التطبيق الويب */
.web-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.web-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.web-btn:active {
    transform: translateY(0);
}

/* مؤشر التحميل للعمليات غير المتزامنة */
.loading-spinner.web {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 10px;
}

/* تحسينات للإشعارات في التطبيق الويب */
.web-notification {
    position: fixed;
    top: 80px;
    right: 20px;
    background: white;
    border-radius: 10px;
    padding: 15px 20px;
    box-shadow: 0 5px 25px rgba(0,0,0,0.15);
    z-index: 1001;
    transform: translateX(400px);
    transition: transform 0.3s ease;
    border-left: 4px solid #4CAF50;
}

.web-notification.show {
    transform: translateX(0);
}

.web-notification.error {
    border-left-color: #f44336;
}

.web-notification.warning {
    border-left-color: #ff9800;
}

.web-notification.info {
    border-left-color: #2196F3;
}

/* تحسينات للوضع المظلم في التطبيق الويب */
.dark-mode .connection-status.connected {
    background: linear-gradient(135deg, #2E7D32, #1B5E20);
    border-color: #2E7D32;
}

.dark-mode .connection-status.disconnected {
    background: linear-gradient(135deg, #E65100, #BF360C);
    border-color: #E65100;
}

.dark-mode .web-notification {
    background: var(--surface-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
}

/* تحسينات استجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .connection-status {
        top: 10px;
        right: 10px;
        font-size: 11px;
        padding: 6px 12px;
    }

    .web-notification {
        top: 60px;
        right: 10px;
        left: 10px;
        transform: translateY(-100px);
    }

    .web-notification.show {
        transform: translateY(0);
    }
}
