<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار اتصال قاعدة البيانات</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            color: white;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2rem;
        }
        
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #3498db;
        }
        
        .test-result {
            padding: 10px;
            border-radius: 8px;
            margin: 10px 0;
            font-family: monospace;
        }
        
        .success { background: rgba(39, 174, 96, 0.3); border-left: 4px solid #27ae60; }
        .error { background: rgba(231, 76, 60, 0.3); border-left: 4px solid #e74c3c; }
        .info { background: rgba(52, 152, 219, 0.3); border-left: 4px solid #3498db; }
        .warning { background: rgba(243, 156, 18, 0.3); border-left: 4px solid #f39c12; }
        
        button {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        
        .data-display {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 0.9rem;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 اختبار اتصال قاعدة البيانات</h1>
        
        <div class="test-section">
            <h3>📊 اختبارات النظام</h3>
            <button onclick="testDatabaseConnection()">اختبار الاتصال</button>
            <button onclick="testLicenseSystem()">اختبار نظام التراخيص</button>
            <button onclick="testSessionSystem()">اختبار نظام الجلسات</button>
            <button onclick="testStatistics()">اختبار الإحصائيات</button>
            <button onclick="clearAllData()">مسح جميع البيانات</button>
            <div id="testResults"></div>
        </div>
        
        <div class="test-section">
            <h3>🔑 التراخيص المتاحة</h3>
            <button onclick="showLicenses()">عرض التراخيص</button>
            <button onclick="addTestLicense()">إضافة ترخيص تجريبي</button>
            <div id="licensesDisplay"></div>
        </div>
        
        <div class="test-section">
            <h3>💻 الجلسات النشطة</h3>
            <button onclick="showSessions()">عرض الجلسات</button>
            <button onclick="createTestSession()">إنشاء جلسة تجريبية</button>
            <div id="sessionsDisplay"></div>
        </div>
        
        <div class="test-section">
            <h3>📱 الأجهزة المسجلة</h3>
            <button onclick="showDevices()">عرض الأجهزة</button>
            <button onclick="registerTestDevice()">تسجيل جهاز تجريبي</button>
            <div id="devicesDisplay"></div>
        </div>
        
        <div class="test-section">
            <h3>📈 إحصائيات النظام</h3>
            <button onclick="showStatistics()">عرض الإحصائيات</button>
            <button onclick="refreshStatistics()">تحديث الإحصائيات</button>
            <div id="statisticsDisplay"></div>
        </div>
        
        <div class="test-section">
            <h3>🔧 أدوات المطور</h3>
            <button onclick="openAdminPanel()">فتح لوحة التحكم</button>
            <button onclick="exportData()">تصدير البيانات</button>
            <button onclick="importTestData()">استيراد بيانات تجريبية</button>
        </div>
    </div>

    <!-- تحميل نظام قاعدة البيانات -->
    <script src="app/database-sync.js"></script>
    
    <script>
        // اختبار اتصال قاعدة البيانات
        function testDatabaseConnection() {
            const results = document.getElementById('testResults');
            results.innerHTML = '';
            
            addResult('بدء اختبار اتصال قاعدة البيانات...', 'info');
            
            // اختبار وجود النظام
            if (typeof window.DB !== 'undefined') {
                addResult('✅ نظام قاعدة البيانات متاح', 'success');
            } else {
                addResult('❌ نظام قاعدة البيانات غير متاح', 'error');
                return;
            }
            
            // اختبار الوظائف الأساسية
            try {
                const licenses = window.DB.getLicenses();
                addResult(`✅ تم تحميل ${licenses.length} ترخيص`, 'success');
                
                const sessions = window.DB.getSessions();
                addResult(`✅ تم تحميل ${sessions.length} جلسة`, 'success');
                
                const devices = window.DB.getDevices();
                addResult(`✅ تم تحميل ${devices.length} جهاز`, 'success');
                
                const stats = window.DB.getStatistics();
                addResult(`✅ تم تحميل الإحصائيات - آخر تحديث: ${new Date(stats.lastUpdate).toLocaleString('ar-SA')}`, 'success');
                
                addResult('🎉 جميع الاختبارات نجحت!', 'success');
                
            } catch (error) {
                addResult(`❌ خطأ في الاختبار: ${error.message}`, 'error');
            }
        }
        
        // اختبار نظام التراخيص
        function testLicenseSystem() {
            const results = document.getElementById('testResults');
            results.innerHTML = '';
            
            try {
                const licenses = window.DB.getLicenses();
                addResult(`📊 إجمالي التراخيص: ${licenses.length}`, 'info');
                
                const activeLicenses = licenses.filter(l => l.isActive);
                addResult(`✅ التراخيص النشطة: ${activeLicenses.length}`, 'success');
                
                const expiredLicenses = licenses.filter(l => new Date(l.expiresAt) < new Date());
                addResult(`⚠️ التراخيص المنتهية: ${expiredLicenses.length}`, expiredLicenses.length > 0 ? 'warning' : 'info');
                
                const boundLicenses = licenses.filter(l => l.deviceId);
                addResult(`🔗 التراخيص المرتبطة بأجهزة: ${boundLicenses.length}`, 'info');
                
            } catch (error) {
                addResult(`❌ خطأ في اختبار التراخيص: ${error.message}`, 'error');
            }
        }
        
        // اختبار نظام الجلسات
        function testSessionSystem() {
            const results = document.getElementById('testResults');
            results.innerHTML = '';
            
            try {
                const sessions = window.DB.getSessions();
                addResult(`📊 إجمالي الجلسات: ${sessions.length}`, 'info');
                
                const activeSessions = sessions.filter(s => s.isActive);
                addResult(`✅ الجلسات النشطة: ${activeSessions.length}`, 'success');
                
                if (activeSessions.length > 0) {
                    const latestSession = activeSessions[activeSessions.length - 1];
                    addResult(`🕐 آخر جلسة: ${new Date(latestSession.startTime).toLocaleString('ar-SA')}`, 'info');
                }
                
            } catch (error) {
                addResult(`❌ خطأ في اختبار الجلسات: ${error.message}`, 'error');
            }
        }
        
        // اختبار الإحصائيات
        function testStatistics() {
            const results = document.getElementById('testResults');
            results.innerHTML = '';
            
            try {
                const stats = window.DB.getStatistics();
                
                addResult(`📊 إحصائيات النظام:`, 'info');
                addResult(`   • إجمالي التراخيص: ${stats.totalLicenses}`, 'info');
                addResult(`   • التراخيص النشطة: ${stats.activeLicenses}`, 'info');
                addResult(`   • الأجهزة المسجلة: ${stats.totalDevices}`, 'info');
                addResult(`   • الجلسات النشطة: ${stats.activeSessions}`, 'info');
                addResult(`   • آخر تحديث: ${new Date(stats.lastUpdate).toLocaleString('ar-SA')}`, 'info');
                
                if (stats.recentActivity && stats.recentActivity.length > 0) {
                    addResult(`📝 آخر نشاط: ${stats.recentActivity[0].action}`, 'info');
                }
                
            } catch (error) {
                addResult(`❌ خطأ في اختبار الإحصائيات: ${error.message}`, 'error');
            }
        }
        
        // عرض التراخيص
        function showLicenses() {
            const display = document.getElementById('licensesDisplay');
            try {
                const licenses = window.DB.getLicenses();
                display.innerHTML = `<div class="data-display">${JSON.stringify(licenses, null, 2)}</div>`;
            } catch (error) {
                display.innerHTML = `<div class="test-result error">خطأ: ${error.message}</div>`;
            }
        }
        
        // عرض الجلسات
        function showSessions() {
            const display = document.getElementById('sessionsDisplay');
            try {
                const sessions = window.DB.getSessions();
                display.innerHTML = `<div class="data-display">${JSON.stringify(sessions, null, 2)}</div>`;
            } catch (error) {
                display.innerHTML = `<div class="test-result error">خطأ: ${error.message}</div>`;
            }
        }
        
        // عرض الأجهزة
        function showDevices() {
            const display = document.getElementById('devicesDisplay');
            try {
                const devices = window.DB.getDevices();
                display.innerHTML = `<div class="data-display">${JSON.stringify(devices, null, 2)}</div>`;
            } catch (error) {
                display.innerHTML = `<div class="test-result error">خطأ: ${error.message}</div>`;
            }
        }
        
        // عرض الإحصائيات
        function showStatistics() {
            const display = document.getElementById('statisticsDisplay');
            try {
                const stats = window.DB.getStatistics();
                display.innerHTML = `<div class="data-display">${JSON.stringify(stats, null, 2)}</div>`;
            } catch (error) {
                display.innerHTML = `<div class="test-result error">خطأ: ${error.message}</div>`;
            }
        }
        
        // إضافة ترخيص تجريبي
        function addTestLicense() {
            try {
                const testCode = 'TEST-' + Date.now().toString(36).toUpperCase();
                const licenses = window.DB.getLicenses();
                
                const newLicense = {
                    id: 'LIC-TEST-' + Date.now(),
                    code: testCode,
                    type: 'demo',
                    status: 'active',
                    expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
                    isActive: true,
                    deviceId: null,
                    activatedAt: null,
                    createdAt: new Date().toISOString(),
                    notes: 'ترخيص تجريبي للاختبار',
                    maxDevices: 1,
                    features: ['basic'],
                    createdBy: 'test-system'
                };
                
                licenses.push(newLicense);
                localStorage.setItem('validLicenses', JSON.stringify(licenses));
                
                addResult(`✅ تم إضافة ترخيص تجريبي: ${testCode}`, 'success');
                
            } catch (error) {
                addResult(`❌ خطأ في إضافة الترخيص: ${error.message}`, 'error');
            }
        }
        
        // إنشاء جلسة تجريبية
        function createTestSession() {
            try {
                const testSessionData = {
                    licenseCode: 'DEMO-2024-TEST-0001',
                    deviceId: 'DEV-TEST-' + Date.now().toString(36).toUpperCase(),
                    loginTime: new Date().toISOString(),
                    sessionId: 'SES-TEST-' + Date.now(),
                    userAgent: navigator.userAgent,
                    ipAddress: 'test-local'
                };
                
                const success = window.DB.registerSession(testSessionData, { type: 'demo' });
                
                if (success) {
                    addResult(`✅ تم إنشاء جلسة تجريبية: ${testSessionData.sessionId}`, 'success');
                } else {
                    addResult(`❌ فشل في إنشاء الجلسة التجريبية`, 'error');
                }
                
            } catch (error) {
                addResult(`❌ خطأ في إنشاء الجلسة: ${error.message}`, 'error');
            }
        }
        
        // تسجيل جهاز تجريبي
        function registerTestDevice() {
            try {
                const testDeviceId = 'DEV-TEST-' + Date.now().toString(36).toUpperCase();
                window.DB.sync.registerDevice(testDeviceId, 'DEMO-2024-TEST-0001', navigator.userAgent);
                
                addResult(`✅ تم تسجيل جهاز تجريبي: ${testDeviceId}`, 'success');
                
            } catch (error) {
                addResult(`❌ خطأ في تسجيل الجهاز: ${error.message}`, 'error');
            }
        }
        
        // تحديث الإحصائيات
        function refreshStatistics() {
            try {
                const stats = window.DB.sync.updateStatistics();
                addResult(`✅ تم تحديث الإحصائيات`, 'success');
                showStatistics();
            } catch (error) {
                addResult(`❌ خطأ في تحديث الإحصائيات: ${error.message}`, 'error');
            }
        }
        
        // فتح لوحة التحكم
        function openAdminPanel() {
            try {
                window.open('admin-control-panel.html', '_blank', 'width=1200,height=800');
                addResult(`✅ تم فتح لوحة التحكم الإدارية`, 'success');
            } catch (error) {
                addResult(`❌ خطأ في فتح لوحة التحكم: ${error.message}`, 'error');
            }
        }
        
        // تصدير البيانات
        function exportData() {
            try {
                const data = {
                    licenses: window.DB.getLicenses(),
                    sessions: window.DB.getSessions(),
                    devices: window.DB.getDevices(),
                    statistics: window.DB.getStatistics(),
                    exportDate: new Date().toISOString()
                };
                
                const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `futurefuel-database-${Date.now()}.json`;
                a.click();
                
                addResult(`✅ تم تصدير البيانات`, 'success');
                
            } catch (error) {
                addResult(`❌ خطأ في تصدير البيانات: ${error.message}`, 'error');
            }
        }
        
        // مسح جميع البيانات
        function clearAllData() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات؟')) {
                try {
                    localStorage.removeItem('validLicenses');
                    localStorage.removeItem('activeSessions');
                    localStorage.removeItem('registeredDevices');
                    localStorage.removeItem('systemStatistics');
                    localStorage.removeItem('systemLogs');
                    
                    // إعادة تهيئة البيانات الافتراضية
                    window.DB.sync.initializeDefaultData();
                    
                    addResult(`✅ تم مسح جميع البيانات وإعادة التهيئة`, 'success');
                    
                } catch (error) {
                    addResult(`❌ خطأ في مسح البيانات: ${error.message}`, 'error');
                }
            }
        }
        
        // استيراد بيانات تجريبية
        function importTestData() {
            try {
                // إنشاء بيانات تجريبية شاملة
                const testData = {
                    licenses: [
                        {
                            id: 'LIC-DEMO-001',
                            code: 'DEMO-2024-TEST-0001',
                            type: 'demo',
                            status: 'active',
                            expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
                            isActive: true,
                            deviceId: 'DEV-DEMO-001',
                            activatedAt: new Date().toISOString(),
                            createdAt: new Date().toISOString(),
                            notes: 'ترخيص تجريبي للاختبار',
                            maxDevices: 1,
                            features: ['basic', 'reports']
                        }
                    ]
                };
                
                localStorage.setItem('validLicenses', JSON.stringify(testData.licenses));
                window.DB.sync.updateStatistics();
                
                addResult(`✅ تم استيراد البيانات التجريبية`, 'success');
                
            } catch (error) {
                addResult(`❌ خطأ في استيراد البيانات: ${error.message}`, 'error');
            }
        }
        
        // إضافة نتيجة اختبار
        function addResult(message, type) {
            const results = document.getElementById('testResults');
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.textContent = message;
            results.appendChild(div);
        }
        
        // تشغيل اختبار أولي عند تحميل الصفحة
        window.addEventListener('load', function() {
            setTimeout(() => {
                addResult('🚀 نظام اختبار قاعدة البيانات جاهز', 'info');
                testDatabaseConnection();
            }, 1000);
        });
    </script>
</body>
</html>
