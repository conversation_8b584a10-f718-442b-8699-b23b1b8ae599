<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تكامل جدول الإرسال</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', sans-serif;
            background-color: #f1f5f9;
            margin: 0;
            padding: 20px;
            direction: rtl;
            color: #334155;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .test-section {
            padding: 2rem;
            border-bottom: 1px solid #e2e8f0;
        }

        .test-section h2 {
            color: #1e3a8a;
            margin-bottom: 1rem;
        }

        .btn {
            padding: 0.75rem 1.25rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            font-size: 0.9rem;
            margin: 0.5rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
            color: white;
        }

        .btn-success {
            background: #10b981;
            color: white;
        }

        .btn-warning {
            background: #f59e0b;
            color: white;
        }

        .btn-danger {
            background: #ef4444;
            color: white;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            font-size: 1rem;
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .test-results {
            background: #f8fafc;
            padding: 1rem;
            border-radius: 8px;
            margin-top: 1rem;
        }

        .success {
            color: #10b981;
        }

        .error {
            color: #ef4444;
        }

        .info {
            color: #3b82f6;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 اختبار تكامل جدول الإرسال</h1>
            <p>اختبار النظام المتكامل لإدارة العملاء وجدول الإرسال</p>
        </div>

        <!-- اختبار إضافة عميل جديد -->
        <div class="test-section">
            <h2>1️⃣ اختبار إضافة عميل جديد</h2>
            <p>اختبار إضافة عميل جديد مع خدمات مختلفة (تركيب، مراقبة، تجديد بطاقة)</p>
            
            <form id="test-customer-form">
                <div class="form-row">
                    <div class="form-group">
                        <label>نوع الخدمة:</label>
                        <select id="test-service-type" required>
                            <option value="">اختر نوع الخدمة</option>
                            <option value="تركيب">تركيب خزان غاز</option>
                            <option value="مراقبة">مراقبة دورية</option>
                            <option value="تجديد بطاقة">تجديد بطاقة الغاز</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>تاريخ الخدمة:</label>
                        <input type="date" id="test-service-date" required>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label>اسم العميل:</label>
                        <input type="text" id="test-customer-name" required placeholder="مثال: أحمد محمد علي">
                    </div>
                    <div class="form-group">
                        <label>رقم الهاتف:</label>
                        <input type="tel" id="test-customer-phone" required placeholder="مثال: 0555123456">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label>رقم التسجيل:</label>
                        <input type="text" id="test-vehicle-plate" required placeholder="مثال: 1506615-26">
                    </div>
                    <div class="form-group">
                        <label>نوع السيارة:</label>
                        <input type="text" id="test-vehicle-type" required placeholder="مثال: Toyota Corolla">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label>رقم خزان الغاز:</label>
                        <input type="text" id="test-tank-number" placeholder="مثال: 1506623">
                    </div>
                    <div class="form-group">
                        <label>الرقم التسلسلي:</label>
                        <input type="text" id="test-serial-number" placeholder="مثال: VF32A5FWC12345678">
                    </div>
                </div>

                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-plus"></i> إضافة العميل واختبار النظام
                </button>
            </form>

            <div id="test-results-1" class="test-results" style="display: none;">
                <h3>نتائج الاختبار:</h3>
                <div id="test-output-1"></div>
            </div>
        </div>

        <!-- اختبار عرض البيانات -->
        <div class="test-section">
            <h2>2️⃣ اختبار عرض البيانات</h2>
            <p>عرض البيانات المحفوظة في جدول الإرسال وبطاقات الغاز</p>
            
            <button type="button" class="btn btn-success" onclick="testDataDisplay()">
                <i class="fas fa-eye"></i> عرض البيانات المحفوظة
            </button>
            
            <button type="button" class="btn btn-warning" onclick="testTransmissionTable()">
                <i class="fas fa-table"></i> عرض جدول الإرسال
            </button>
            
            <button type="button" class="btn btn-info" onclick="testGasCards()">
                <i class="fas fa-id-card"></i> عرض بطاقات الغاز
            </button>

            <div id="test-results-2" class="test-results" style="display: none;">
                <h3>البيانات المحفوظة:</h3>
                <div id="test-output-2"></div>
            </div>
        </div>

        <!-- اختبار التصدير -->
        <div class="test-section">
            <h2>3️⃣ اختبار التصدير والطباعة</h2>
            <p>اختبار تصدير جدول الإرسال إلى PDF وطباعة البيانات</p>

            <button type="button" class="btn btn-primary" onclick="testPDFExport()">
                <i class="fas fa-file-pdf"></i> تصدير جدول الإرسال PDF
            </button>

            <button type="button" class="btn btn-success" onclick="testPrint()">
                <i class="fas fa-print"></i> طباعة جدول الإرسال
            </button>

            <div id="test-results-3" class="test-results" style="display: none;">
                <h3>نتائج التصدير:</h3>
                <div id="test-output-3"></div>
            </div>
        </div>

        <!-- اختبار التنبيهات -->
        <div class="test-section">
            <h2>4️⃣ اختبار نظام التنبيهات</h2>
            <p>اختبار تنبيهات إرسال الجدول للمديرية كل 6 أشهر وسنوياً</p>

            <button type="button" class="btn btn-warning" onclick="testTransmissionReminders()">
                <i class="fas fa-bell"></i> اختبار تنبيهات الإرسال
            </button>

            <button type="button" class="btn btn-success" onclick="testMarkSent()">
                <i class="fas fa-check"></i> تسجيل إرسال الجدول
            </button>

            <button type="button" class="btn btn-info" onclick="testCompanySettings()">
                <i class="fas fa-cog"></i> اختبار إعدادات المؤسسة
            </button>

            <div id="test-results-4" class="test-results" style="display: none;">
                <h3>نتائج التنبيهات:</h3>
                <div id="test-output-4"></div>
            </div>
        </div>

        <!-- مسح البيانات -->
        <div class="test-section">
            <h2>5️⃣ إدارة البيانات</h2>
            <p>مسح البيانات التجريبية وإعادة تعيين النظام</p>

            <button type="button" class="btn btn-danger" onclick="clearTestData()">
                <i class="fas fa-trash"></i> مسح البيانات التجريبية
            </button>

            <button type="button" class="btn btn-warning" onclick="generateSampleData()">
                <i class="fas fa-database"></i> إنشاء بيانات تجريبية
            </button>

            <div id="test-results-5" class="test-results" style="display: none;">
                <h3>نتائج إدارة البيانات:</h3>
                <div id="test-output-5"></div>
            </div>
        </div>
    </div>

    <script>
        // تعيين تاريخ اليوم كافتراضي
        document.getElementById('test-service-date').value = new Date().toISOString().split('T')[0];
        
        // محاكاة بيانات التطبيق
        let testAppData = {
            customers: [],
            vehicles: [],
            gasCards: [],
            transmissionTable: [],
            settings: {
                company: {
                    name: 'مركز وقود المستقبل - عزيري عبد الله اسحاق',
                    number: '463/2019',
                    directorateName: 'مدير الصناعة و المناجم لولاية المدية',
                    transmissionFrequency: 'both',
                    nextTransmissionDate: '',
                    transmissionReminderDays: 7,
                    lastTransmissionDate: '',
                    transmissionHistory: []
                }
            }
        };

        // تحميل البيانات من localStorage إذا كانت موجودة
        function loadTestData() {
            const savedData = localStorage.getItem('testAppData');
            if (savedData) {
                testAppData = JSON.parse(savedData);
            }
        }

        // حفظ البيانات في localStorage
        function saveTestData() {
            localStorage.setItem('testAppData', JSON.stringify(testAppData));
        }

        // إنشاء معرف فريد
        function generateId() {
            return Date.now().toString(36) + Math.random().toString(36).substr(2);
        }

        // معالجة نموذج إضافة العميل
        document.getElementById('test-customer-form').addEventListener('submit', function(e) {
            e.preventDefault();

            const serviceType = document.getElementById('test-service-type').value;
            const serviceDate = document.getElementById('test-service-date').value;
            const customerName = document.getElementById('test-customer-name').value;
            const customerPhone = document.getElementById('test-customer-phone').value;
            const vehiclePlate = document.getElementById('test-vehicle-plate').value;
            const vehicleType = document.getElementById('test-vehicle-type').value;
            const tankNumber = document.getElementById('test-tank-number').value;
            const serialNumber = document.getElementById('test-serial-number').value;

            if (!serviceType || !serviceDate || !customerName || !customerPhone || !vehiclePlate || !vehicleType) {
                showTestResult(1, 'error', 'يرجى ملء جميع الحقول المطلوبة');
                return;
            }

            // إنشاء العميل
            const customerId = generateId();
            const vehicleId = generateId();

            const customer = {
                id: customerId,
                name: customerName,
                phone: customerPhone,
                createdAt: new Date().toISOString()
            };

            const vehicle = {
                id: vehicleId,
                plateNumber: vehiclePlate,
                brand: vehicleType.split(' ')[0] || 'غير محدد',
                model: vehicleType.split(' ').slice(1).join(' ') || 'غير محدد',
                customerId: customerId,
                createdAt: new Date().toISOString()
            };

            testAppData.customers.push(customer);
            testAppData.vehicles.push(vehicle);

            let resultMessage = `✅ تم إضافة العميل: ${customerName}<br>`;
            resultMessage += `🚗 تم إضافة السيارة: ${vehiclePlate}<br>`;

            // معالجة نوع الخدمة
            if (serviceType === 'تركيب' || serviceType === 'مراقبة') {
                // إضافة إلى جدول الإرسال
                const transmissionEntry = {
                    id: generateId(),
                    type: serviceType,
                    tankNumber: tankNumber || 'غير محدد',
                    carType: vehicleType,
                    serialNumber: serialNumber || tankNumber || 'غير محدد',
                    registrationNumber: vehiclePlate,
                    ownerName: customerName,
                    operationDate: serviceDate,
                    customerId: customerId,
                    vehicleId: vehicleId,
                    createdAt: new Date().toISOString()
                };

                testAppData.transmissionTable.push(transmissionEntry);
                resultMessage += `📋 تم إضافة عملية ${serviceType} إلى جدول الإرسال<br>`;

            } else if (serviceType === 'تجديد بطاقة') {
                // إضافة بطاقة غاز
                const gasCard = {
                    id: generateId(),
                    customerId: customerId,
                    vehicleId: vehicleId,
                    cardNumber: generateCardNumber(),
                    issueDate: serviceDate,
                    expiryDate: new Date(new Date(serviceDate).setFullYear(new Date(serviceDate).getFullYear() + 1)).toISOString().split('T')[0],
                    status: 'active',
                    notes: `بطاقة جديدة - ${serviceType}`,
                    createdAt: new Date().toISOString()
                };

                testAppData.gasCards.push(gasCard);
                resultMessage += `🎫 تم إصدار بطاقة غاز جديدة رقم: ${gasCard.cardNumber}<br>`;
            }

            saveTestData();
            showTestResult(1, 'success', resultMessage);

            // مسح النموذج
            document.getElementById('test-customer-form').reset();
            document.getElementById('test-service-date').value = new Date().toISOString().split('T')[0];
        });

        // إنشاء رقم بطاقة
        function generateCardNumber() {
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
            return `${year}${month}${day}${random}`;
        }

        // عرض نتيجة الاختبار
        function showTestResult(testNumber, type, message) {
            const resultsDiv = document.getElementById(`test-results-${testNumber}`);
            const outputDiv = document.getElementById(`test-output-${testNumber}`);

            resultsDiv.style.display = 'block';
            outputDiv.innerHTML = `<div class="${type}">${message}</div>`;
        }

        // اختبار عرض البيانات
        function testDataDisplay() {
            let output = '<h4>📊 إحصائيات البيانات:</h4>';
            output += `<p><strong>العملاء:</strong> ${testAppData.customers.length}</p>`;
            output += `<p><strong>السيارات:</strong> ${testAppData.vehicles.length}</p>`;
            output += `<p><strong>جدول الإرسال:</strong> ${testAppData.transmissionTable.length}</p>`;
            output += `<p><strong>بطاقات الغاز:</strong> ${testAppData.gasCards.length}</p>`;

            if (testAppData.customers.length > 0) {
                output += '<h4>👥 العملاء:</h4><ul>';
                testAppData.customers.forEach(customer => {
                    output += `<li>${customer.name} - ${customer.phone}</li>`;
                });
                output += '</ul>';
            }

            showTestResult(2, 'info', output);
        }

        // اختبار جدول الإرسال
        function testTransmissionTable() {
            let output = '<h4>📋 جدول الإرسال:</h4>';

            if (testAppData.transmissionTable.length === 0) {
                output += '<p class="error">لا توجد عمليات في جدول الإرسال</p>';
            } else {
                output += '<table border="1" style="width:100%; border-collapse: collapse;">';
                output += '<tr><th>النوع</th><th>رقم الخزان</th><th>الصنف</th><th>رقم التسجيل</th><th>الاسم</th><th>التاريخ</th></tr>';

                testAppData.transmissionTable.forEach(entry => {
                    output += `<tr>
                        <td>${entry.type}</td>
                        <td>${entry.tankNumber}</td>
                        <td>${entry.carType}</td>
                        <td>${entry.registrationNumber}</td>
                        <td>${entry.ownerName}</td>
                        <td>${entry.operationDate}</td>
                    </tr>`;
                });

                output += '</table>';
            }

            showTestResult(2, 'info', output);
        }

        // اختبار بطاقات الغاز
        function testGasCards() {
            let output = '<h4>🎫 بطاقات الغاز:</h4>';

            if (testAppData.gasCards.length === 0) {
                output += '<p class="error">لا توجد بطاقات غاز</p>';
            } else {
                output += '<table border="1" style="width:100%; border-collapse: collapse;">';
                output += '<tr><th>رقم البطاقة</th><th>العميل</th><th>رقم السيارة</th><th>تاريخ الإصدار</th><th>تاريخ الانتهاء</th></tr>';

                testAppData.gasCards.forEach(card => {
                    const customer = testAppData.customers.find(c => c.id === card.customerId);
                    const vehicle = testAppData.vehicles.find(v => v.id === card.vehicleId);

                    output += `<tr>
                        <td>${card.cardNumber}</td>
                        <td>${customer ? customer.name : 'غير موجود'}</td>
                        <td>${vehicle ? vehicle.plateNumber : 'غير موجود'}</td>
                        <td>${card.issueDate}</td>
                        <td>${card.expiryDate}</td>
                    </tr>`;
                });

                output += '</table>';
            }

            showTestResult(2, 'info', output);
        }

        // اختبار تصدير PDF
        function testPDFExport() {
            if (testAppData.transmissionTable.length === 0) {
                showTestResult(3, 'error', 'لا توجد بيانات في جدول الإرسال للتصدير');
                return;
            }

            try {
                // محاكاة تصدير PDF
                let pdfContent = 'محتوى جدول الإرسال:\n\n';

                testAppData.transmissionTable.forEach((entry, index) => {
                    pdfContent += `${index + 1}. ${entry.type} - ${entry.ownerName} - ${entry.registrationNumber} - ${entry.operationDate}\n`;
                });

                // إنشاء ملف نصي كمحاكاة للـ PDF
                const blob = new Blob([pdfContent], { type: 'text/plain;charset=utf-8' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'جدول_الإرسال_تجريبي.txt';
                a.click();
                URL.revokeObjectURL(url);

                showTestResult(3, 'success', '✅ تم تصدير جدول الإرسال بنجاح (ملف نصي كمحاكاة)');
            } catch (error) {
                showTestResult(3, 'error', `❌ خطأ في التصدير: ${error.message}`);
            }
        }

        // اختبار الطباعة
        function testPrint() {
            if (testAppData.transmissionTable.length === 0) {
                showTestResult(3, 'error', 'لا توجد بيانات في جدول الإرسال للطباعة');
                return;
            }

            // إنشاء نافذة طباعة
            const printWindow = window.open('', '_blank', 'width=800,height=600');

            let printContent = `
                <!DOCTYPE html>
                <html dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <title>جدول الإرسال</title>
                    <style>
                        body { font-family: Arial, sans-serif; direction: rtl; }
                        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                        th, td { border: 1px solid black; padding: 8px; text-align: center; }
                        th { background-color: #f0f0f0; }
                        .header { text-align: center; margin-bottom: 20px; }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1>الجمهورية الجزائرية الديمقراطية الشعبية</h1>
                        <h2>مركز وقود المستقبل - عزيري عبد الله اسحاق</h2>
                        <h3>جدول إرسال تجريبي</h3>
                    </div>
                    <table>
                        <thead>
                            <tr>
                                <th>تركيب أو مراقبة</th>
                                <th>رقم خزان الغاز</th>
                                <th>الصنف</th>
                                <th>الرقم التسلسلي</th>
                                <th>رقم التسجيل</th>
                                <th>الإسم و اللقب</th>
                                <th>الرقم</th>
                                <th>تاريخ العملية</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            testAppData.transmissionTable.forEach((entry, index) => {
                printContent += `
                    <tr>
                        <td>${entry.type}</td>
                        <td>${entry.tankNumber}</td>
                        <td>${entry.carType}</td>
                        <td>${entry.serialNumber}</td>
                        <td>${entry.registrationNumber}</td>
                        <td>${entry.ownerName}</td>
                        <td>${index + 1}</td>
                        <td>${entry.operationDate}</td>
                    </tr>
                `;
            });

            printContent += `
                        </tbody>
                    </table>
                </body>
                </html>
            `;

            printWindow.document.write(printContent);
            printWindow.document.close();
            printWindow.print();

            showTestResult(3, 'success', '✅ تم فتح نافذة الطباعة');
        }

        // مسح البيانات التجريبية
        function clearTestData() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات التجريبية؟')) {
                testAppData = {
                    customers: [],
                    vehicles: [],
                    gasCards: [],
                    transmissionTable: []
                };

                saveTestData();
                showTestResult(4, 'success', '✅ تم مسح جميع البيانات التجريبية');
            }
        }

        // إنشاء بيانات تجريبية
        function generateSampleData() {
            const sampleCustomers = [
                { name: 'أحمد محمد علي', phone: '0555123456' },
                { name: 'فاطمة حسن', phone: '0666789012' },
                { name: 'محمد عبد الله', phone: '0777345678' }
            ];

            const sampleVehicles = [
                { plate: '1506615-26', type: 'Toyota Corolla' },
                { plate: '2507720-31', type: 'Peugeot 206' },
                { plate: '3608825-16', type: 'Renault Clio' }
            ];

            const serviceTypes = ['تركيب', 'مراقبة', 'تجديد بطاقة'];

            // مسح البيانات الحالية
            testAppData = {
                customers: [],
                vehicles: [],
                gasCards: [],
                transmissionTable: []
            };

            sampleCustomers.forEach((customerData, index) => {
                const customerId = generateId();
                const vehicleId = generateId();
                const serviceType = serviceTypes[index % serviceTypes.length];
                const serviceDate = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

                // إضافة العميل
                const customer = {
                    id: customerId,
                    name: customerData.name,
                    phone: customerData.phone,
                    createdAt: new Date().toISOString()
                };
                testAppData.customers.push(customer);

                // إضافة السيارة
                const vehicle = {
                    id: vehicleId,
                    plateNumber: sampleVehicles[index].plate,
                    brand: sampleVehicles[index].type.split(' ')[0],
                    model: sampleVehicles[index].type.split(' ').slice(1).join(' '),
                    customerId: customerId,
                    createdAt: new Date().toISOString()
                };
                testAppData.vehicles.push(vehicle);

                // إضافة الخدمة حسب النوع
                if (serviceType === 'تركيب' || serviceType === 'مراقبة') {
                    const transmissionEntry = {
                        id: generateId(),
                        type: serviceType,
                        tankNumber: `150${Math.floor(Math.random() * 10000)}`,
                        carType: sampleVehicles[index].type,
                        serialNumber: `VF${Math.floor(Math.random() * 1000000000)}`,
                        registrationNumber: sampleVehicles[index].plate,
                        ownerName: customerData.name,
                        operationDate: serviceDate,
                        customerId: customerId,
                        vehicleId: vehicleId,
                        createdAt: new Date().toISOString()
                    };
                    testAppData.transmissionTable.push(transmissionEntry);
                } else {
                    const gasCard = {
                        id: generateId(),
                        customerId: customerId,
                        vehicleId: vehicleId,
                        cardNumber: generateCardNumber(),
                        issueDate: serviceDate,
                        expiryDate: new Date(new Date(serviceDate).setFullYear(new Date(serviceDate).getFullYear() + 1)).toISOString().split('T')[0],
                        status: 'active',
                        notes: 'بطاقة تجريبية',
                        createdAt: new Date().toISOString()
                    };
                    testAppData.gasCards.push(gasCard);
                }
            });

            saveTestData();

            let resultMessage = `✅ تم إنشاء البيانات التجريبية:<br>`;
            resultMessage += `👥 العملاء: ${testAppData.customers.length}<br>`;
            resultMessage += `🚗 السيارات: ${testAppData.vehicles.length}<br>`;
            resultMessage += `📋 جدول الإرسال: ${testAppData.transmissionTable.length}<br>`;
            resultMessage += `🎫 بطاقات الغاز: ${testAppData.gasCards.length}`;

            showTestResult(5, 'success', resultMessage);
        }

        // اختبار تنبيهات الإرسال
        function testTransmissionReminders() {
            // حساب موعد الإرسال القادم
            const now = new Date();
            let nextDate = new Date();

            // محاكاة موعد قريب (خلال 5 أيام)
            nextDate.setDate(now.getDate() + 5);
            testAppData.settings.company.nextTransmissionDate = nextDate.toISOString().split('T')[0];

            const daysLeft = Math.ceil((nextDate - now) / (1000 * 60 * 60 * 24));

            let output = '<h4>📋 تنبيهات جدول الإرسال:</h4>';
            output += `<p><strong>الموعد القادم:</strong> ${nextDate.toLocaleDateString('ar-SA')}</p>`;
            output += `<p><strong>الأيام المتبقية:</strong> ${daysLeft} يوم</p>`;
            output += `<p><strong>المديرية:</strong> ${testAppData.settings.company.directorateName}</p>`;
            output += `<p><strong>تكرار الإرسال:</strong> ${testAppData.settings.company.transmissionFrequency === 'both' ? 'كل 6 أشهر وسنوياً' : testAppData.settings.company.transmissionFrequency}</p>`;

            if (daysLeft <= 7) {
                output += '<div class="error">⚠️ تنبيه: يجب إرسال الجدول قريباً!</div>';
            } else {
                output += '<div class="success">✅ لا توجد تنبيهات عاجلة</div>';
            }

            saveTestData();
            showTestResult(4, 'info', output);
        }

        // اختبار تسجيل الإرسال
        function testMarkSent() {
            if (testAppData.transmissionTable.length === 0) {
                showTestResult(4, 'error', 'لا توجد عمليات في جدول الإرسال لتسجيل الإرسال');
                return;
            }

            const now = new Date();
            const transmissionRecord = {
                date: now.toISOString().split('T')[0],
                entriesCount: testAppData.transmissionTable.length,
                period: getTransmissionPeriod(),
                sentAt: now.toISOString()
            };

            // إضافة إلى تاريخ الإرسالات
            testAppData.settings.company.transmissionHistory.push(transmissionRecord);
            testAppData.settings.company.lastTransmissionDate = now.toISOString().split('T')[0];

            // حساب الموعد القادم (6 أشهر من الآن)
            const nextDate = new Date();
            nextDate.setMonth(nextDate.getMonth() + 6);
            testAppData.settings.company.nextTransmissionDate = nextDate.toISOString().split('T')[0];

            saveTestData();

            let output = '<h4>✅ تم تسجيل الإرسال:</h4>';
            output += `<p><strong>تاريخ الإرسال:</strong> ${now.toLocaleDateString('ar-SA')}</p>`;
            output += `<p><strong>عدد العمليات:</strong> ${transmissionRecord.entriesCount}</p>`;
            output += `<p><strong>الفترة:</strong> ${transmissionRecord.period}</p>`;
            output += `<p><strong>الموعد القادم:</strong> ${nextDate.toLocaleDateString('ar-SA')}</p>`;

            showTestResult(4, 'success', output);
        }

        // اختبار إعدادات المؤسسة
        function testCompanySettings() {
            let output = '<h4>🏢 إعدادات المؤسسة:</h4>';
            output += `<p><strong>اسم المؤسسة:</strong> ${testAppData.settings.company.name}</p>`;
            output += `<p><strong>رقم المؤسسة:</strong> ${testAppData.settings.company.number}</p>`;
            output += `<p><strong>المديرية:</strong> ${testAppData.settings.company.directorateName}</p>`;
            output += `<p><strong>تكرار الإرسال:</strong> ${testAppData.settings.company.transmissionFrequency}</p>`;
            output += `<p><strong>أيام التذكير:</strong> ${testAppData.settings.company.transmissionReminderDays}</p>`;

            if (testAppData.settings.company.transmissionHistory.length > 0) {
                output += '<h5>📋 تاريخ الإرسالات:</h5><ul>';
                testAppData.settings.company.transmissionHistory.forEach(record => {
                    output += `<li>${record.date} - ${record.period} (${record.entriesCount} عملية)</li>`;
                });
                output += '</ul>';
            } else {
                output += '<p class="info">لا يوجد تاريخ إرسالات</p>';
            }

            showTestResult(4, 'info', output);
        }

        // تحديد فترة الإرسال
        function getTransmissionPeriod() {
            const now = new Date();
            const month = now.getMonth();
            const year = now.getFullYear();

            if (month < 6) {
                return `النصف الأول من ${year}`;
            } else {
                return `النصف الثاني من ${year}`;
            }
        }

        // تحديث مسح البيانات
        function clearTestData() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات التجريبية؟')) {
                testAppData = {
                    customers: [],
                    vehicles: [],
                    gasCards: [],
                    transmissionTable: [],
                    settings: {
                        company: {
                            name: 'مركز وقود المستقبل - عزيري عبد الله اسحاق',
                            number: '463/2019',
                            directorateName: 'مدير الصناعة و المناجم لولاية المدية',
                            transmissionFrequency: 'both',
                            nextTransmissionDate: '',
                            transmissionReminderDays: 7,
                            lastTransmissionDate: '',
                            transmissionHistory: []
                        }
                    }
                };

                saveTestData();
                showTestResult(5, 'success', '✅ تم مسح جميع البيانات التجريبية');
            }
        }

        // تحميل البيانات عند بدء الصفحة
        loadTestData();
    </script>
</body>
</html>
