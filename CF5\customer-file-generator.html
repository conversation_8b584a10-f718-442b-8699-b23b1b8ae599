<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مولد ملفات العملاء - Customer File Generator</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            direction: rtl;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #eee;
        }
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-label {
            display: block;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
        }
        .form-input, .form-select {
            width: 100%;
            padding: 10px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }
        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            margin: 5px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
        .btn-success { background: #27ae60; }
        .btn-success:hover { background: #229954; }
        .btn-warning { background: #f39c12; }
        .btn-warning:hover { background: #e67e22; }
        .preview-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border: 2px solid #e9ecef;
        }
        .file-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .file-option {
            background: #ecf0f1;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        .file-option:hover {
            background: #d5dbdb;
            border-color: #3498db;
        }
        .file-option.selected {
            background: #e8f4fd;
            border-color: #3498db;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-file-alt"></i> مولد ملفات العملاء</h1>
            <h2>Customer File Generator</h2>
            <p>إنشاء ملفات احترافية لإرسالها للعملاء مع تراخيصهم</p>
        </div>

        <div class="form-grid">
            <!-- معلومات الترخيص -->
            <div>
                <h3><i class="fas fa-key"></i> معلومات الترخيص</h3>
                <div class="form-group">
                    <label class="form-label">كود الترخيص</label>
                    <input type="text" id="licenseCode" class="form-input" placeholder="YEAR-2025-XXXX-XXXX">
                </div>
                <div class="form-group">
                    <label class="form-label">نوع الترخيص</label>
                    <select id="licenseType" class="form-select">
                        <option value="demo">تجريبي (30 يوم)</option>
                        <option value="monthly">شهري (30 يوم)</option>
                        <option value="quarterly">ربع سنوي (90 يوم)</option>
                        <option value="yearly" selected>سنوي (365 يوم)</option>
                        <option value="lifetime">مدى الحياة</option>
                        <option value="admin">إداري</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">تاريخ الانتهاء</label>
                    <input type="date" id="expiryDate" class="form-input">
                </div>
            </div>

            <!-- معلومات العميل -->
            <div>
                <h3><i class="fas fa-user"></i> معلومات العميل</h3>
                <div class="form-group">
                    <label class="form-label">اسم العميل</label>
                    <input type="text" id="customerName" class="form-input" placeholder="أحمد محمد السعيد">
                </div>
                <div class="form-group">
                    <label class="form-label">البريد الإلكتروني</label>
                    <input type="email" id="customerEmail" class="form-input" placeholder="<EMAIL>">
                </div>
                <div class="form-group">
                    <label class="form-label">رقم الهاتف</label>
                    <input type="tel" id="customerPhone" class="form-input" placeholder="+966-50-123-4567">
                </div>
                <div class="form-group">
                    <label class="form-label">اسم الشركة (اختياري)</label>
                    <input type="text" id="companyName" class="form-input" placeholder="شركة الوقود المتقدم">
                </div>
            </div>
        </div>

        <!-- خيارات الملفات -->
        <div>
            <h3><i class="fas fa-file-download"></i> اختر نوع الملفات المطلوبة</h3>
            <div class="file-options">
                <div class="file-option selected" data-type="html">
                    <i class="fas fa-globe" style="font-size: 2rem; color: #e67e22;"></i>
                    <h4>ملف HTML تفاعلي</h4>
                    <p>ملف ويب احترافي مع تصميم جميل</p>
                </div>
                <div class="file-option selected" data-type="txt">
                    <i class="fas fa-file-alt" style="font-size: 2rem; color: #3498db;"></i>
                    <h4>ملف نصي بسيط</h4>
                    <p>ملف نصي سهل القراءة والطباعة</p>
                </div>
                <div class="file-option" data-type="pdf">
                    <i class="fas fa-file-pdf" style="font-size: 2rem; color: #e74c3c;"></i>
                    <h4>ملف PDF</h4>
                    <p>ملف PDF جاهز للطباعة</p>
                </div>
                <div class="file-option" data-type="email">
                    <i class="fas fa-envelope" style="font-size: 2rem; color: #9b59b6;"></i>
                    <h4>قالب بريد إلكتروني</h4>
                    <p>نص جاهز لإرساله بالبريد</p>
                </div>
            </div>
        </div>

        <!-- أزرار الإجراءات -->
        <div style="text-align: center; margin: 30px 0;">
            <button class="btn btn-success" onclick="generateFiles()">
                <i class="fas fa-magic"></i> إنشاء الملفات
            </button>
            <button class="btn btn-warning" onclick="previewFiles()">
                <i class="fas fa-eye"></i> معاينة
            </button>
            <button class="btn" onclick="clearForm()">
                <i class="fas fa-eraser"></i> مسح النموذج
            </button>
            <button class="btn" onclick="loadFromLicenseGenerator()">
                <i class="fas fa-download"></i> تحميل من مولد التراخيص
            </button>
        </div>

        <!-- قسم المعاينة -->
        <div id="previewSection" class="preview-section" style="display: none;">
            <h3><i class="fas fa-eye"></i> معاينة الملفات</h3>
            <div id="previewContent"></div>
        </div>

        <!-- قسم النتائج -->
        <div id="resultSection" style="display: none;">
            <h3><i class="fas fa-check-circle"></i> تم إنشاء الملفات بنجاح!</h3>
            <div id="downloadLinks"></div>
        </div>
    </div>

    <script>
        // تعيين تاريخ الانتهاء الافتراضي (سنة من الآن)
        document.getElementById('expiryDate').value = new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

        // معالجة اختيار أنواع الملفات
        document.querySelectorAll('.file-option').forEach(option => {
            option.addEventListener('click', function() {
                this.classList.toggle('selected');
            });
        });

        function getSelectedFileTypes() {
            return Array.from(document.querySelectorAll('.file-option.selected')).map(el => el.dataset.type);
        }

        function getLicenseData() {
            return {
                code: document.getElementById('licenseCode').value || 'YEAR-2025-XXXX-XXXX',
                type: document.getElementById('licenseType').value,
                expiryDate: document.getElementById('expiryDate').value,
                customerName: document.getElementById('customerName').value || 'عميل مؤسسة وقود المستقبل',
                customerEmail: document.getElementById('customerEmail').value,
                customerPhone: document.getElementById('customerPhone').value,
                companyName: document.getElementById('companyName').value,
                issueDate: new Date().toISOString().split('T')[0],
                createdAt: new Date().toISOString(),
                expiresAt: new Date(document.getElementById('expiryDate').value).toISOString()
            };
        }

        function getLicenseTypeName(type) {
            const names = {
                'demo': 'تجريبي (30 يوم)',
                'monthly': 'شهري (30 يوم)',
                'quarterly': 'ربع سنوي (90 يوم)',
                'yearly': 'سنوي (365 يوم)',
                'lifetime': 'مدى الحياة',
                'admin': 'إداري'
            };
            return names[type] || type;
        }

        function generateHTMLFile(data) {
            // قراءة قالب HTML وتعبئته بالبيانات
            return fetch('customer-license-template.html')
                .then(response => response.text())
                .then(template => {
                    return template
                        .replace(/\[اسم العميل\]/g, data.customerName)
                        .replace(/\[نوع الترخيص\]/g, getLicenseTypeName(data.type))
                        .replace(/\[تاريخ الإصدار\]/g, new Date(data.issueDate).toLocaleDateString('ar-SA'))
                        .replace(/\[تاريخ الانتهاء\]/g, new Date(data.expiryDate).toLocaleDateString('ar-SA'))
                        .replace(/\[كود الترخيص\]/g, data.code);
                })
                .catch(() => {
                    // في حالة عدم وجود القالب، إنشاء HTML بسيط
                    return `
                        <!DOCTYPE html>
                        <html lang="ar" dir="rtl">
                        <head>
                            <meta charset="UTF-8">
                            <title>ترخيص ${data.customerName}</title>
                            <style>
                                body { font-family: Arial, sans-serif; direction: rtl; padding: 20px; }
                                .license-code { background: #2c3e50; color: white; padding: 20px; text-align: center; font-size: 1.5rem; }
                            </style>
                        </head>
                        <body>
                            <h1>ترخيص مؤسسة وقود المستقبل</h1>
                            <p><strong>اسم العميل:</strong> ${data.customerName}</p>
                            <p><strong>نوع الترخيص:</strong> ${getLicenseTypeName(data.type)}</p>
                            <p><strong>تاريخ الإصدار:</strong> ${new Date(data.issueDate).toLocaleDateString('ar-SA')}</p>
                            <p><strong>تاريخ الانتهاء:</strong> ${new Date(data.expiryDate).toLocaleDateString('ar-SA')}</p>
                            <div class="license-code">${data.code}</div>
                        </body>
                        </html>
                    `;
                });
        }

        function generateTXTFile(data) {
            return fetch('customer-license-simple.txt')
                .then(response => response.text())
                .then(template => {
                    return template
                        .replace(/\[اسم العميل\]/g, data.customerName)
                        .replace(/\[نوع الترخيص\]/g, getLicenseTypeName(data.type))
                        .replace(/\[تاريخ الإصدار\]/g, new Date(data.issueDate).toLocaleDateString('ar-SA'))
                        .replace(/\[تاريخ الانتهاء\]/g, new Date(data.expiryDate).toLocaleDateString('ar-SA'))
                        .replace(/\[كود الترخيص\]/g, data.code)
                        .replace(/\[تاريخ الإنشاء\]/g, new Date().toLocaleDateString('ar-SA'))
                        .replace(/\[رقم المرجع\]/g, 'REF-' + Date.now())
                        .replace(/\[رقم الترخيص\]/g, data.code);
                })
                .catch(() => {
                    // نص بسيط في حالة عدم وجود القالب
                    return `
ترخيص مؤسسة وقود المستقبل
================================

اسم العميل: ${data.customerName}
نوع الترخيص: ${getLicenseTypeName(data.type)}
تاريخ الإصدار: ${new Date(data.issueDate).toLocaleDateString('ar-SA')}
تاريخ الانتهاء: ${new Date(data.expiryDate).toLocaleDateString('ar-SA')}

كود الترخيص: ${data.code}

تعليمات الاستخدام:
1. افتح التطبيق
2. أدخل كود الترخيص
3. انقر تسجيل الدخول

للدعم الفني: <EMAIL>
                    `;
                });
        }

        function generateEmailTemplate(data) {
            return `
الموضوع: ترخيص مؤسسة وقود المستقبل - ${data.customerName}

عزيزي/عزيزتي ${data.customerName}،

نشكركم لاختياركم مؤسسة وقود المستقبل. يسعدنا أن نرسل إليكم ترخيص النظام الخاص بكم.

معلومات الترخيص:
- نوع الترخيص: ${getLicenseTypeName(data.type)}
- تاريخ الإصدار: ${new Date(data.issueDate).toLocaleDateString('ar-SA')}
- تاريخ الانتهاء: ${new Date(data.expiryDate).toLocaleDateString('ar-SA')}

كود الترخيص الخاص بكم:
${data.code}

تعليمات التفعيل:
1. قوموا بتحميل التطبيق من الرابط المرفق
2. افتحوا التطبيق وأدخلوا كود الترخيص
3. انقروا على "تسجيل الدخول"

في حالة وجود أي استفسارات، لا تترددوا في التواصل معنا:
- البريد الإلكتروني: <EMAIL>
- الهاتف: +966-11-123-4567

مع أطيب التحيات،
فريق مؤسسة وقود المستقبل
            `;
        }

        async function generateFiles() {
            const data = getLicenseData();
            const selectedTypes = getSelectedFileTypes();
            
            if (selectedTypes.length === 0) {
                alert('يرجى اختيار نوع ملف واحد على الأقل');
                return;
            }

            const resultSection = document.getElementById('resultSection');
            const downloadLinks = document.getElementById('downloadLinks');
            
            resultSection.style.display = 'block';
            downloadLinks.innerHTML = '<p>جاري إنشاء الملفات...</p>';

            let linksHTML = '';

            for (const type of selectedTypes) {
                try {
                    let content, filename, mimeType;

                    switch (type) {
                        case 'html':
                            content = await generateHTMLFile(data);
                            filename = `license-${data.customerName.replace(/\s+/g, '-')}.html`;
                            mimeType = 'text/html';
                            break;
                        case 'txt':
                            content = await generateTXTFile(data);
                            filename = `license-${data.customerName.replace(/\s+/g, '-')}.txt`;
                            mimeType = 'text/plain';
                            break;
                        case 'email':
                            content = generateEmailTemplate(data);
                            filename = `email-template-${data.customerName.replace(/\s+/g, '-')}.txt`;
                            mimeType = 'text/plain';
                            break;
                    }

                    if (content) {
                        const blob = new Blob([content], { type: mimeType + ';charset=utf-8' });
                        const url = URL.createObjectURL(blob);
                        
                        linksHTML += `
                            <div style="margin: 10px 0; padding: 15px; background: #f8f9fa; border-radius: 8px;">
                                <i class="fas fa-file"></i>
                                <strong>${filename}</strong>
                                <a href="${url}" download="${filename}" class="btn" style="margin-right: 10px;">
                                    <i class="fas fa-download"></i> تحميل
                                </a>
                            </div>
                        `;
                    }
                } catch (error) {
                    console.error(`خطأ في إنشاء ملف ${type}:`, error);
                }
            }

            downloadLinks.innerHTML = linksHTML || '<p>حدث خطأ في إنشاء الملفات</p>';
        }

        function previewFiles() {
            const data = getLicenseData();
            const previewSection = document.getElementById('previewSection');
            const previewContent = document.getElementById('previewContent');
            
            previewSection.style.display = 'block';
            previewContent.innerHTML = `
                <div class="info">
                    <h4>معاينة البيانات:</h4>
                    <p><strong>اسم العميل:</strong> ${data.customerName}</p>
                    <p><strong>نوع الترخيص:</strong> ${getLicenseTypeName(data.type)}</p>
                    <p><strong>كود الترخيص:</strong> ${data.code}</p>
                    <p><strong>تاريخ الإصدار:</strong> ${new Date(data.issueDate).toLocaleDateString('ar-SA')}</p>
                    <p><strong>تاريخ الانتهاء:</strong> ${new Date(data.expiryDate).toLocaleDateString('ar-SA')}</p>
                    <p><strong>أنواع الملفات المختارة:</strong> ${getSelectedFileTypes().join(', ')}</p>
                </div>
            `;
        }

        function clearForm() {
            document.getElementById('licenseCode').value = '';
            document.getElementById('customerName').value = '';
            document.getElementById('customerEmail').value = '';
            document.getElementById('customerPhone').value = '';
            document.getElementById('companyName').value = '';
            document.getElementById('previewSection').style.display = 'none';
            document.getElementById('resultSection').style.display = 'none';
        }

        function loadFromLicenseGenerator() {
            // محاولة تحميل آخر ترخيص تم إنشاؤه
            const licenses = JSON.parse(localStorage.getItem('validLicenses') || '[]');
            if (licenses.length > 0) {
                const lastLicense = licenses[licenses.length - 1];
                document.getElementById('licenseCode').value = lastLicense.code;
                document.getElementById('licenseType').value = lastLicense.type;
                document.getElementById('customerName').value = lastLicense.customerName || '';
                document.getElementById('expiryDate').value = lastLicense.expiresAt.split('T')[0];
                alert('تم تحميل بيانات آخر ترخيص تم إنشاؤه');
            } else {
                alert('لا توجد تراخيص محفوظة');
            }
        }
    </script>
</body>
</html>
