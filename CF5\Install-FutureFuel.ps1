# مثبت مؤسسة وقود المستقبل - Future Fuel Corporation Installer
# PowerShell Installation Script
# Version: 2.2.0
# Date: 2025-07-08

param(
    [string]$InstallPath = "$env:USERPROFILE\Desktop\Future Fuel",
    [switch]$Silent = $false,
    [switch]$CreateShortcuts = $true,
    [switch]$RegisterProgram = $true,
    [string]$LicenseCode = ""
)

# إعدادات البرنامج
$AppName = "مؤسسة وقود المستقبل"
$AppNameEN = "Future Fuel Corporation"
$Version = "2.2.0"
$Publisher = "Future Fuel Corporation"

# ألوان النص
$Colors = @{
    Red = "Red"
    Green = "Green"
    Yellow = "Yellow"
    Blue = "Blue"
    Cyan = "Cyan"
    Magenta = "Magenta"
    White = "White"
}

function Write-ColorText {
    param(
        [string]$Text,
        [string]$Color = "White"
    )
    Write-Host $Text -ForegroundColor $Color
}

function Write-Header {
    param([string]$Title)
    
    Clear-Host
    Write-ColorText "=================================================================================" "Cyan"
    Write-ColorText "                    $Title" "White"
    Write-ColorText "=================================================================================" "Cyan"
    Write-Host ""
}

function Write-Step {
    param(
        [string]$StepName,
        [string]$Status = "Info"
    )
    
    $Symbol = switch ($Status) {
        "Success" { "✅" }
        "Error" { "❌" }
        "Warning" { "⚠️" }
        "Info" { "ℹ️" }
        default { "•" }
    }
    
    $Color = switch ($Status) {
        "Success" { "Green" }
        "Error" { "Red" }
        "Warning" { "Yellow" }
        "Info" { "Blue" }
        default { "White" }
    }
    
    Write-ColorText "$Symbol $StepName" $Color
}

function Test-Prerequisites {
    Write-Step "فحص متطلبات النظام..." "Info"
    
    # فحص نظام التشغيل
    $OS = Get-WmiObject -Class Win32_OperatingSystem
    if ([int]$OS.Version.Split('.')[0] -lt 6) {
        Write-Step "نظام التشغيل غير مدعوم. يتطلب Windows 7 أو أحدث" "Error"
        return $false
    }
    
    # فحص المساحة المتاحة
    $Drive = (Get-Item $InstallPath).PSDrive.Name
    $FreeSpace = (Get-WmiObject -Class Win32_LogicalDisk -Filter "DeviceID='${Drive}:'").FreeSpace
    if ($FreeSpace -lt 104857600) { # 100 MB
        Write-Step "مساحة غير كافية. مطلوب 100 MB على الأقل" "Error"
        return $false
    }
    
    # فحص صلاحيات الكتابة
    try {
        $TestPath = Split-Path $InstallPath -Parent
        $TestFile = Join-Path $TestPath "test_write_permission.tmp"
        "test" | Out-File -FilePath $TestFile -Force
        Remove-Item $TestFile -Force
    }
    catch {
        Write-Step "لا توجد صلاحيات كتابة في المجلد المحدد" "Error"
        return $false
    }
    
    Write-Step "جميع متطلبات النظام متوفرة" "Success"
    return $true
}

function Install-Application {
    Write-Step "إنشاء مجلد التثبيت..." "Info"
    
    try {
        if (-not (Test-Path $InstallPath)) {
            New-Item -ItemType Directory -Path $InstallPath -Force | Out-Null
        }
        Write-Step "تم إنشاء مجلد التثبيت: $InstallPath" "Success"
    }
    catch {
        Write-Step "فشل في إنشاء مجلد التثبيت: $($_.Exception.Message)" "Error"
        return $false
    }
    
    Write-Step "نسخ ملفات التطبيق..." "Info"
    
    try {
        # نسخ ملفات customer-package
        if (Test-Path "customer-package") {
            Copy-Item -Path "customer-package\*" -Destination $InstallPath -Recurse -Force
            Write-Step "تم نسخ ملفات التطبيق الأساسية" "Success"
        }
        else {
            Write-Step "مجلد customer-package غير موجود" "Error"
            return $false
        }
        
        # نسخ الملفات المشتركة
        if (Test-Path "shared") {
            $SharedPath = Join-Path $InstallPath "shared"
            Copy-Item -Path "shared\*" -Destination $SharedPath -Recurse -Force
            Write-Step "تم نسخ الملفات المشتركة" "Success"
        }
        
        # نسخ الوثائق
        if (Test-Path "docs") {
            $DocsPath = Join-Path $InstallPath "docs"
            Copy-Item -Path "docs\*" -Destination $DocsPath -Recurse -Force
            Write-Step "تم نسخ ملفات الوثائق" "Success"
        }
        
        # نسخ ملفات README
        Get-ChildItem -Path "." -Filter "*.md" | ForEach-Object {
            Copy-Item -Path $_.FullName -Destination $InstallPath -Force
        }
        
        return $true
    }
    catch {
        Write-Step "فشل في نسخ الملفات: $($_.Exception.Message)" "Error"
        return $false
    }
}

function Create-Shortcuts {
    if (-not $CreateShortcuts) {
        return $true
    }
    
    Write-Step "إنشاء الاختصارات..." "Info"
    
    try {
        $WshShell = New-Object -ComObject WScript.Shell
        
        # اختصار سطح المكتب
        $DesktopPath = [Environment]::GetFolderPath("Desktop")
        $ShortcutPath = Join-Path $DesktopPath "$AppName.lnk"
        $Shortcut = $WshShell.CreateShortcut($ShortcutPath)
        $Shortcut.TargetPath = Join-Path $InstallPath "app\index.html"
        $Shortcut.WorkingDirectory = Join-Path $InstallPath "app"
        $Shortcut.Description = "$AppName - $Version"
        $Shortcut.Save()
        
        Write-Step "تم إنشاء اختصار سطح المكتب" "Success"
        
        # اختصار قائمة ابدأ
        $StartMenuPath = Join-Path $env:APPDATA "Microsoft\Windows\Start Menu\Programs\Future Fuel"
        if (-not (Test-Path $StartMenuPath)) {
            New-Item -ItemType Directory -Path $StartMenuPath -Force | Out-Null
        }
        
        $StartShortcutPath = Join-Path $StartMenuPath "$AppName.lnk"
        $StartShortcut = $WshShell.CreateShortcut($StartShortcutPath)
        $StartShortcut.TargetPath = Join-Path $InstallPath "app\index.html"
        $StartShortcut.WorkingDirectory = Join-Path $InstallPath "app"
        $StartShortcut.Description = "$AppName - $Version"
        $StartShortcut.Save()
        
        Write-Step "تم إنشاء اختصار قائمة ابدأ" "Success"
        
        return $true
    }
    catch {
        Write-Step "فشل في إنشاء الاختصارات: $($_.Exception.Message)" "Warning"
        return $true # لا نفشل التثبيت بسبب الاختصارات
    }
}

function Register-Program {
    if (-not $RegisterProgram) {
        return $true
    }
    
    Write-Step "تسجيل البرنامج في النظام..." "Info"
    
    try {
        $UninstallKey = "HKCU:\Software\Microsoft\Windows\CurrentVersion\Uninstall\FutureFuel"
        
        if (-not (Test-Path $UninstallKey)) {
            New-Item -Path $UninstallKey -Force | Out-Null
        }
        
        Set-ItemProperty -Path $UninstallKey -Name "DisplayName" -Value $AppName
        Set-ItemProperty -Path $UninstallKey -Name "DisplayVersion" -Value $Version
        Set-ItemProperty -Path $UninstallKey -Name "Publisher" -Value $Publisher
        Set-ItemProperty -Path $UninstallKey -Name "InstallLocation" -Value $InstallPath
        Set-ItemProperty -Path $UninstallKey -Name "UninstallString" -Value (Join-Path $InstallPath "Uninstall.bat")
        Set-ItemProperty -Path $UninstallKey -Name "InstallDate" -Value (Get-Date -Format "yyyyMMdd")
        
        Write-Step "تم تسجيل البرنامج في النظام" "Success"
        return $true
    }
    catch {
        Write-Step "فشل في تسجيل البرنامج: $($_.Exception.Message)" "Warning"
        return $true # لا نفشل التثبيت بسبب التسجيل
    }
}

function Create-Uninstaller {
    Write-Step "إنشاء ملف إلغاء التثبيت..." "Info"
    
    try {
        $UninstallScript = @"
@echo off
chcp 65001 >nul
title إلغاء تثبيت $AppName

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                        إلغاء تثبيت $AppName                    ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.
echo هل أنت متأكد من إلغاء تثبيت البرنامج؟
echo.
echo سيتم حذف:
echo   • جميع ملفات البرنامج
echo   • الاختصارات
echo   • الإعدادات المحفوظة
echo.
set /p confirm="اكتب 'نعم' للتأكيد: "
if /i "%confirm%"=="نعم" (
    echo.
    echo جاري إلغاء التثبيت...
    
    echo حذف ملفات البرنامج...
    rmdir /s /q "$InstallPath" 2>nul
    
    echo حذف الاختصارات...
    del "%USERPROFILE%\Desktop\$AppName.lnk" 2>nul
    rmdir /s /q "%APPDATA%\Microsoft\Windows\Start Menu\Programs\Future Fuel" 2>nul
    
    echo إزالة التسجيل من النظام...
    reg delete "HKCU\Software\Microsoft\Windows\CurrentVersion\Uninstall\FutureFuel" /f 2>nul
    
    echo.
    echo ✅ تم إلغاء التثبيت بنجاح
    echo.
    echo شكراً لاستخدامكم $AppName
) else (
    echo.
    echo تم إلغاء عملية إلغاء التثبيت
)
echo.
pause
"@
        
        $UninstallPath = Join-Path $InstallPath "Uninstall.bat"
        $UninstallScript | Out-File -FilePath $UninstallPath -Encoding UTF8
        
        Write-Step "تم إنشاء ملف إلغاء التثبيت" "Success"
        return $true
    }
    catch {
        Write-Step "فشل في إنشاء ملف إلغاء التثبيت: $($_.Exception.Message)" "Warning"
        return $true
    }
}

function Set-LicenseCode {
    if ([string]::IsNullOrEmpty($LicenseCode)) {
        return $true
    }
    
    Write-Step "تعيين كود الترخيص..." "Info"
    
    try {
        # إنشاء ملف تكوين الترخيص
        $LicenseConfig = @{
            code = $LicenseCode
            activatedAt = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
            deviceId = $env:COMPUTERNAME
        }
        
        $ConfigPath = Join-Path $InstallPath "app\license.json"
        $LicenseConfig | ConvertTo-Json | Out-File -FilePath $ConfigPath -Encoding UTF8
        
        Write-Step "تم تعيين كود الترخيص بنجاح" "Success"
        return $true
    }
    catch {
        Write-Step "فشل في تعيين كود الترخيص: $($_.Exception.Message)" "Warning"
        return $true
    }
}

function Show-CompletionMessage {
    Write-Header "✅ تم التثبيت بنجاح!"
    
    Write-ColorText "🎉 تهانينا! تم تثبيت $AppName بنجاح" "Green"
    Write-Host ""
    
    Write-ColorText "📁 مجلد التثبيت: $InstallPath" "Yellow"
    if ($CreateShortcuts) {
        Write-ColorText "🖥️  اختصار سطح المكتب: $AppName" "Yellow"
        Write-ColorText "📋 قائمة ابدأ: البرامج > Future Fuel" "Yellow"
    }
    Write-Host ""
    
    Write-ColorText "🚀 لبدء الاستخدام:" "Cyan"
    Write-ColorText "  1️⃣  انقر نقراً مزدوجاً على اختصار سطح المكتب" "White"
    Write-ColorText "  2️⃣  أدخل كود الترخيص" "White"
    Write-ColorText "  3️⃣  انقر على 'تسجيل الدخول'" "White"
    Write-Host ""
    
    if ([string]::IsNullOrEmpty($LicenseCode)) {
        Write-ColorText "🔑 للحصول على كود الترخيص:" "Blue"
        Write-ColorText "  📧 البريد الإلكتروني: <EMAIL>" "White"
        Write-ColorText "  📱 الهاتف: +966-11-123-4567" "White"
        Write-Host ""
    }
    
    if (-not $Silent) {
        $Launch = Read-Host "هل تريد تشغيل البرنامج الآن؟ (y/n)"
        if ($Launch -eq "y" -or $Launch -eq "Y") {
            $AppPath = Join-Path $InstallPath "app\index.html"
            if (Test-Path $AppPath) {
                Start-Process $AppPath
                Write-ColorText "✅ تم تشغيل البرنامج بنجاح!" "Green"
            }
        }
    }
    
    Write-Host ""
    Write-ColorText "شكراً لاختياركم مؤسسة وقود المستقبل!" "Magenta"
}

# البرنامج الرئيسي
function Main {
    try {
        if (-not $Silent) {
            Write-Header "🚀 مثبت مؤسسة وقود المستقبل"
            Write-ColorText "الإصدار: $Version" "Yellow"
            Write-ColorText "المطور: $Publisher" "Yellow"
            Write-Host ""
            Write-ColorText "سيتم تثبيت البرنامج في: $InstallPath" "Cyan"
            Write-Host ""
            
            $Continue = Read-Host "هل تريد المتابعة؟ (y/n)"
            if ($Continue -ne "y" -and $Continue -ne "Y") {
                Write-ColorText "تم إلغاء التثبيت" "Yellow"
                return
            }
        }
        
        # فحص المتطلبات
        if (-not (Test-Prerequisites)) {
            Write-ColorText "فشل في فحص المتطلبات" "Red"
            return
        }
        
        # تثبيت التطبيق
        if (-not (Install-Application)) {
            Write-ColorText "فشل في تثبيت التطبيق" "Red"
            return
        }
        
        # إنشاء الاختصارات
        Create-Shortcuts | Out-Null
        
        # تسجيل البرنامج
        Register-Program | Out-Null
        
        # إنشاء ملف إلغاء التثبيت
        Create-Uninstaller | Out-Null
        
        # تعيين كود الترخيص
        Set-LicenseCode | Out-Null
        
        # عرض رسالة الإكمال
        Show-CompletionMessage
        
    }
    catch {
        Write-ColorText "حدث خطأ غير متوقع: $($_.Exception.Message)" "Red"
        Write-ColorText "يرجى المحاولة مرة أخرى أو التواصل مع الدعم الفني" "Yellow"
    }
}

# تشغيل البرنامج الرئيسي
Main
