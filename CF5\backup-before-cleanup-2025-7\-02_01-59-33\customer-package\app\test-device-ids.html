<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار معرفات الأجهزة المختلفة</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
            direction: rtl;
            margin: 0;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            background: linear-gradient(135deg, #2196f3, #1976d2);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        .device-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .device-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }
        .device-card:hover {
            border-color: #2196f3;
            box-shadow: 0 4px 12px rgba(33, 150, 243, 0.15);
        }
        .device-card.active {
            border-color: #4caf50;
            background: #e8f5e8;
        }
        .device-card.error {
            border-color: #f44336;
            background: #ffebee;
        }
        .device-id {
            font-family: monospace;
            font-size: 14px;
            background: white;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #ddd;
            margin: 10px 0;
            word-break: break-all;
        }
        .device-type {
            font-weight: bold;
            color: #2196f3;
            margin-bottom: 10px;
        }
        .status {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            display: inline-block;
            margin: 5px 0;
        }
        .status.valid {
            background: #e8f5e8;
            color: #2e7d32;
        }
        .status.invalid {
            background: #ffebee;
            color: #c62828;
        }
        .test-section {
            background: #fff3cd;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #ffc107;
            margin: 20px 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            box-sizing: border-box;
        }
        button {
            background: #2196f3;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: background 0.3s;
        }
        button:hover {
            background: #1976d2;
        }
        button.success {
            background: #4caf50;
        }
        button.success:hover {
            background: #45a049;
        }
        button.warning {
            background: #ff9800;
        }
        button.warning:hover {
            background: #f57c00;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        .result.success {
            background: #e8f5e8;
            color: #2e7d32;
            border: 1px solid #4caf50;
        }
        .result.error {
            background: #ffebee;
            color: #c62828;
            border: 1px solid #f44336;
        }
        .result.info {
            background: #e3f2fd;
            color: #1976d2;
            border: 1px solid #2196f3;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-mobile-alt"></i> اختبار معرفات الأجهزة المختلفة</h1>
            <p>Device ID Compatibility Testing System</p>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-vial"></i> اختبار معرف جهاز مخصص</h3>
            <div class="form-group">
                <label>أدخل معرف الجهاز للاختبار:</label>
                <input type="text" id="customDeviceId" placeholder="WIN-CHR-4F27B665-2025" value="WIN-CHR-4F27B665-2025">
            </div>
            <button onclick="testCustomDeviceId()" class="success">
                <i class="fas fa-check"></i> اختبار معرف الجهاز
            </button>
            <button onclick="useAsCurrentDevice()" class="warning">
                <i class="fas fa-download"></i> استخدام كمعرف حالي
            </button>
            <div id="customResult" class="result"></div>
        </div>

        <h3><i class="fas fa-list"></i> أمثلة على معرفات الأجهزة المدعومة</h3>
        
        <div class="device-grid" id="deviceGrid">
            <!-- سيتم ملؤها بـ JavaScript -->
        </div>

        <div class="test-section">
            <h3><i class="fas fa-cog"></i> معرف الجهاز الحالي</h3>
            <div class="device-id" id="currentDeviceId">جاري التحميل...</div>
            <button onclick="regenerateDeviceId()">
                <i class="fas fa-refresh"></i> توليد معرف جديد
            </button>
            <button onclick="copyCurrentDeviceId()" class="success">
                <i class="fas fa-copy"></i> نسخ المعرف الحالي
            </button>
            <button onclick="testCurrentDeviceId()" class="warning">
                <i class="fas fa-vial"></i> اختبار المعرف الحالي
            </button>
        </div>

        <div id="testResults" class="result"></div>
    </div>

    <script src="database-sync.js"></script>
    <script>
        // أمثلة على معرفات الأجهزة المختلفة
        const deviceExamples = [
            {
                type: 'Windows + Chrome',
                id: 'WIN-CHR-4F27B665-2025',
                description: 'جهاز Windows مع متصفح Chrome'
            },
            {
                type: 'Windows + Firefox',
                id: 'WIN-FFX-A1B2C3D4-2024',
                description: 'جهاز Windows مع متصفح Firefox'
            },
            {
                type: 'Windows + Edge',
                id: 'WIN-EDG-12345678-2025',
                description: 'جهاز Windows مع متصفح Edge'
            },
            {
                type: 'Mac + Safari',
                id: 'MAC-SAF-ABCDEF12-2024',
                description: 'جهاز Mac مع متصفح Safari'
            },
            {
                type: 'Mac + Chrome',
                id: 'MAC-CHR-98765432-2025',
                description: 'جهاز Mac مع متصفح Chrome'
            },
            {
                type: 'Linux + Firefox',
                id: 'LNX-FFX-FEDCBA98-2024',
                description: 'جهاز Linux مع متصفح Firefox'
            },
            {
                type: 'Android + Chrome',
                id: 'AND-CHR-MOBILE01-2025',
                description: 'جهاز Android مع متصفح Chrome'
            },
            {
                type: 'iOS + Safari',
                id: 'IOS-SAF-IPHONE12-2024',
                description: 'جهاز iOS مع متصفح Safari'
            },
            {
                type: 'Generated Device',
                id: 'DEV-GEN-ABCD1234-EFGH',
                description: 'معرف مولد تلقائياً'
            },
            {
                type: 'Custom Format',
                id: 'CUSTOM-ID-123456-ABCD',
                description: 'تنسيق مخصص'
            }
        ];

        // التحقق من صحة معرف الجهاز
        function isValidDeviceId(deviceId) {
            const patterns = [
                /^[A-Z]{3}-[A-Z]{3}-[A-Z0-9]{8}-[0-9]{4}$/,  // WIN-CHR-4F27B665-2025
                /^DEV-[A-Z0-9]{3,}-[A-Z0-9]{4,}-[A-Z0-9]{4,}$/,  // DEV-GEN-XXXXX-XXXX
                /^[A-Z0-9]{3,}-[A-Z0-9]{3,}-[A-Z0-9]{4,}-[A-Z0-9]{4,}$/,  // عام
                /^[A-Z0-9\-]{10,}$/  // أي معرف يحتوي على أحرف وأرقام وشرطات بطول 10+ أحرف
            ];
            
            return patterns.some(pattern => pattern.test(deviceId.toUpperCase()));
        }

        // عرض أمثلة معرفات الأجهزة
        function displayDeviceExamples() {
            const grid = document.getElementById('deviceGrid');
            grid.innerHTML = '';

            deviceExamples.forEach((device, index) => {
                const isValid = isValidDeviceId(device.id);
                const card = document.createElement('div');
                card.className = `device-card ${isValid ? 'active' : 'error'}`;
                card.innerHTML = `
                    <div class="device-type">${device.type}</div>
                    <div class="device-id">${device.id}</div>
                    <div class="status ${isValid ? 'valid' : 'invalid'}">
                        ${isValid ? '✅ صالح' : '❌ غير صالح'}
                    </div>
                    <p style="font-size: 12px; color: #666; margin: 10px 0 0 0;">${device.description}</p>
                    <button onclick="testDeviceExample('${device.id}')" style="margin-top: 10px; font-size: 12px; padding: 5px 10px;">
                        <i class="fas fa-vial"></i> اختبار
                    </button>
                    <button onclick="useDeviceExample('${device.id}')" style="margin-top: 10px; font-size: 12px; padding: 5px 10px;" class="warning">
                        <i class="fas fa-download"></i> استخدام
                    </button>
                `;
                grid.appendChild(card);
            });
        }

        // اختبار معرف جهاز مخصص
        function testCustomDeviceId() {
            const deviceId = document.getElementById('customDeviceId').value.trim().toUpperCase();
            const resultDiv = document.getElementById('customResult');
            
            if (!deviceId) {
                showResult('customResult', '⚠️ يرجى إدخال معرف الجهاز', 'error');
                return;
            }
            
            const isValid = isValidDeviceId(deviceId);
            const message = isValid 
                ? `✅ معرف الجهاز صالح: ${deviceId}`
                : `❌ معرف الجهاز غير صالح: ${deviceId}`;
            
            showResult('customResult', message, isValid ? 'success' : 'error');
            
            if (isValid) {
                console.log('معرف الجهاز صالح:', deviceId);
            } else {
                console.log('معرف الجهاز غير صالح:', deviceId);
            }
        }

        // استخدام معرف مخصص كمعرف حالي
        function useAsCurrentDevice() {
            const deviceId = document.getElementById('customDeviceId').value.trim().toUpperCase();
            
            if (!deviceId) {
                showResult('customResult', '⚠️ يرجى إدخال معرف الجهاز', 'error');
                return;
            }
            
            if (isValidDeviceId(deviceId)) {
                localStorage.setItem('deviceId', deviceId);
                document.getElementById('currentDeviceId').textContent = deviceId;
                showResult('customResult', `✅ تم تحديث معرف الجهاز الحالي إلى: ${deviceId}`, 'success');
            } else {
                showResult('customResult', '❌ لا يمكن استخدام معرف جهاز غير صالح', 'error');
            }
        }

        // اختبار مثال معرف جهاز
        function testDeviceExample(deviceId) {
            const isValid = isValidDeviceId(deviceId);
            const message = `اختبار ${deviceId}: ${isValid ? '✅ صالح' : '❌ غير صالح'}`;
            showResult('testResults', message, isValid ? 'success' : 'error');
        }

        // استخدام مثال معرف جهاز
        function useDeviceExample(deviceId) {
            localStorage.setItem('deviceId', deviceId);
            document.getElementById('currentDeviceId').textContent = deviceId;
            showResult('testResults', `✅ تم تحديث معرف الجهاز إلى: ${deviceId}`, 'success');
        }

        // توليد معرف جهاز جديد
        function regenerateDeviceId() {
            // استخدام نفس الوظيفة من الملف الأصلي
            const deviceId = generateAdvancedDeviceId();
            localStorage.setItem('deviceId', deviceId);
            document.getElementById('currentDeviceId').textContent = deviceId;
            showResult('testResults', `✅ تم توليد معرف جهاز جديد: ${deviceId}`, 'success');
        }

        // توليد معرف جهاز متقدم
        function generateAdvancedDeviceId() {
            try {
                const platform = navigator.platform || 'UNKNOWN';
                const userAgent = navigator.userAgent || '';
                
                let osType = 'UNK';
                if (platform.includes('Win')) osType = 'WIN';
                else if (platform.includes('Mac')) osType = 'MAC';
                else if (platform.includes('Linux')) osType = 'LNX';
                else if (platform.includes('Android')) osType = 'AND';
                else if (platform.includes('iPhone') || platform.includes('iPad')) osType = 'IOS';
                
                let browserType = 'UNK';
                if (userAgent.includes('Chrome')) browserType = 'CHR';
                else if (userAgent.includes('Firefox')) browserType = 'FFX';
                else if (userAgent.includes('Safari')) browserType = 'SAF';
                else if (userAgent.includes('Edge')) browserType = 'EDG';
                else if (userAgent.includes('Opera')) browserType = 'OPR';
                
                const deviceInfo = `${platform}-${userAgent}-${navigator.language}-${Date.now()}`;
                const hash = btoa(deviceInfo).replace(/[^A-Z0-9]/g, '').substr(0, 8);
                const year = new Date().getFullYear();
                
                return `${osType}-${browserType}-${hash}-${year}`;
                
            } catch (error) {
                const timestamp = Date.now().toString(36);
                const random = Math.random().toString(36).substr(2, 8);
                return `DEV-GEN-${timestamp}-${random}`.toUpperCase();
            }
        }

        // نسخ معرف الجهاز الحالي
        function copyCurrentDeviceId() {
            const deviceId = document.getElementById('currentDeviceId').textContent;
            navigator.clipboard.writeText(deviceId).then(() => {
                showResult('testResults', '✅ تم نسخ معرف الجهاز الحالي', 'success');
            }).catch(() => {
                showResult('testResults', '❌ فشل في نسخ معرف الجهاز', 'error');
            });
        }

        // اختبار معرف الجهاز الحالي
        function testCurrentDeviceId() {
            const deviceId = document.getElementById('currentDeviceId').textContent;
            const isValid = isValidDeviceId(deviceId);
            const message = `اختبار المعرف الحالي: ${isValid ? '✅ صالح' : '❌ غير صالح'}`;
            showResult('testResults', message, isValid ? 'success' : 'error');
        }

        // عرض النتيجة
        function showResult(elementId, message, type) {
            const resultDiv = document.getElementById(elementId);
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = message;
            resultDiv.style.display = 'block';
            
            // إخفاء النتيجة بعد 5 ثوان
            setTimeout(() => {
                resultDiv.style.display = 'none';
            }, 5000);
        }

        // تحديث معرف الجهاز الحالي
        function updateCurrentDeviceId() {
            let deviceId = localStorage.getItem('deviceId');
            if (!deviceId) {
                deviceId = generateAdvancedDeviceId();
                localStorage.setItem('deviceId', deviceId);
            }
            document.getElementById('currentDeviceId').textContent = deviceId;
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            displayDeviceExamples();
            updateCurrentDeviceId();
            
            console.log('🧪 صفحة اختبار معرفات الأجهزة جاهزة');
            console.log('📱 معرفات الأجهزة المدعومة:', deviceExamples.map(d => d.id));
        });
    </script>
</body>
</html>
