<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة العميل - Customer Dashboard</title>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            color: #2c3e50;
        }

        /* الحاوية الرئيسية */
        .main-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        /* الرأس */
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .header-logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #3498db, #2980b9);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 2rem;
            color: white;
            box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3);
        }

        .header-title {
            font-size: 2rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }

        .header-subtitle {
            color: #7f8c8d;
            font-size: 1.1rem;
        }

        /* شبكة البطاقات */
        .cards-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
        }

        .card-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #ecf0f1;
        }

        .card-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }

        .card-icon.license {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }

        .card-icon.device {
            background: linear-gradient(135deg, #27ae60, #229954);
        }

        .card-icon.status {
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }

        .card-icon.support {
            background: linear-gradient(135deg, #9b59b6, #8e44ad);
        }

        .card-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: #2c3e50;
        }

        /* معلومات البطاقة */
        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid #ecf0f1;
        }

        .info-item:last-child {
            border-bottom: none;
        }

        .info-label {
            font-weight: 600;
            color: #7f8c8d;
            font-size: 0.9rem;
        }

        .info-value {
            font-weight: 700;
            color: #2c3e50;
            font-family: 'Courier New', monospace;
        }

        /* حالات الترخيص */
        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-size: 0.8rem;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-active {
            background: rgba(39, 174, 96, 0.1);
            color: #27ae60;
            border: 2px solid #27ae60;
        }

        .status-expired {
            background: rgba(231, 76, 60, 0.1);
            color: #e74c3c;
            border: 2px solid #e74c3c;
        }

        .status-expiring {
            background: rgba(243, 156, 18, 0.1);
            color: #f39c12;
            border: 2px solid #f39c12;
        }

        /* شريط التقدم */
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #ecf0f1;
            border-radius: 4px;
            overflow: hidden;
            margin: 1rem 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #27ae60, #2ecc71);
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .progress-fill.warning {
            background: linear-gradient(90deg, #f39c12, #e67e22);
        }

        .progress-fill.danger {
            background: linear-gradient(90deg, #e74c3c, #c0392b);
        }

        /* الأزرار */
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 10px;
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60, #229954);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .btn-full {
            width: 100%;
            justify-content: center;
        }

        /* قسم الدعم */
        .support-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        /* إشعارات */
        .notification {
            position: fixed;
            top: 20px;
            left: 20px;
            background: white;
            color: #2c3e50;
            border-radius: 12px;
            padding: 1rem 1.5rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            z-index: 10000;
            transform: translateX(-400px);
            opacity: 0;
            transition: all 0.4s ease;
            min-width: 320px;
            border-left: 5px solid #3498db;
        }

        .notification.show {
            transform: translateX(0);
            opacity: 1;
        }

        .notification.success { border-left-color: #27ae60; }
        .notification.error { border-left-color: #e74c3c; }
        .notification.warning { border-left-color: #f39c12; }

        .notification-content {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        /* تحديث الحالة */
        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
            font-weight: 600;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        .status-dot.online {
            background: #27ae60;
        }

        .status-dot.offline {
            background: #95a5a6;
            animation: none;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        /* استجابة للشاشات الصغيرة */
        @media (max-width: 768px) {
            .main-container {
                padding: 1rem;
            }
            
            .cards-grid {
                grid-template-columns: 1fr;
            }
            
            .support-actions {
                grid-template-columns: 1fr;
            }
            
            .header {
                padding: 1.5rem;
            }
            
            .card {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- الرأس -->
        <div class="header">
            <div class="header-logo">
                <i class="fas fa-user-circle"></i>
            </div>
            <h1 class="header-title">لوحة العميل</h1>
            <p class="header-subtitle">Customer Dashboard - مرحباً بك في نظام إدارة مؤسسة وقود المستقبل</p>
        </div>

        <!-- شبكة البطاقات -->
        <div class="cards-grid">
            <!-- بطاقة معلومات الترخيص -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon license">
                        <i class="fas fa-certificate"></i>
                    </div>
                    <div class="card-title">معلومات الترخيص</div>
                </div>

                <div class="info-item">
                    <span class="info-label">كود الترخيص:</span>
                    <span class="info-value" id="licenseCode">جاري التحميل...</span>
                </div>

                <div class="info-item">
                    <span class="info-label">نوع الترخيص:</span>
                    <span class="info-value" id="licenseType">جاري التحميل...</span>
                </div>

                <div class="info-item">
                    <span class="info-label">تاريخ التفعيل:</span>
                    <span class="info-value" id="activationDate">جاري التحميل...</span>
                </div>

                <div class="info-item">
                    <span class="info-label">تاريخ الانتهاء:</span>
                    <span class="info-value" id="expiryDate">جاري التحميل...</span>
                </div>

                <div class="info-item">
                    <span class="info-label">الحالة:</span>
                    <span class="status-badge" id="licenseStatus">جاري التحميل...</span>
                </div>

                <div class="progress-bar">
                    <div class="progress-fill" id="licenseProgress"></div>
                </div>

                <div style="text-align: center; margin-top: 1rem; font-size: 0.9rem; color: #7f8c8d;">
                    <span id="daysRemaining">جاري حساب الأيام المتبقية...</span>
                </div>
            </div>

            <!-- بطاقة معلومات الجهاز -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon device">
                        <i class="fas fa-desktop"></i>
                    </div>
                    <div class="card-title">معلومات الجهاز</div>
                </div>

                <div class="info-item">
                    <span class="info-label">معرف الجهاز:</span>
                    <span class="info-value" id="deviceId">جاري التحميل...</span>
                </div>

                <div class="info-item">
                    <span class="info-label">حالة الاتصال:</span>
                    <div class="status-indicator" id="connectionStatus">
                        <div class="status-dot online"></div>
                        <span>متصل</span>
                    </div>
                </div>

                <div class="info-item">
                    <span class="info-label">آخر نشاط:</span>
                    <span class="info-value" id="lastActivity">الآن</span>
                </div>

                <div class="info-item">
                    <span class="info-label">نظام التشغيل:</span>
                    <span class="info-value" id="operatingSystem">Windows 11</span>
                </div>

                <div class="info-item">
                    <span class="info-label">المتصفح:</span>
                    <span class="info-value" id="browserInfo">جاري التحديد...</span>
                </div>

                <button class="btn btn-primary btn-full" onclick="refreshDeviceInfo()">
                    <i class="fas fa-sync"></i>
                    تحديث معلومات الجهاز
                </button>
            </div>

            <!-- بطاقة حالة النظام -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon status">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="card-title">حالة النظام</div>
                </div>

                <div class="info-item">
                    <span class="info-label">حالة الخدمة:</span>
                    <span class="status-badge status-active" id="serviceStatus">نشط</span>
                </div>

                <div class="info-item">
                    <span class="info-label">إصدار النظام:</span>
                    <span class="info-value">2.2.0</span>
                </div>

                <div class="info-item">
                    <span class="info-label">آخر تحديث:</span>
                    <span class="info-value" id="lastUpdate">اليوم</span>
                </div>

                <div class="info-item">
                    <span class="info-label">مدة الاستخدام:</span>
                    <span class="info-value" id="usageTime">جاري الحساب...</span>
                </div>

                <button class="btn btn-success btn-full" onclick="checkForUpdates()">
                    <i class="fas fa-download"></i>
                    فحص التحديثات
                </button>
            </div>

            <!-- بطاقة الدعم والمساعدة -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon support">
                        <i class="fas fa-headset"></i>
                    </div>
                    <div class="card-title">الدعم والمساعدة</div>
                </div>

                <div class="info-item">
                    <span class="info-label">حالة الدعم:</span>
                    <span class="status-badge status-active">متاح 24/7</span>
                </div>

                <div class="info-item">
                    <span class="info-label">رقم العميل:</span>
                    <span class="info-value" id="customerNumber">جاري التحميل...</span>
                </div>

                <div class="support-actions">
                    <button class="btn btn-primary" onclick="contactSupport()">
                        <i class="fas fa-phone"></i>
                        اتصل بالدعم
                    </button>

                    <button class="btn btn-warning" onclick="reportIssue()">
                        <i class="fas fa-exclamation-triangle"></i>
                        بلاغ مشكلة
                    </button>

                    <button class="btn btn-success" onclick="requestRenewal()">
                        <i class="fas fa-redo"></i>
                        طلب تجديد
                    </button>

                    <button class="btn btn-danger" onclick="emergencySupport()">
                        <i class="fas fa-ambulance"></i>
                        دعم طارئ
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // بيانات العميل
        let customerData = {
            session: null,
            license: null,
            device: null
        };

        // تهيئة النظام
        document.addEventListener('DOMContentLoaded', function() {
            loadCustomerData();
            updateDeviceInfo();
            startPeriodicUpdates();

            // رسالة ترحيب
            setTimeout(() => {
                showNotification('مرحباً بك في لوحة العميل', 'success');
            }, 1000);
        });

        // تحميل بيانات العميل
        function loadCustomerData() {
            // تحميل بيانات الجلسة
            const sessionData = localStorage.getItem('currentSession');
            if (sessionData) {
                customerData.session = JSON.parse(sessionData);
                loadLicenseInfo();
            } else {
                showNotification('لم يتم العثور على بيانات الجلسة', 'error');
                setTimeout(() => {
                    window.location.href = 'index.html';
                }, 3000);
                return;
            }

            // تحميل معلومات الجهاز
            customerData.device = {
                id: customerData.session.deviceId,
                os: getOperatingSystem(),
                browser: getBrowserInfo(),
                lastActivity: new Date().toISOString()
            };

            updateDisplay();
        }

        // تحميل معلومات الترخيص
        function loadLicenseInfo() {
            const licenses = JSON.parse(localStorage.getItem('validLicenses') || '[]');
            customerData.license = licenses.find(l => l.code === customerData.session.licenseCode);

            if (!customerData.license) {
                showNotification('لم يتم العثور على معلومات الترخيص', 'error');
                return;
            }
        }

        // تحديث العرض
        function updateDisplay() {
            if (!customerData.license || !customerData.session) return;

            // معلومات الترخيص
            document.getElementById('licenseCode').textContent = customerData.license.code;
            document.getElementById('licenseType').textContent = getLicenseTypeName(customerData.license.type);
            document.getElementById('activationDate').textContent = formatDate(customerData.license.activatedAt || customerData.session.loginTime);
            document.getElementById('expiryDate').textContent = formatDate(customerData.license.expiresAt);

            // حساب الأيام المتبقية والحالة
            const now = new Date();
            const expiryDate = new Date(customerData.license.expiresAt);
            const daysRemaining = Math.ceil((expiryDate - now) / (1000 * 60 * 60 * 24));

            updateLicenseStatus(daysRemaining);

            // معلومات الجهاز
            document.getElementById('deviceId').textContent = customerData.device.id;
            document.getElementById('operatingSystem').textContent = customerData.device.os;
            document.getElementById('browserInfo').textContent = customerData.device.browser;
            document.getElementById('lastActivity').textContent = formatDateTime(customerData.device.lastActivity);

            // رقم العميل (مولد من معرف الجهاز)
            const customerNumber = generateCustomerNumber(customerData.device.id);
            document.getElementById('customerNumber').textContent = customerNumber;

            // مدة الاستخدام
            const usageTime = calculateUsageTime();
            document.getElementById('usageTime').textContent = usageTime;

            // آخر تحديث
            document.getElementById('lastUpdate').textContent = formatDate(new Date().toISOString());
        }

        // تحديث حالة الترخيص
        function updateLicenseStatus(daysRemaining) {
            const statusElement = document.getElementById('licenseStatus');
            const progressElement = document.getElementById('licenseProgress');
            const daysElement = document.getElementById('daysRemaining');

            let status, statusClass, progressClass, progressWidth;

            if (daysRemaining <= 0) {
                status = 'منتهي الصلاحية';
                statusClass = 'status-expired';
                progressClass = 'danger';
                progressWidth = 0;
            } else if (daysRemaining <= 7) {
                status = 'ينتهي قريباً';
                statusClass = 'status-expiring';
                progressClass = 'danger';
                progressWidth = (daysRemaining / 7) * 100;
            } else if (daysRemaining <= 30) {
                status = 'نشط - تحذير';
                statusClass = 'status-expiring';
                progressClass = 'warning';
                progressWidth = (daysRemaining / 365) * 100;
            } else {
                status = 'نشط';
                statusClass = 'status-active';
                progressClass = '';
                progressWidth = (daysRemaining / 365) * 100;
            }

            statusElement.textContent = status;
            statusElement.className = `status-badge ${statusClass}`;

            progressElement.className = `progress-fill ${progressClass}`;
            progressElement.style.width = `${Math.min(progressWidth, 100)}%`;

            if (daysRemaining > 0) {
                daysElement.textContent = `${daysRemaining} يوم متبقي`;
            } else {
                daysElement.textContent = 'انتهت صلاحية الترخيص';
            }
        }

        // تحديث معلومات الجهاز
        function updateDeviceInfo() {
            customerData.device.lastActivity = new Date().toISOString();
            document.getElementById('lastActivity').textContent = formatDateTime(customerData.device.lastActivity);

            // محاكاة حالة الاتصال
            const isOnline = navigator.onLine;
            const statusElement = document.getElementById('connectionStatus');

            if (isOnline) {
                statusElement.innerHTML = `
                    <div class="status-dot online"></div>
                    <span>متصل</span>
                `;
            } else {
                statusElement.innerHTML = `
                    <div class="status-dot offline"></div>
                    <span>غير متصل</span>
                `;
            }
        }

        // بدء التحديثات الدورية
        function startPeriodicUpdates() {
            // تحديث كل 30 ثانية
            setInterval(() => {
                updateDeviceInfo();
                updateDisplay();
            }, 30000);

            // تحديث حالة الاتصال
            window.addEventListener('online', updateDeviceInfo);
            window.addEventListener('offline', updateDeviceInfo);
        }

        // وظائف الدعم
        function contactSupport() {
            const customerNumber = document.getElementById('customerNumber').textContent;
            const licenseCode = customerData.license.code;

            const message = `
مرحباً، أحتاج للمساعدة:

رقم العميل: ${customerNumber}
كود الترخيص: ${licenseCode}
معرف الجهاز: ${customerData.device.id}

يرجى التواصل معي في أقرب وقت ممكن.
            `;

            // محاكاة فتح تطبيق البريد الإلكتروني
            const mailtoLink = `mailto:<EMAIL>?subject=طلب دعم - ${customerNumber}&body=${encodeURIComponent(message)}`;
            window.open(mailtoLink);

            showNotification('تم فتح تطبيق البريد الإلكتروني', 'info');
        }

        function reportIssue() {
            const issue = prompt('يرجى وصف المشكلة التي تواجهها:');
            if (issue) {
                // محاكاة إرسال البلاغ
                showNotification('تم إرسال البلاغ بنجاح. سيتم التواصل معك قريباً.', 'success');

                // حفظ البلاغ محلياً
                const reports = JSON.parse(localStorage.getItem('customerReports') || '[]');
                reports.push({
                    id: Date.now(),
                    customerNumber: document.getElementById('customerNumber').textContent,
                    licenseCode: customerData.license.code,
                    deviceId: customerData.device.id,
                    issue: issue,
                    timestamp: new Date().toISOString(),
                    status: 'pending'
                });
                localStorage.setItem('customerReports', JSON.stringify(reports));
            }
        }

        function requestRenewal() {
            const confirmRenewal = confirm('هل تريد طلب تجديد الترخيص؟ سيتم التواصل معك لإتمام عملية التجديد.');
            if (confirmRenewal) {
                showNotification('تم إرسال طلب التجديد. سيتم التواصل معك قريباً.', 'success');

                // حفظ طلب التجديد
                const renewalRequests = JSON.parse(localStorage.getItem('renewalRequests') || '[]');
                renewalRequests.push({
                    customerNumber: document.getElementById('customerNumber').textContent,
                    licenseCode: customerData.license.code,
                    deviceId: customerData.device.id,
                    requestDate: new Date().toISOString(),
                    status: 'pending'
                });
                localStorage.setItem('renewalRequests', JSON.stringify(renewalRequests));
            }
        }

        function emergencySupport() {
            const emergency = confirm('هل هذا طلب دعم طارئ؟ سيتم إعطاؤه أولوية قصوى.');
            if (emergency) {
                showNotification('تم إرسال طلب الدعم الطارئ. سيتم التواصل معك فوراً.', 'warning');

                // محاكاة اتصال طارئ
                setTimeout(() => {
                    alert('جاري الاتصال بفريق الدعم الطارئ...\nرقم الطوارئ: +966-11-123-4567');
                }, 2000);
            }
        }

        function refreshDeviceInfo() {
            showNotification('جاري تحديث معلومات الجهاز...', 'info');

            setTimeout(() => {
                updateDeviceInfo();
                customerData.device.browser = getBrowserInfo();
                customerData.device.os = getOperatingSystem();

                document.getElementById('browserInfo').textContent = customerData.device.browser;
                document.getElementById('operatingSystem').textContent = customerData.device.os;

                showNotification('تم تحديث معلومات الجهاز بنجاح', 'success');
            }, 1500);
        }

        function checkForUpdates() {
            showNotification('جاري فحص التحديثات...', 'info');

            setTimeout(() => {
                const hasUpdate = Math.random() > 0.7; // 30% احتمال وجود تحديث

                if (hasUpdate) {
                    const confirmUpdate = confirm('يتوفر تحديث جديد للنظام. هل تريد تحميله الآن؟');
                    if (confirmUpdate) {
                        showNotification('جاري تحميل التحديث...', 'warning');
                        setTimeout(() => {
                            showNotification('تم تحميل التحديث بنجاح. سيتم تطبيقه عند إعادة التشغيل.', 'success');
                        }, 3000);
                    }
                } else {
                    showNotification('النظام محدث إلى أحدث إصدار', 'success');
                }
            }, 2000);
        }

        // وظائف مساعدة
        function getLicenseTypeName(type) {
            const names = {
                'demo': 'تجريبي',
                'monthly': 'شهري',
                'yearly': 'سنوي',
                'lifetime': 'مدى الحياة'
            };
            return names[type] || type;
        }

        function formatDate(dateString) {
            if (!dateString) return 'غير محدد';
            return new Date(dateString).toLocaleDateString('ar-SA');
        }

        function formatDateTime(dateString) {
            if (!dateString) return 'غير محدد';
            return new Date(dateString).toLocaleString('ar-SA');
        }

        function getOperatingSystem() {
            const userAgent = navigator.userAgent;
            if (userAgent.includes('Windows NT 10.0')) return 'Windows 10/11';
            if (userAgent.includes('Windows NT 6.3')) return 'Windows 8.1';
            if (userAgent.includes('Windows NT 6.2')) return 'Windows 8';
            if (userAgent.includes('Windows NT 6.1')) return 'Windows 7';
            if (userAgent.includes('Mac OS X')) return 'macOS';
            if (userAgent.includes('Linux')) return 'Linux';
            if (userAgent.includes('Android')) return 'Android';
            if (userAgent.includes('iPhone')) return 'iOS';
            return 'غير معروف';
        }

        function getBrowserInfo() {
            const userAgent = navigator.userAgent;
            if (userAgent.includes('Chrome') && !userAgent.includes('Edg')) return 'Google Chrome';
            if (userAgent.includes('Firefox')) return 'Mozilla Firefox';
            if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) return 'Safari';
            if (userAgent.includes('Edg')) return 'Microsoft Edge';
            if (userAgent.includes('Opera')) return 'Opera';
            return 'غير معروف';
        }

        function generateCustomerNumber(deviceId) {
            // توليد رقم عميل من معرف الجهاز
            const hash = deviceId.split('-').join('').slice(0, 8).toUpperCase();
            return `CUS-${hash}`;
        }

        function calculateUsageTime() {
            if (!customerData.session) return 'غير محدد';

            const loginTime = new Date(customerData.session.loginTime);
            const now = new Date();
            const diffMs = now - loginTime;

            const hours = Math.floor(diffMs / (1000 * 60 * 60));
            const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

            if (hours > 0) {
                return `${hours} ساعة و ${minutes} دقيقة`;
            } else {
                return `${minutes} دقيقة`;
            }
        }

        // نظام الإشعارات
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.innerHTML = `
                <div class="notification-content">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
                    <span>${message}</span>
                </div>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.classList.add('show');
            }, 100);

            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    if (notification.parentElement) {
                        notification.remove();
                    }
                }, 400);
            }, 4000);
        }
    </script>
</body>
</html>
