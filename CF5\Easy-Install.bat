@echo off
chcp 65001 >nul
title تثبيت سريع - مؤسسة وقود المستقبل

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                                                                              ║
echo ║                    🚀 تثبيت سريع - مؤسسة وقود المستقبل                    ║
echo ║                    Quick Install - Future Fuel Corporation                   ║
echo ║                                                                              ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.
echo                              الإصدار: 2.2.0
echo.
echo ┌──────────────────────────────────────────────────────────────────────────────┐
echo │  هذا البرنامج سيقوم بتثبيت التطبيق تلقائياً على جهازكم                    │
echo │  لن تحتاجوا لأي إعدادات معقدة - فقط انقروا "موافق"                       │
echo └──────────────────────────────────────────────────────────────────────────────┘
echo.
echo ⚠️  تأكد من إغلاق جميع البرامج الأخرى قبل المتابعة
echo.
echo اضغط أي زر للبدء أو أغلق النافذة للإلغاء...
pause >nul

echo.
echo ┌──────────────────────────────────────────────────────────────────────────────┐
echo │                              🔄 جاري التثبيت...                             │
echo └──────────────────────────────────────────────────────────────────────────────┘
echo.

:: تحديد مجلد التثبيت
set INSTALL_DIR=%USERPROFILE%\Desktop\مؤسسة وقود المستقبل

echo ⏳ إنشاء مجلد التطبيق...
if not exist "%INSTALL_DIR%" (
    mkdir "%INSTALL_DIR%" 2>nul
)

if not exist "%INSTALL_DIR%" (
    echo ❌ فشل في إنشاء مجلد التطبيق
    echo    جرب تشغيل البرنامج كمدير (انقر بالزر الأيمن > تشغيل كمدير)
    echo.
    pause
    exit /b 1
)
echo ✅ تم إنشاء مجلد التطبيق

echo.
echo ⏳ نسخ ملفات التطبيق...
xcopy /E /I /Y "customer-package\*" "%INSTALL_DIR%\" >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ فشل في نسخ ملفات التطبيق
    echo    تأكد من وجود مجلد customer-package في نفس مكان ملف التثبيت
    echo.
    pause
    exit /b 1
)
echo ✅ تم نسخ ملفات التطبيق

echo.
echo ⏳ نسخ الملفات المساعدة...
if exist "shared" (
    xcopy /E /I /Y "shared\*" "%INSTALL_DIR%\shared\" >nul 2>&1
    echo ✅ تم نسخ الملفات المساعدة
) else (
    echo ⚠️  الملفات المساعدة غير موجودة (اختياري)
)

echo.
echo ⏳ إنشاء اختصار على سطح المكتب...
powershell -Command "
try {
    $WshShell = New-Object -comObject WScript.Shell
    $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\مؤسسة وقود المستقبل.lnk')
    $Shortcut.TargetPath = '%INSTALL_DIR%\app\index.html'
    $Shortcut.WorkingDirectory = '%INSTALL_DIR%\app'
    $Shortcut.Description = 'مؤسسة وقود المستقبل - نظام إدارة محطات الوقود'
    $Shortcut.Save()
    exit 0
} catch {
    exit 1
}
" >nul 2>&1

if %errorlevel% equ 0 (
    echo ✅ تم إنشاء اختصار سطح المكتب
) else (
    echo ⚠️  لم يتم إنشاء اختصار سطح المكتب (يمكنك فتح التطبيق من المجلد مباشرة)
)

echo.
echo ⏳ إنشاء ملف إلغاء التثبيت...
(
echo @echo off
echo title إلغاء تثبيت مؤسسة وقود المستقبل
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                        إلغاء تثبيت مؤسسة وقود المستقبل                    ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.
echo هل أنت متأكد من إلغاء تثبيت البرنامج؟
echo.
echo سيتم حذف:
echo   • جميع ملفات البرنامج
echo   • اختصار سطح المكتب
echo   • جميع الإعدادات المحفوظة
echo.
echo ⚠️  تأكد من عمل نسخة احتياطية من بياناتك المهمة
echo.
set /p confirm="اكتب 'نعم' للتأكيد: "
if /i "%%confirm%%"=="نعم" (
    echo.
    echo ⏳ جاري إلغاء التثبيت...
    echo.
    echo حذف ملفات البرنامج...
    rmdir /s /q "%INSTALL_DIR%" 2^>nul
    echo.
    echo حذف اختصار سطح المكتب...
    del "%USERPROFILE%\Desktop\مؤسسة وقود المستقبل.lnk" 2^>nul
    echo.
    echo ✅ تم إلغاء التثبيت بنجاح
    echo.
    echo شكراً لاستخدامكم مؤسسة وقود المستقبل
) else (
    echo.
    echo تم إلغاء عملية إلغاء التثبيت
)
echo.
pause
) > "%INSTALL_DIR%\إلغاء التثبيت.bat"
echo ✅ تم إنشاء ملف إلغاء التثبيت

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                            ✅ تم التثبيت بنجاح!                            ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.
echo 🎉 تهانينا! تم تثبيت مؤسسة وقود المستقبل بنجاح على جهازكم
echo.
echo ┌──────────────────────────────────────────────────────────────────────────────┐
echo │                              📍 معلومات التثبيت                             │
echo ├──────────────────────────────────────────────────────────────────────────────┤
echo │  📁 مجلد البرنامج: %INSTALL_DIR%
echo │  🖥️  اختصار سطح المكتب: مؤسسة وقود المستقبل                              │
echo │  🗑️  إلغاء التثبيت: إلغاء التثبيت.bat (في مجلد البرنامج)                │
echo └──────────────────────────────────────────────────────────────────────────────┘
echo.
echo ┌──────────────────────────────────────────────────────────────────────────────┐
echo │                              🚀 كيفية الاستخدام                             │
echo ├──────────────────────────────────────────────────────────────────────────────┤
echo │  1️⃣  انقر نقراً مزدوجاً على اختصار "مؤسسة وقود المستقبل" على سطح المكتب  │
echo │  2️⃣  أدخل كود الترخيص الخاص بك                                            │
echo │  3️⃣  انقر على "تسجيل الدخول"                                              │
echo │  4️⃣  ابدأ استخدام النظام                                                   │
echo └──────────────────────────────────────────────────────────────────────────────┘
echo.
echo ┌──────────────────────────────────────────────────────────────────────────────┐
echo │                              🔑 كود الترخيص                                 │
echo ├──────────────────────────────────────────────────────────────────────────────┤
echo │  إذا لم تحصل على كود الترخيص بعد، تواصل معنا:                            │
echo │                                                                              │
echo │  📧 البريد الإلكتروني: <EMAIL>                              │
echo │  📱 الهاتف: +966-11-123-4567                                                │
echo │  💬 واتساب: +966-50-123-4567                                                │
echo │  🌐 الموقع: www.futurefuel.com                                              │
echo └──────────────────────────────────────────────────────────────────────────────┘
echo.
echo ┌──────────────────────────────────────────────────────────────────────────────┐
echo │                              ⚠️  ملاحظات مهمة                               │
echo ├──────────────────────────────────────────────────────────────────────────────┤
echo │  • احتفظ بكود الترخيص في مكان آمن                                         │
echo │  • لا تشارك كود الترخيص مع أشخاص آخرين                                   │
echo │  • تأكد من عمل نسخة احتياطية من بياناتك بانتظام                           │
echo │  • للحصول على الدعم الفني، تواصل معنا على المعلومات أعلاه                │
echo └──────────────────────────────────────────────────────────────────────────────┘
echo.

:: سؤال المستخدم إذا كان يريد تشغيل البرنامج الآن
set /p launch="🚀 هل تريد تشغيل البرنامج الآن؟ (اكتب 'نعم' أو اضغط Enter للخروج): "
if /i "%launch%"=="نعم" (
    echo.
    echo ⏳ جاري تشغيل البرنامج...
    
    :: محاولة فتح البرنامج
    if exist "%INSTALL_DIR%\app\index.html" (
        start "" "%INSTALL_DIR%\app\index.html"
        echo ✅ تم تشغيل البرنامج بنجاح!
        echo.
        echo يمكنك الآن إغلاق هذه النافذة والبدء في استخدام النظام
    ) else (
        echo ❌ لم يتم العثور على ملف البرنامج
        echo    تأكد من وجود الملف: %INSTALL_DIR%\app\index.html
    )
) else (
    echo.
    echo يمكنك تشغيل البرنامج لاحقاً من اختصار سطح المكتب
    echo أو من مجلد البرنامج: %INSTALL_DIR%\app\index.html
)

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                    شكراً لاختياركم مؤسسة وقود المستقبل!                   ║
echo ║                    Thank you for choosing Future Fuel!                      ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.
pause
