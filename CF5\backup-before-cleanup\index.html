<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام إدارة مؤسسة وقود المستقبل</title>
    <link rel="icon" type="image/x-icon" href="assets/favicon.ico">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            direction: rtl;
            overflow: hidden;
            position: relative;
        }

        /* خلفية متحركة */
        .background-animation {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 0;
        }

        .floating-shapes {
            position: absolute;
            width: 100%;
            height: 100%;
        }

        .shape {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        .shape:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .shape:nth-child(2) {
            width: 120px;
            height: 120px;
            top: 60%;
            left: 80%;
            animation-delay: 2s;
        }

        .shape:nth-child(3) {
            width: 60px;
            height: 60px;
            top: 80%;
            left: 20%;
            animation-delay: 4s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        /* الحاوية الرئيسية */
        .login-container {
            position: relative;
            z-index: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            padding: 2rem;
        }

        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
            overflow: hidden;
            width: 100%;
            max-width: 500px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        /* رأس البطاقة */
        .login-header {
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            color: white;
            padding: 2.5rem;
            text-align: center;
            position: relative;
        }

        .login-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .login-logo {
            position: relative;
            z-index: 1;
            width: 90px;
            height: 90px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            font-size: 2.5rem;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
        }

        .login-title {
            position: relative;
            z-index: 1;
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .login-subtitle {
            position: relative;
            z-index: 1;
            font-size: 1.1rem;
            opacity: 0.9;
        }

        /* محتوى النموذج */
        .login-body {
            padding: 3rem;
        }

        .form-group {
            margin-bottom: 2rem;
            position: relative;
        }

        .form-label {
            display: block;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 0.75rem;
            font-size: 1.1rem;
        }

        .form-input-container {
            position: relative;
        }

        .form-input {
            width: 100%;
            padding: 1.25rem 1.25rem 1.25rem 3.5rem;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            background: #f8f9fa;
            font-family: 'Courier New', monospace;
        }

        .form-input:focus {
            outline: none;
            border-color: #3498db;
            background: white;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        .form-icon {
            position: absolute;
            left: 1.25rem;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
            font-size: 1.3rem;
        }

        .form-input:focus + .form-icon {
            color: #3498db;
        }

        /* معرف الجهاز */
        .device-info {
            background: #e8f4fd;
            border-radius: 15px;
            padding: 1.5rem;
            border-left: 4px solid #3498db;
            margin-bottom: 2rem;
        }

        .device-label {
            font-size: 0.9rem;
            color: #6c757d;
            margin-bottom: 0.5rem;
            font-weight: 600;
        }

        .device-value {
            font-family: 'Courier New', monospace;
            font-size: 1rem;
            color: #2c3e50;
            font-weight: 600;
            word-break: break-all;
            cursor: pointer;
            padding: 0.5rem;
            background: white;
            border-radius: 8px;
            border: 1px solid #ddd;
            transition: all 0.3s ease;
        }

        .device-value:hover {
            background: #f8f9fa;
            border-color: #3498db;
        }

        /* زر تسجيل الدخول */
        .login-btn {
            width: 100%;
            padding: 1.5rem;
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            border: none;
            border-radius: 15px;
            font-size: 1.2rem;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            position: relative;
            overflow: hidden;
        }

        .login-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .login-btn:hover::before {
            left: 100%;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(52, 152, 219, 0.3);
        }

        .login-btn:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
            transform: none;
        }

        /* تأثيرات التحميل */
        .loading-spinner {
            display: none;
            width: 20px;
            height: 20px;
            border: 2px solid #ffffff;
            border-top: 2px solid transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* معلومات إضافية */
        .info-section {
            background: #f8f9fa;
            padding: 1.5rem;
            text-align: center;
            border-top: 1px solid #e9ecef;
        }

        .info-text {
            color: #6c757d;
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }

        .version-info {
            color: #495057;
            font-size: 0.8rem;
            font-weight: 600;
        }

        /* إشعارات */
        .notification {
            position: fixed;
            top: 20px;
            left: 20px;
            background: white;
            border-radius: 12px;
            padding: 1rem 1.5rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            z-index: 10000;
            transform: translateX(-400px);
            opacity: 0;
            transition: all 0.4s ease;
            min-width: 320px;
            border-left: 5px solid #3498db;
        }

        .notification.show {
            transform: translateX(0);
            opacity: 1;
        }

        .notification.success { border-left-color: #27ae60; }
        .notification.error { border-left-color: #e74c3c; }
        .notification.warning { border-left-color: #f39c12; }

        .notification-content {
            display: flex;
            align-items: center;
            gap: 1rem;
            color: #2c3e50;
        }

        /* استجابة للشاشات الصغيرة */
        @media (max-width: 600px) {
            .login-container {
                padding: 1rem;
            }

            .login-card {
                max-width: 100%;
            }

            .login-header {
                padding: 2rem;
            }

            .login-body {
                padding: 2rem;
            }
        }
    </style>
</head>
<body>
    <!-- خلفية متحركة -->
    <div class="background-animation">
        <div class="floating-shapes">
            <div class="shape"></div>
            <div class="shape"></div>
            <div class="shape"></div>
        </div>
    </div>

    <!-- الحاوية الرئيسية -->
    <div class="login-container">
        <div class="login-card">
            <!-- رأس البطاقة -->
            <div class="login-header">
                <div class="login-logo">
                    <i class="fas fa-gas-pump"></i>
                </div>
                <h1 class="login-title">نظام إدارة مؤسسة وقود المستقبل</h1>
                <p class="login-subtitle">Future Fuel Corporation Management System</p>
            </div>

            <!-- محتوى النموذج -->
            <div class="login-body">
                <form id="loginForm">
                    <div class="form-group">
                        <label class="form-label">كود الترخيص</label>
                        <div class="form-input-container">
                            <input type="text" id="licenseCode" class="form-input" placeholder="أدخل كود الترخيص (مثال: XXXX-XXXX-XXXX-XXXX)" required>
                            <i class="fas fa-key form-icon"></i>
                        </div>
                    </div>

                    <!-- معرف الجهاز -->
                    <div class="device-info">
                        <div class="device-label">معرف الجهاز</div>
                        <div class="device-value" id="deviceId" title="انقر للنسخ">جاري التحميل...</div>
                    </div>

                    <button type="submit" class="login-btn">
                        <i class="fas fa-sign-in-alt"></i>
                        تسجيل الدخول
                        <div class="loading-spinner" id="loginSpinner"></div>
                    </button>
                </form>
            </div>

            <!-- معلومات إضافية -->
            <div class="info-section">
                <div class="info-text">
                    <i class="fas fa-info-circle"></i>
                    يرجى إدخال كود الترخيص الصحيح للوصول إلى النظام
                </div>

                <!-- روابط سريعة -->
                <div style="margin: 1rem 0; display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
                    <button onclick="openDeveloperPanel()" style="background: #e74c3c; color: white; border: none; padding: 0.5rem 1rem; border-radius: 8px; cursor: pointer; font-size: 0.8rem; transition: all 0.3s ease;">
                        <i class="fas fa-code"></i> لوحة المطور
                    </button>
                    <button onclick="showLicenseHelp()" style="background: #f39c12; color: white; border: none; padding: 0.5rem 1rem; border-radius: 8px; cursor: pointer; font-size: 0.8rem; transition: all 0.3s ease;">
                        <i class="fas fa-question-circle"></i> مساعدة
                    </button>
                    <button onclick="showSystemInfo()" style="background: #27ae60; color: white; border: none; padding: 0.5rem 1rem; border-radius: 8px; cursor: pointer; font-size: 0.8rem; transition: all 0.3s ease;">
                        <i class="fas fa-info"></i> معلومات النظام
                    </button>
                </div>

                <div class="version-info">
                    الإصدار 2.2.0 - جميع الحقوق محفوظة
                </div>
            </div>
        </div>
    </div>

    <script>
        // توليد معرف الجهاز
        function generateDeviceId() {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            ctx.textBaseline = 'top';
            ctx.font = '14px Arial';
            ctx.fillText('Device fingerprint', 2, 2);

            const fingerprint = canvas.toDataURL();
            const hash = btoa(fingerprint).slice(0, 16);

            const timestamp = Date.now().toString(36);
            const random = Math.random().toString(36).substr(2, 5);

            return `DEV-${hash}-${timestamp}-${random}`.toUpperCase();
        }

        // تحديث معرف الجهاز
        function updateDeviceId() {
            let deviceId = localStorage.getItem('deviceId');
            if (!deviceId) {
                deviceId = generateDeviceId();
                localStorage.setItem('deviceId', deviceId);
            }
            document.getElementById('deviceId').textContent = deviceId;
        }

        // نسخ معرف الجهاز
        function copyDeviceId() {
            const deviceId = document.getElementById('deviceId').textContent;
            navigator.clipboard.writeText(deviceId).then(() => {
                showNotification('تم نسخ معرف الجهاز', 'success');
            }).catch(() => {
                // طريقة بديلة للنسخ
                const textArea = document.createElement('textarea');
                textArea.value = deviceId;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showNotification('تم نسخ معرف الجهاز', 'success');
            });
        }

        // قاعدة بيانات التراخيص الصالحة
        function getValidLicenses() {
            const storedLicenses = localStorage.getItem('validLicenses');
            if (storedLicenses) {
                return JSON.parse(storedLicenses);
            }

            // تراخيص افتراضية للاختبار
            const defaultLicenses = [
                {
                    code: 'DEMO-2024-TEST-0001',
                    type: 'demo',
                    expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 يوم
                    isActive: true,
                    deviceId: null,
                    activatedAt: null
                },
                {
                    code: 'FULL-2024-PROD-0001',
                    type: 'full',
                    expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(), // سنة
                    isActive: true,
                    deviceId: null,
                    activatedAt: null
                }
            ];

            localStorage.setItem('validLicenses', JSON.stringify(defaultLicenses));
            return defaultLicenses;
        }

        // التحقق من صحة الترخيص
        function validateLicense(licenseCode, deviceId) {
            console.log('التحقق من الترخيص:', licenseCode);

            const licenses = getValidLicenses();
            console.log('التراخيص المتاحة:', licenses);

            const license = licenses.find(l => l.code === licenseCode);
            console.log('الترخيص الموجود:', license);

            if (!license) {
                console.log('الترخيص غير موجود');
                return { valid: false, message: 'كود الترخيص غير صحيح' };
            }

            if (!license.isActive) {
                console.log('الترخيص غير نشط');
                return { valid: false, message: 'هذا الترخيص معطل' };
            }

            const now = new Date();
            const expiryDate = new Date(license.expiresAt);
            console.log('تاريخ الانتهاء:', expiryDate, 'التاريخ الحالي:', now);

            if (now > expiryDate) {
                console.log('الترخيص منتهي الصلاحية');
                return { valid: false, message: 'انتهت صلاحية هذا الترخيص' };
            }

            // التحقق من ربط الجهاز
            if (license.deviceId && license.deviceId !== deviceId) {
                console.log('الترخيص مرتبط بجهاز آخر');
                return { valid: false, message: 'هذا الترخيص مرتبط بجهاز آخر' };
            }

            console.log('الترخيص صالح');
            return {
                valid: true,
                license: license,
                message: 'تم التحقق من الترخيص بنجاح'
            };
        }

        // ربط الترخيص بالجهاز
        function bindLicenseToDevice(licenseCode, deviceId) {
            const licenses = getValidLicenses();
            const licenseIndex = licenses.findIndex(l => l.code === licenseCode);

            if (licenseIndex !== -1) {
                licenses[licenseIndex].deviceId = deviceId;
                licenses[licenseIndex].activatedAt = new Date().toISOString();
                localStorage.setItem('validLicenses', JSON.stringify(licenses));
            }
        }

        // معالجة نموذج تسجيل الدخول
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const licenseCode = document.getElementById('licenseCode').value.trim();
            const deviceId = document.getElementById('deviceId').textContent;

            if (!licenseCode) {
                showNotification('يرجى إدخال كود الترخيص', 'warning');
                return;
            }

            // التحقق من تنسيق كود الترخيص
            const licensePattern = /^[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$/;
            if (!licensePattern.test(licenseCode)) {
                showNotification('تنسيق كود الترخيص غير صحيح. يجب أن يكون بالشكل: XXXX-XXXX-XXXX-XXXX', 'error');
                return;
            }

            // إظهار مؤشر التحميل
            const spinner = document.getElementById('loginSpinner');
            const loginBtn = document.querySelector('.login-btn');

            spinner.style.display = 'inline-block';
            loginBtn.disabled = true;

            try {
                console.log('بدء التحقق من الترخيص...');
                console.log('كود الترخيص المدخل:', licenseCode);
                console.log('معرف الجهاز:', deviceId);

                // محاكاة تأخير الشبكة
                await new Promise(resolve => setTimeout(resolve, 1500));

                // التحقق من صحة الترخيص
                const validation = validateLicense(licenseCode, deviceId);
                console.log('نتيجة التحقق:', validation);

                if (!validation.valid) {
                    console.log('فشل التحقق:', validation.message);
                    showNotification(validation.message, 'error');
                    return;
                }

                // ربط الترخيص بالجهاز إذا لم يكن مرتبطاً
                if (!validation.license.deviceId) {
                    bindLicenseToDevice(licenseCode, deviceId);
                }

                // حفظ معلومات الجلسة
                const sessionData = {
                    licenseCode: licenseCode,
                    deviceId: deviceId,
                    loginTime: new Date().toISOString(),
                    isActivated: true,
                    licenseType: validation.license.type,
                    expiresAt: validation.license.expiresAt
                };

                localStorage.setItem('currentSession', JSON.stringify(sessionData));

                showNotification(validation.message, 'success');

                // الانتقال للنظام الرئيسي بعد 2 ثانية
                setTimeout(() => {
                    window.location.href = 'installer-package/app/index.html';
                }, 2000);

            } catch (error) {
                showNotification('حدث خطأ في التحقق من الترخيص', 'error');
            } finally {
                // إخفاء مؤشر التحميل
                spinner.style.display = 'none';
                loginBtn.disabled = false;
            }
        });

        // عرض الإشعارات
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.innerHTML = `
                <div class="notification-content">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
                    <span>${message}</span>
                </div>
            `;

            document.body.appendChild(notification);

            // إظهار الإشعار
            setTimeout(() => {
                notification.classList.add('show');
            }, 100);

            // إزالة الإشعار تلقائياً
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    if (notification.parentElement) {
                        notification.remove();
                    }
                }, 400);
            }, 4000);
        }

        // إضافة ترخيص جديد (للاستخدام من لوحة المطور)
        function addNewLicense(licenseCode, type = 'full', durationDays = 365) {
            const licenses = getValidLicenses();

            // التحقق من عدم وجود الترخيص مسبقاً
            if (licenses.find(l => l.code === licenseCode)) {
                return { success: false, message: 'هذا الترخيص موجود مسبقاً' };
            }

            const newLicense = {
                code: licenseCode,
                type: type,
                expiresAt: new Date(Date.now() + durationDays * 24 * 60 * 60 * 1000).toISOString(),
                isActive: true,
                deviceId: null,
                activatedAt: null,
                createdAt: new Date().toISOString()
            };

            licenses.push(newLicense);
            localStorage.setItem('validLicenses', JSON.stringify(licenses));

            return { success: true, message: 'تم إضافة الترخيص بنجاح' };
        }

        // عرض التراخيص المتاحة (للمطورين)
        function showAvailableLicenses() {
            const licenses = getValidLicenses();
            console.log('التراخيص المتاحة:');
            licenses.forEach(license => {
                const status = license.deviceId ? `مرتبط بالجهاز: ${license.deviceId}` : 'غير مرتبط';
                const expiry = new Date(license.expiresAt).toLocaleDateString('ar-SA');
                console.log(`- ${license.code} (${license.type}) - انتهاء: ${expiry} - ${status}`);
            });
            return licenses;
        }

        // تهيئة النظام عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateDeviceId();

            // إضافة حدث النقر لنسخ معرف الجهاز
            document.getElementById('deviceId').addEventListener('click', copyDeviceId);

            // تنسيق كود الترخيص تلقائياً
            document.getElementById('licenseCode').addEventListener('input', function(e) {
                let value = e.target.value.replace(/[^A-Z0-9]/g, '').toUpperCase();
                let formatted = value.match(/.{1,4}/g)?.join('-') || value;
                if (formatted.length > 19) formatted = formatted.substr(0, 19);
                e.target.value = formatted;
            });

            // إضافة اختصارات لوحة المفاتيح للمطورين
            document.addEventListener('keydown', function(e) {
                // Ctrl + Shift + L لعرض التراخيص
                if (e.ctrlKey && e.shiftKey && e.key === 'L') {
                    e.preventDefault();
                    showAvailableLicenses();
                    showNotification('تم عرض التراخيص في وحدة التحكم (F12)', 'info');
                }

                // Ctrl + Shift + D لعرض معرف الجهاز
                if (e.ctrlKey && e.shiftKey && e.key === 'D') {
                    e.preventDefault();
                    const deviceId = document.getElementById('deviceId').textContent;
                    console.log('معرف الجهاز:', deviceId);
                    showNotification('تم عرض معرف الجهاز في وحدة التحكم', 'info');
                }

                // Ctrl + Shift + A لفتح لوحة التحكم الإدارية
                if (e.ctrlKey && e.shiftKey && e.key === 'A') {
                    e.preventDefault();
                    window.open('admin-control-panel.html', '_blank');
                    showNotification('تم فتح لوحة التحكم الإدارية', 'info');
                }
            });

            // رسالة ترحيب مع معلومات التراخيص التجريبية
            setTimeout(() => {
                showNotification('مرحباً بك في نظام إدارة مؤسسة وقود المستقبل', 'info');

                // عرض التراخيص التجريبية للمطورين
                setTimeout(() => {
                    const licenses = getValidLicenses();
                    console.log('التراخيص المتاحة عند التحميل:', licenses);

                    const demoLicense = licenses.find(l => l.type === 'demo');
                    if (demoLicense) {
                        showNotification(`ترخيص تجريبي متاح: ${demoLicense.code}`, 'warning');
                        console.log('ترخيص تجريبي:', demoLicense.code);
                    }

                    const fullLicense = licenses.find(l => l.type === 'full');
                    if (fullLicense) {
                        setTimeout(() => {
                            showNotification(`ترخيص كامل متاح: ${fullLicense.code}`, 'info');
                            console.log('ترخيص كامل:', fullLicense.code);
                        }, 1000);
                    }
                }, 2000);
            }, 1000);
        });

        // إتاحة الوظائف للاستخدام العام (للربط مع لوحة المطور)
        window.LicenseManager = {
            addLicense: addNewLicense,
            showLicenses: showAvailableLicenses,
            validateLicense: validateLicense,
            getValidLicenses: getValidLicenses
        };
    </script>
</body>
</html>
