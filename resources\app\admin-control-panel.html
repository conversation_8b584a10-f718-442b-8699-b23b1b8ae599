<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم الإدارية - مؤسسة وقود المستقبل</title>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            min-height: 100vh;
            direction: rtl;
            color: #e0e6ed;
            overflow-x: hidden;
        }

        /* الرأس الرئيسي */
        .main-header {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            padding: 1.5rem 2rem;
            box-shadow: 0 4px 20px rgba(231, 76, 60, 0.3);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1400px;
            margin: 0 auto;
        }

        .header-title {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .header-title h1 {
            font-size: 1.8rem;
            font-weight: 700;
        }

        .header-title .subtitle {
            font-size: 0.9rem;
            opacity: 0.9;
            margin-top: 0.2rem;
        }

        .header-stats {
            display: flex;
            gap: 2rem;
            align-items: center;
        }

        .stat-item {
            text-align: center;
        }

        .stat-value {
            font-size: 1.5rem;
            font-weight: bold;
            display: block;
        }

        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }

        /* الحاوية الرئيسية */
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        /* التبويبات */
        .tabs-container {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 1rem;
            margin-bottom: 2rem;
            backdrop-filter: blur(10px);
        }

        .tabs {
            display: flex;
            gap: 0.5rem;
            overflow-x: auto;
        }

        .tab-btn {
            background: transparent;
            color: #e0e6ed;
            border: none;
            padding: 1rem 1.5rem;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            white-space: nowrap;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
        }

        .tab-btn:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .tab-btn.active {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
        }

        /* محتوى التبويبات */
        .tab-content {
            display: none;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 2rem;
            backdrop-filter: blur(10px);
        }

        .tab-content.active {
            display: block;
        }

        /* الأزرار */
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 600;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        /* النماذج */
        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #e0e6ed;
        }

        .form-input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            color: #e0e6ed;
            font-size: 1rem;
        }

        .form-input:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
        }

        /* الجداول */
        .table-container {
            overflow-x: auto;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.05);
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            padding: 1rem;
            text-align: right;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .table th {
            background: rgba(255, 255, 255, 0.1);
            font-weight: 600;
            color: #3498db;
        }

        .table tr:hover {
            background: rgba(255, 255, 255, 0.05);
        }

        /* البطاقات */
        .card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .card-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #3498db;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        /* الحالات */
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .status-pending {
            background: #f39c12;
            color: white;
        }

        .status-approved {
            background: #2ecc71;
            color: white;
        }

        .status-rejected {
            background: #e74c3c;
            color: white;
        }

        .status-active {
            background: #2ecc71;
            color: white;
        }

        .status-expired {
            background: #e74c3c;
            color: white;
        }

        /* التنبيهات */
        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .alert-success {
            background: rgba(46, 204, 113, 0.2);
            border: 1px solid #2ecc71;
            color: #2ecc71;
        }

        .alert-warning {
            background: rgba(243, 156, 18, 0.2);
            border: 1px solid #f39c12;
            color: #f39c12;
        }

        .alert-danger {
            background: rgba(231, 76, 60, 0.2);
            border: 1px solid #e74c3c;
            color: #e74c3c;
        }

        /* تحسينات الاستجابة */
        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }

            .header-content {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }

            .header-stats {
                flex-direction: row;
                gap: 1rem;
            }

            .tabs {
                flex-direction: column;
            }

            .tab-content {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- الرأس الرئيسي -->
    <header class="main-header">
        <div class="header-content">
            <div class="header-title">
                <i class="fas fa-shield-alt" style="font-size: 2rem;"></i>
                <div>
                    <h1>لوحة التحكم الإدارية</h1>
                    <div class="subtitle">مؤسسة وقود المستقبل - إدارة التراخيص</div>
                </div>
            </div>
            
            <div class="header-stats">
                <div class="stat-item">
                    <span class="stat-value" id="totalRequests">0</span>
                    <span class="stat-label">طلبات التفعيل</span>
                </div>
                <div class="stat-item">
                    <span class="stat-value" id="activeLicenses">0</span>
                    <span class="stat-label">التراخيص النشطة</span>
                </div>
                <div class="stat-item">
                    <span class="stat-value" id="pendingRequests">0</span>
                    <span class="stat-label">في الانتظار</span>
                </div>
            </div>
        </div>
    </header>

    <!-- الحاوية الرئيسية -->
    <div class="container">
        <!-- التبويبات -->
        <div class="tabs-container">
            <div class="tabs">
                <button class="tab-btn active" data-tab="requests">
                    <i class="fas fa-inbox"></i>
                    طلبات التفعيل
                </button>
                <button class="tab-btn" data-tab="licenses">
                    <i class="fas fa-key"></i>
                    إدارة التراخيص
                </button>
                <button class="tab-btn" data-tab="generator">
                    <i class="fas fa-plus-circle"></i>
                    مولد التراخيص
                </button>
                <button class="tab-btn" data-tab="analytics">
                    <i class="fas fa-chart-bar"></i>
                    الإحصائيات
                </button>
                <button class="tab-btn" data-tab="activity">
                    <i class="fas fa-history"></i>
                    سجل النشاطات
                </button>
                <button class="tab-btn" data-tab="settings">
                    <i class="fas fa-cog"></i>
                    الإعدادات
                </button>
            </div>
        </div>

        <!-- محتوى التبويبات -->

        <!-- تبويب طلبات التفعيل -->
        <div class="tab-content active" id="requests-tab">
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">
                        <i class="fas fa-inbox"></i>
                        طلبات التفعيل
                    </h2>
                    <div>
                        <button class="btn btn-primary" onclick="refreshRequests()">
                            <i class="fas fa-sync-alt"></i>
                            تحديث
                        </button>
                        <button class="btn btn-success" onclick="exportRequests()">
                            <i class="fas fa-download"></i>
                            تصدير
                        </button>
                    </div>
                </div>

                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>معرف الطلب</th>
                                <th>الاسم</th>
                                <th>الهاتف</th>
                                <th>معرف الجهاز</th>
                                <th>التاريخ</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="requestsTableBody">
                            <!-- سيتم ملؤها بواسطة JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- تبويب إدارة التراخيص -->
        <div class="tab-content" id="licenses-tab">
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">
                        <i class="fas fa-key"></i>
                        التراخيص المُصدرة
                    </h2>
                    <div>
                        <button class="btn btn-primary" onclick="refreshLicenses()">
                            <i class="fas fa-sync-alt"></i>
                            تحديث
                        </button>
                        <button class="btn btn-success" onclick="exportLicenses()">
                            <i class="fas fa-download"></i>
                            تصدير
                        </button>
                    </div>
                </div>

                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>معرف الترخيص</th>
                                <th>معرف الجهاز</th>
                                <th>العميل</th>
                                <th>تاريخ الإصدار</th>
                                <th>تاريخ الانتهاء</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="licensesTableBody">
                            <!-- سيتم ملؤها بواسطة JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- تبويب مولد التراخيص -->
        <div class="tab-content" id="generator-tab">
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">
                        <i class="fas fa-plus-circle"></i>
                        مولد التراخيص الجديد
                    </h2>
                </div>

                <form id="licenseGeneratorForm">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;">
                        <div>
                            <div class="form-group">
                                <label class="form-label">اسم العميل:</label>
                                <input type="text" class="form-input" id="clientName" required>
                            </div>

                            <div class="form-group">
                                <label class="form-label">معرف الجهاز:</label>
                                <input type="text" class="form-input" id="deviceIdInput" required
                                       placeholder="FFC-XXXX-XXXX-XXXX-XXXX">
                            </div>

                            <div class="form-group">
                                <label class="form-label">مدة الترخيص:</label>
                                <select class="form-input" id="licenseDuration">
                                    <option value="30">30 يوم</option>
                                    <option value="90">90 يوم</option>
                                    <option value="180">180 يوم</option>
                                    <option value="365" selected>سنة واحدة</option>
                                    <option value="730">سنتان</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label class="form-label">ملاحظات:</label>
                                <textarea class="form-input" id="licenseNotes" rows="3"></textarea>
                            </div>

                            <button type="submit" class="btn btn-success" style="width: 100%;">
                                <i class="fas fa-key"></i>
                                توليد الترخيص
                            </button>
                        </div>

                        <div>
                            <div class="form-group">
                                <label class="form-label">الترخيص المُولد:</label>
                                <textarea class="form-input" id="generatedLicense" rows="8" readonly
                                          placeholder="سيظهر الترخيص هنا بعد التوليد..."></textarea>
                            </div>

                            <div style="display: flex; gap: 1rem;">
                                <button type="button" class="btn btn-primary" onclick="copyLicense()" style="flex: 1;">
                                    <i class="fas fa-copy"></i>
                                    نسخ الترخيص
                                </button>
                                <button type="button" class="btn btn-warning" onclick="saveLicense()" style="flex: 1;">
                                    <i class="fas fa-save"></i>
                                    حفظ الترخيص
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- تبويب الإحصائيات -->
        <div class="tab-content" id="analytics-tab">
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">
                        <i class="fas fa-chart-bar"></i>
                        لوحة الإحصائيات المتقدمة
                    </h2>
                    <div>
                        <button class="btn btn-primary" onclick="refreshAnalytics()">
                            <i class="fas fa-sync-alt"></i>
                            تحديث
                        </button>
                        <button class="btn btn-success" onclick="exportAnalytics()">
                            <i class="fas fa-download"></i>
                            تصدير التقرير
                        </button>
                    </div>
                </div>

                <!-- الإحصائيات السريعة -->
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem; margin-bottom: 2rem;">
                    <div class="card" style="background: linear-gradient(135deg, #3498db 0%, #2980b9 100%); color: white; text-align: center;">
                        <h3 style="margin: 0; font-size: 2rem;" id="analyticsRequests">0</h3>
                        <p style="margin: 0.5rem 0 0 0; opacity: 0.9;">إجمالي الطلبات</p>
                    </div>
                    <div class="card" style="background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%); color: white; text-align: center;">
                        <h3 style="margin: 0; font-size: 2rem;" id="analyticsActiveLicenses">0</h3>
                        <p style="margin: 0.5rem 0 0 0; opacity: 0.9;">التراخيص النشطة</p>
                    </div>
                    <div class="card" style="background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%); color: white; text-align: center;">
                        <h3 style="margin: 0; font-size: 2rem;" id="analyticsPending">0</h3>
                        <p style="margin: 0.5rem 0 0 0; opacity: 0.9;">في الانتظار</p>
                    </div>
                    <div class="card" style="background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%); color: white; text-align: center;">
                        <h3 style="margin: 0; font-size: 2rem;" id="analyticsExpired">0</h3>
                        <p style="margin: 0.5rem 0 0 0; opacity: 0.9;">التراخيص المنتهية</p>
                    </div>
                </div>

                <!-- الرسوم البيانية -->
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; margin-bottom: 2rem;">
                    <div class="card">
                        <h3 style="color: #3498db; margin-bottom: 1rem; text-align: center;">
                            <i class="fas fa-pie-chart"></i>
                            توزيع حالات الطلبات
                        </h3>
                        <canvas id="requestsChart" width="300" height="200"></canvas>
                    </div>

                    <div class="card">
                        <h3 style="color: #3498db; margin-bottom: 1rem; text-align: center;">
                            <i class="fas fa-chart-line"></i>
                            الطلبات خلال الأسبوع
                        </h3>
                        <canvas id="weeklyChart" width="300" height="200"></canvas>
                    </div>
                </div>

                <!-- جدول التراخيص المنتهية قريباً -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-exclamation-triangle" style="color: #f39c12;"></i>
                            التراخيص المنتهية قريباً (خلال 30 يوم)
                        </h3>
                    </div>

                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>العميل</th>
                                    <th>معرف الجهاز</th>
                                    <th>تاريخ الانتهاء</th>
                                    <th>الأيام المتبقية</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="expiringLicensesTable">
                                <!-- سيتم ملؤها بواسطة JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- تقرير النشاط اليومي -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-calendar-day"></i>
                            تقرير النشاط اليومي
                        </h3>
                    </div>

                    <div id="dailyActivityReport">
                        <!-- سيتم ملؤه بواسطة JavaScript -->
                    </div>
                </div>
            </div>
        </div>

        <!-- تبويب سجل النشاطات -->
        <div class="tab-content" id="activity-tab">
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">
                        <i class="fas fa-history"></i>
                        سجل النشاطات والأحداث
                    </h2>
                    <div>
                        <button class="btn btn-primary" onclick="refreshActivityLog()">
                            <i class="fas fa-sync-alt"></i>
                            تحديث
                        </button>
                        <button class="btn btn-warning" onclick="clearActivityLog()">
                            <i class="fas fa-trash"></i>
                            مسح السجل
                        </button>
                        <button class="btn btn-success" onclick="exportActivityLog()">
                            <i class="fas fa-download"></i>
                            تصدير السجل
                        </button>
                    </div>
                </div>

                <!-- فلاتر السجل -->
                <div style="background: rgba(255, 255, 255, 0.05); padding: 1rem; border-radius: 8px; margin-bottom: 1.5rem;">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; align-items: end;">
                        <div>
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 600; color: #e0e6ed;">نوع النشاط:</label>
                            <select id="activityTypeFilter" class="form-input" onchange="filterActivityLog()">
                                <option value="">جميع الأنشطة</option>
                                <option value="request">طلبات التفعيل</option>
                                <option value="license">إدارة التراخيص</option>
                                <option value="system">أحداث النظام</option>
                                <option value="error">أخطاء</option>
                            </select>
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 600; color: #e0e6ed;">التاريخ من:</label>
                            <input type="date" id="dateFromFilter" class="form-input" onchange="filterActivityLog()">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 600; color: #e0e6ed;">التاريخ إلى:</label>
                            <input type="date" id="dateToFilter" class="form-input" onchange="filterActivityLog()">
                        </div>
                        <div>
                            <button class="btn btn-primary" onclick="resetActivityFilters()" style="width: 100%;">
                                <i class="fas fa-undo"></i>
                                إعادة تعيين
                            </button>
                        </div>
                    </div>
                </div>

                <!-- جدول سجل النشاطات -->
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>الوقت</th>
                                <th>نوع النشاط</th>
                                <th>الوصف</th>
                                <th>المستخدم/الجهاز</th>
                                <th>التفاصيل</th>
                            </tr>
                        </thead>
                        <tbody id="activityLogTable">
                            <!-- سيتم ملؤها بواسطة JavaScript -->
                        </tbody>
                    </table>
                </div>

                <!-- إحصائيات سريعة للنشاطات -->
                <div style="margin-top: 2rem;">
                    <h3 style="color: #3498db; margin-bottom: 1rem;">إحصائيات النشاطات</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                        <div style="background: rgba(52, 152, 219, 0.1); padding: 1rem; border-radius: 8px; border-left: 4px solid #3498db;">
                            <h4 style="margin: 0; color: #3498db;">اليوم</h4>
                            <p style="margin: 0.5rem 0 0 0; font-size: 1.5rem; font-weight: bold;" id="todayActivities">0</p>
                        </div>
                        <div style="background: rgba(46, 204, 113, 0.1); padding: 1rem; border-radius: 8px; border-left: 4px solid #2ecc71;">
                            <h4 style="margin: 0; color: #2ecc71;">هذا الأسبوع</h4>
                            <p style="margin: 0.5rem 0 0 0; font-size: 1.5rem; font-weight: bold;" id="weekActivities">0</p>
                        </div>
                        <div style="background: rgba(243, 156, 18, 0.1); padding: 1rem; border-radius: 8px; border-left: 4px solid #f39c12;">
                            <h4 style="margin: 0; color: #f39c12;">هذا الشهر</h4>
                            <p style="margin: 0.5rem 0 0 0; font-size: 1.5rem; font-weight: bold;" id="monthActivities">0</p>
                        </div>
                        <div style="background: rgba(231, 76, 60, 0.1); padding: 1rem; border-radius: 8px; border-left: 4px solid #e74c3c;">
                            <h4 style="margin: 0; color: #e74c3c;">الأخطاء</h4>
                            <p style="margin: 0.5rem 0 0 0; font-size: 1.5rem; font-weight: bold;" id="errorActivities">0</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- تبويب الإعدادات -->
        <div class="tab-content" id="settings-tab">
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">
                        <i class="fas fa-cog"></i>
                        إعدادات النظام
                    </h2>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;">
                    <div>
                        <h3 style="color: #3498db; margin-bottom: 1rem;">إعدادات التراخيص</h3>

                        <div class="form-group">
                            <label class="form-label">مدة الترخيص الافتراضية (بالأيام):</label>
                            <input type="number" class="form-input" id="defaultDuration" value="365" min="1" max="3650">
                        </div>

                        <div class="form-group">
                            <label class="form-label">بادئة التراخيص:</label>
                            <input type="text" class="form-input" id="licensePrefix" value="FFC" maxlength="5">
                        </div>

                        <button class="btn btn-primary" onclick="saveSettings()">
                            <i class="fas fa-save"></i>
                            حفظ الإعدادات
                        </button>
                    </div>

                    <div>
                        <h3 style="color: #3498db; margin-bottom: 1rem;">أدوات النظام</h3>

                        <div style="display: flex; flex-direction: column; gap: 1rem;">
                            <button class="btn btn-success" onclick="exportAllData()">
                                <i class="fas fa-download"></i>
                                تصدير جميع البيانات
                            </button>

                            <button class="btn btn-warning" onclick="importData()">
                                <i class="fas fa-upload"></i>
                                استيراد البيانات
                            </button>

                            <button class="btn btn-danger" onclick="clearAllData()">
                                <i class="fas fa-trash"></i>
                                مسح جميع البيانات
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // بيانات النظام
        let systemData = {
            activationRequests: [],
            issuedLicenses: [],
            activityLog: [],
            settings: {
                defaultDuration: 365,
                licensePrefix: 'FFC'
            }
        };

        // تهيئة النظام
        document.addEventListener('DOMContentLoaded', function() {
            loadSystemData();
            setupEventListeners();
            updateStatistics();
            loadRequestsTable();
            loadLicensesTable();

            console.log('✅ تم تهيئة لوحة التحكم الإدارية بنجاح');
        });

        // تحميل بيانات النظام
        function loadSystemData() {
            // تحميل طلبات التفعيل
            const requests = localStorage.getItem('developerPanel_activationRequests');
            if (requests) {
                systemData.activationRequests = JSON.parse(requests);
            }

            // تحميل التراخيص المُصدرة
            const licenses = localStorage.getItem('developerPanel_issuedLicenses');
            if (licenses) {
                systemData.issuedLicenses = JSON.parse(licenses);
            }

            // تحميل الإعدادات
            const settings = localStorage.getItem('developerPanel_settings');
            if (settings) {
                systemData.settings = { ...systemData.settings, ...JSON.parse(settings) };
            }

            // تحميل سجل النشاطات
            const activityLog = localStorage.getItem('developerPanel_activityLog');
            if (activityLog) {
                systemData.activityLog = JSON.parse(activityLog);
            }
        }

        // حفظ بيانات النظام
        function saveSystemData() {
            localStorage.setItem('developerPanel_activationRequests', JSON.stringify(systemData.activationRequests));
            localStorage.setItem('developerPanel_issuedLicenses', JSON.stringify(systemData.issuedLicenses));
            localStorage.setItem('developerPanel_activityLog', JSON.stringify(systemData.activityLog));
            localStorage.setItem('developerPanel_settings', JSON.stringify(systemData.settings));
        }

        // إعداد مستمعي الأحداث
        function setupEventListeners() {
            // التبويبات
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const tabName = this.dataset.tab;
                    switchTab(tabName);
                });
            });

            // نموذج توليد الترخيص
            document.getElementById('licenseGeneratorForm').addEventListener('submit', handleLicenseGeneration);
        }

        // تبديل التبويبات
        function switchTab(tabName) {
            // إخفاء جميع التبويبات
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });

            // إزالة الحالة النشطة من جميع الأزرار
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            // إظهار التبويب المحدد
            document.getElementById(tabName + '-tab').classList.add('active');
            document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

            // تحديث البيانات حسب التبويب
            if (tabName === 'requests') {
                loadRequestsTable();
            } else if (tabName === 'licenses') {
                loadLicensesTable();
            } else if (tabName === 'analytics') {
                loadAnalytics();
            } else if (tabName === 'activity') {
                loadActivityLog();
            }
        }

        // تحديث الإحصائيات
        function updateStatistics() {
            const totalRequests = systemData.activationRequests.length;
            const pendingRequests = systemData.activationRequests.filter(req => req.status === 'pending').length;
            const activeLicenses = systemData.issuedLicenses.filter(license => {
                return new Date(license.expiryDate) > new Date();
            }).length;

            document.getElementById('totalRequests').textContent = totalRequests;
            document.getElementById('pendingRequests').textContent = pendingRequests;
            document.getElementById('activeLicenses').textContent = activeLicenses;
        }

        // تحميل جدول طلبات التفعيل
        function loadRequestsTable() {
            const tbody = document.getElementById('requestsTableBody');
            tbody.innerHTML = '';

            if (systemData.activationRequests.length === 0) {
                tbody.innerHTML = '<tr><td colspan="7" style="text-align: center; color: #6c757d;">لا توجد طلبات تفعيل</td></tr>';
                return;
            }

            systemData.activationRequests.forEach(request => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td style="font-family: monospace; font-size: 0.8rem;">${request.id}</td>
                    <td>${request.firstName} ${request.lastName}</td>
                    <td>${request.phone}</td>
                    <td style="font-family: monospace; font-size: 0.8rem;">${request.deviceId.substr(0, 20)}...</td>
                    <td>${new Date(request.timestamp).toLocaleDateString('ar-SA')}</td>
                    <td><span class="status-badge status-${request.status}">${getStatusText(request.status)}</span></td>
                    <td>
                        <div style="display: flex; gap: 0.5rem;">
                            ${request.status === 'pending' ? `
                                <button class="btn btn-success" style="padding: 0.25rem 0.5rem; font-size: 0.8rem;" onclick="approveRequest('${request.id}')">
                                    <i class="fas fa-check"></i>
                                </button>
                                <button class="btn btn-danger" style="padding: 0.25rem 0.5rem; font-size: 0.8rem;" onclick="rejectRequest('${request.id}')">
                                    <i class="fas fa-times"></i>
                                </button>
                            ` : ''}
                            <button class="btn btn-primary" style="padding: 0.25rem 0.5rem; font-size: 0.8rem;" onclick="viewRequestDetails('${request.id}')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // تحميل جدول التراخيص
        function loadLicensesTable() {
            const tbody = document.getElementById('licensesTableBody');
            tbody.innerHTML = '';

            if (systemData.issuedLicenses.length === 0) {
                tbody.innerHTML = '<tr><td colspan="7" style="text-align: center; color: #6c757d;">لا توجد تراخيص مُصدرة</td></tr>';
                return;
            }

            systemData.issuedLicenses.forEach(license => {
                const isExpired = new Date(license.expiryDate) < new Date();
                const status = isExpired ? 'expired' : 'active';

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td style="font-family: monospace; font-size: 0.8rem;">${license.id}</td>
                    <td style="font-family: monospace; font-size: 0.8rem;">${license.deviceId.substr(0, 20)}...</td>
                    <td>${license.clientName}</td>
                    <td>${new Date(license.issuedDate).toLocaleDateString('ar-SA')}</td>
                    <td>${new Date(license.expiryDate).toLocaleDateString('ar-SA')}</td>
                    <td><span class="status-badge status-${status}">${status === 'active' ? 'نشط' : 'منتهي'}</span></td>
                    <td>
                        <div style="display: flex; gap: 0.5rem;">
                            <button class="btn btn-primary" style="padding: 0.25rem 0.5rem; font-size: 0.8rem;" onclick="viewLicenseDetails('${license.id}')">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-warning" style="padding: 0.25rem 0.5rem; font-size: 0.8rem;" onclick="renewLicense('${license.id}')">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                            <button class="btn btn-danger" style="padding: 0.25rem 0.5rem; font-size: 0.8rem;" onclick="revokeLicense('${license.id}')">
                                <i class="fas fa-ban"></i>
                            </button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // الحصول على نص الحالة
        function getStatusText(status) {
            const statusTexts = {
                'pending': 'في الانتظار',
                'approved': 'مُوافق عليه',
                'rejected': 'مرفوض'
            };
            return statusTexts[status] || status;
        }

        // الموافقة على طلب التفعيل
        function approveRequest(requestId) {
            const request = systemData.activationRequests.find(req => req.id === requestId);
            if (!request) return;

            // تحديث حالة الطلب
            request.status = 'approved';
            request.approvedAt = new Date().toISOString();

            // تسجيل النشاط
            logActivity('request', `تم الموافقة على طلب التفعيل للعميل: ${request.firstName} ${request.lastName}`, {
                requestId: request.id,
                deviceId: request.deviceId,
                clientName: `${request.firstName} ${request.lastName}`
            });

            // ملء نموذج توليد الترخيص
            document.getElementById('clientName').value = `${request.firstName} ${request.lastName}`;
            document.getElementById('deviceIdInput').value = request.deviceId;

            // التبديل إلى تبويب مولد التراخيص
            switchTab('generator');

            // حفظ البيانات
            saveSystemData();
            updateStatistics();
            loadRequestsTable();

            showNotification('تم الموافقة على الطلب. يمكنك الآن توليد الترخيص.', 'success');
        }

        // رفض طلب التفعيل
        function rejectRequest(requestId) {
            if (!confirm('هل أنت متأكد من رفض هذا الطلب؟')) return;

            const request = systemData.activationRequests.find(req => req.id === requestId);
            if (!request) return;

            request.status = 'rejected';
            request.rejectedAt = new Date().toISOString();

            // تسجيل النشاط
            logActivity('request', `تم رفض طلب التفعيل للعميل: ${request.firstName} ${request.lastName}`, {
                requestId: request.id,
                deviceId: request.deviceId,
                clientName: `${request.firstName} ${request.lastName}`
            });

            saveSystemData();
            updateStatistics();
            loadRequestsTable();

            showNotification('تم رفض الطلب.', 'warning');
        }

        // عرض تفاصيل الطلب
        function viewRequestDetails(requestId) {
            const request = systemData.activationRequests.find(req => req.id === requestId);
            if (!request) return;

            const details = `
معرف الطلب: ${request.id}
الاسم: ${request.firstName} ${request.lastName}
الهاتف: ${request.phone}
اسم المؤسسة: ${request.businessName || 'غير محدد'}
معرف الجهاز: ${request.deviceId}
تاريخ الطلب: ${new Date(request.timestamp).toLocaleString('ar-SA')}
الحالة: ${getStatusText(request.status)}
الملاحظات: ${request.notes || 'لا توجد ملاحظات'}
            `;

            alert(details);
        }

        // معالجة توليد الترخيص
        function handleLicenseGeneration(event) {
            event.preventDefault();

            const clientName = document.getElementById('clientName').value.trim();
            const deviceId = document.getElementById('deviceIdInput').value.trim();
            const duration = parseInt(document.getElementById('licenseDuration').value);
            const notes = document.getElementById('licenseNotes').value.trim();

            if (!clientName || !deviceId) {
                showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
                return;
            }

            // التحقق من صيغة معرف الجهاز
            if (!deviceId.match(/^FFC-[A-F0-9]{4}-[A-F0-9]{4}-[A-F0-9]{4}-[A-F0-9]{4}$/)) {
                showNotification('صيغة معرف الجهاز غير صحيحة', 'error');
                return;
            }

            // توليد الترخيص
            const license = generateLicense(clientName, deviceId, duration, notes);

            // عرض الترخيص
            document.getElementById('generatedLicense').value = license.licenseKey;

            showNotification('تم توليد الترخيص بنجاح!', 'success');
        }

        // توليد الترخيص
        function generateLicense(clientName, deviceId, durationDays, notes) {
            const now = new Date();
            const expiryDate = new Date(now.getTime() + (durationDays * 24 * 60 * 60 * 1000));

            const licenseData = {
                id: 'LIC-' + Date.now(),
                clientName: clientName,
                deviceId: deviceId,
                issuedDate: now.toISOString(),
                expiryDate: expiryDate.toISOString(),
                duration: durationDays,
                notes: notes,
                type: 'FFC_LICENSE',
                version: '2.0'
            };

            // تشفير الترخيص
            const licenseKey = btoa(JSON.stringify({
                deviceId: deviceId,
                expiryDate: expiryDate.toISOString(),
                type: 'FFC_LICENSE',
                issued: now.toISOString()
            }));

            licenseData.licenseKey = licenseKey;

            // تسجيل النشاط
            logActivity('license', `تم إصدار ترخيص جديد للعميل: ${clientName}`, {
                licenseId: licenseData.id,
                deviceId: deviceId,
                clientName: clientName,
                duration: durationDays
            });

            // حفظ الترخيص
            systemData.issuedLicenses.push(licenseData);
            saveSystemData();
            updateStatistics();
            loadLicensesTable();

            return licenseData;
        }

        // نسخ الترخيص
        function copyLicense() {
            const licenseText = document.getElementById('generatedLicense').value;
            if (!licenseText) {
                showNotification('لا يوجد ترخيص للنسخ', 'warning');
                return;
            }

            navigator.clipboard.writeText(licenseText).then(() => {
                showNotification('تم نسخ الترخيص إلى الحافظة', 'success');
            }).catch(() => {
                // طريقة بديلة
                const textArea = document.createElement('textarea');
                textArea.value = licenseText;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showNotification('تم نسخ الترخيص', 'success');
            });
        }

        // حفظ الترخيص
        function saveLicense() {
            const licenseText = document.getElementById('generatedLicense').value;
            if (!licenseText) {
                showNotification('لا يوجد ترخيص للحفظ', 'warning');
                return;
            }

            const blob = new Blob([licenseText], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `license-${Date.now()}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            showNotification('تم حفظ الترخيص كملف', 'success');
        }

        // وظائف التحديث
        function refreshRequests() {
            loadSystemData();
            loadRequestsTable();
            updateStatistics();
            showNotification('تم تحديث طلبات التفعيل', 'success');
        }

        function refreshLicenses() {
            loadSystemData();
            loadLicensesTable();
            updateStatistics();
            showNotification('تم تحديث التراخيص', 'success');
        }

        // عرض تفاصيل الترخيص
        function viewLicenseDetails(licenseId) {
            const license = systemData.issuedLicenses.find(lic => lic.id === licenseId);
            if (!license) return;

            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.8);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
                direction: rtl;
            `;

            const isExpired = new Date(license.expiryDate) < new Date();
            const daysLeft = Math.ceil((new Date(license.expiryDate) - new Date()) / (1000 * 60 * 60 * 24));

            modal.innerHTML = `
                <div style="background: white; padding: 2rem; border-radius: 15px; max-width: 600px; width: 90%; max-height: 80vh; overflow-y: auto;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 2rem; border-bottom: 1px solid #eee; padding-bottom: 1rem;">
                        <h3 style="color: #2c3e50; margin: 0;">
                            <i class="fas fa-key" style="color: #3498db;"></i>
                            تفاصيل الترخيص
                        </h3>
                        <button onclick="this.closest('div').parentElement.remove()" style="background: #e74c3c; color: white; border: none; padding: 0.5rem; border-radius: 50%; cursor: pointer; width: 40px; height: 40px;">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;">
                        <div>
                            <h4 style="color: #3498db; margin-bottom: 1rem;">معلومات أساسية</h4>
                            <div style="margin-bottom: 1rem;">
                                <strong>معرف الترخيص:</strong><br>
                                <span style="font-family: monospace; background: #f8f9fa; padding: 0.25rem; border-radius: 4px;">${license.id}</span>
                            </div>
                            <div style="margin-bottom: 1rem;">
                                <strong>اسم العميل:</strong><br>
                                <span>${license.clientName}</span>
                            </div>
                            <div style="margin-bottom: 1rem;">
                                <strong>معرف الجهاز:</strong><br>
                                <span style="font-family: monospace; background: #f8f9fa; padding: 0.25rem; border-radius: 4px; word-break: break-all;">${license.deviceId}</span>
                            </div>
                            <div style="margin-bottom: 1rem;">
                                <strong>الحالة:</strong><br>
                                <span class="status-badge status-${isExpired ? 'expired' : 'active'}">${isExpired ? 'منتهي الصلاحية' : 'نشط'}</span>
                            </div>
                        </div>

                        <div>
                            <h4 style="color: #3498db; margin-bottom: 1rem;">معلومات التوقيت</h4>
                            <div style="margin-bottom: 1rem;">
                                <strong>تاريخ الإصدار:</strong><br>
                                <span>${new Date(license.issuedDate).toLocaleString('ar-SA')}</span>
                            </div>
                            <div style="margin-bottom: 1rem;">
                                <strong>تاريخ الانتهاء:</strong><br>
                                <span>${new Date(license.expiryDate).toLocaleString('ar-SA')}</span>
                            </div>
                            <div style="margin-bottom: 1rem;">
                                <strong>المدة:</strong><br>
                                <span>${license.duration || 'غير محدد'} يوم</span>
                            </div>
                            <div style="margin-bottom: 1rem;">
                                <strong>الأيام المتبقية:</strong><br>
                                <span style="color: ${daysLeft > 30 ? '#2ecc71' : daysLeft > 7 ? '#f39c12' : '#e74c3c'}; font-weight: bold;">
                                    ${isExpired ? 'منتهي' : daysLeft + ' يوم'}
                                </span>
                            </div>
                        </div>
                    </div>

                    <div style="margin-top: 2rem;">
                        <h4 style="color: #3498db; margin-bottom: 1rem;">مفتاح الترخيص</h4>
                        <textarea readonly style="width: 100%; height: 100px; font-family: monospace; font-size: 0.8rem; background: #f8f9fa; border: 1px solid #ddd; border-radius: 4px; padding: 0.5rem;">${license.licenseKey}</textarea>
                        <div style="margin-top: 0.5rem;">
                            <button onclick="copyToClipboard('${license.licenseKey}')" style="background: #3498db; color: white; border: none; padding: 0.5rem 1rem; border-radius: 4px; cursor: pointer;">
                                <i class="fas fa-copy"></i> نسخ المفتاح
                            </button>
                        </div>
                    </div>

                    ${license.notes ? `
                        <div style="margin-top: 2rem;">
                            <h4 style="color: #3498db; margin-bottom: 1rem;">ملاحظات</h4>
                            <div style="background: #f8f9fa; padding: 1rem; border-radius: 4px; border-left: 4px solid #3498db;">
                                ${license.notes}
                            </div>
                        </div>
                    ` : ''}

                    <div style="margin-top: 2rem; display: flex; gap: 1rem; justify-content: center;">
                        <button onclick="renewLicense('${license.id}')" style="background: #f39c12; color: white; border: none; padding: 0.75rem 1.5rem; border-radius: 8px; cursor: pointer;">
                            <i class="fas fa-sync-alt"></i> تجديد الترخيص
                        </button>
                        <button onclick="revokeLicense('${license.id}')" style="background: #e74c3c; color: white; border: none; padding: 0.75rem 1.5rem; border-radius: 8px; cursor: pointer;">
                            <i class="fas fa-ban"></i> إلغاء الترخيص
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
        }

        // تجديد الترخيص
        function renewLicense(licenseId) {
            const license = systemData.issuedLicenses.find(lic => lic.id === licenseId);
            if (!license) return;

            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.8);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
                direction: rtl;
            `;

            modal.innerHTML = `
                <div style="background: white; padding: 2rem; border-radius: 15px; max-width: 500px; width: 90%;">
                    <h3 style="color: #2c3e50; margin-bottom: 1rem; text-align: center;">
                        <i class="fas fa-sync-alt" style="color: #f39c12;"></i>
                        تجديد الترخيص
                    </h3>

                    <form id="renewLicenseForm">
                        <div style="margin-bottom: 1rem;">
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 600; color: #2c3e50;">العميل:</label>
                            <input type="text" value="${license.clientName}" readonly style="width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 8px; background: #f8f9fa;">
                        </div>

                        <div style="margin-bottom: 1rem;">
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 600; color: #2c3e50;">مدة التجديد (بالأيام):</label>
                            <select id="renewDuration" style="width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 8px;">
                                <option value="30">30 يوم</option>
                                <option value="90">90 يوم</option>
                                <option value="180">180 يوم</option>
                                <option value="365" selected>سنة واحدة</option>
                                <option value="730">سنتان</option>
                            </select>
                        </div>

                        <div style="margin-bottom: 1.5rem;">
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 600; color: #2c3e50;">ملاحظات التجديد:</label>
                            <textarea id="renewNotes" rows="3" style="width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 8px; resize: vertical;" placeholder="ملاحظات اختيارية..."></textarea>
                        </div>

                        <div style="display: flex; gap: 1rem; justify-content: center;">
                            <button type="submit" style="background: #f39c12; color: white; border: none; padding: 0.75rem 1.5rem; border-radius: 8px; cursor: pointer; font-weight: 600;">
                                <i class="fas fa-sync-alt"></i> تجديد الترخيص
                            </button>
                            <button type="button" onclick="this.closest('div').parentElement.remove()" style="background: #95a5a6; color: white; border: none; padding: 0.75rem 1.5rem; border-radius: 8px; cursor: pointer;">
                                إلغاء
                            </button>
                        </div>
                    </form>
                </div>
            `;

            document.body.appendChild(modal);

            document.getElementById('renewLicenseForm').addEventListener('submit', function(e) {
                e.preventDefault();

                const duration = parseInt(document.getElementById('renewDuration').value);
                const notes = document.getElementById('renewNotes').value;

                // تحديث تاريخ الانتهاء
                const newExpiryDate = new Date(Date.now() + (duration * 24 * 60 * 60 * 1000));
                license.expiryDate = newExpiryDate.toISOString();
                license.renewedAt = new Date().toISOString();
                license.renewalNotes = notes;

                // توليد مفتاح ترخيص جديد
                const newLicenseData = {
                    deviceId: license.deviceId,
                    expiryDate: newExpiryDate.toISOString(),
                    type: 'FFC_LICENSE',
                    issued: license.issuedDate,
                    renewed: new Date().toISOString()
                };

                license.licenseKey = btoa(JSON.stringify(newLicenseData));

                saveSystemData();
                updateStatistics();
                loadLicensesTable();

                modal.remove();
                showNotification(`تم تجديد الترخيص لـ ${duration} يوم`, 'success');
            });
        }

        // إلغاء الترخيص
        function revokeLicense(licenseId) {
            if (!confirm('هل أنت متأكد من إلغاء هذا الترخيص؟ هذا الإجراء لا يمكن التراجع عنه.')) return;

            const licenseIndex = systemData.issuedLicenses.findIndex(lic => lic.id === licenseId);
            if (licenseIndex === -1) return;

            // تحديث الترخيص كملغي بدلاً من حذفه
            systemData.issuedLicenses[licenseIndex].status = 'revoked';
            systemData.issuedLicenses[licenseIndex].revokedAt = new Date().toISOString();

            saveSystemData();
            updateStatistics();
            loadLicensesTable();

            // إغلاق أي نوافذ مفتوحة
            document.querySelectorAll('div[style*="position: fixed"]').forEach(modal => {
                if (modal.style.background.includes('rgba(0, 0, 0, 0.8)')) {
                    modal.remove();
                }
            });

            showNotification('تم إلغاء الترخيص بنجاح', 'warning');
        }

        // نسخ إلى الحافظة
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                showNotification('تم نسخ النص إلى الحافظة', 'success');
            }).catch(() => {
                // طريقة بديلة
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showNotification('تم نسخ النص', 'success');
            });
        }

        // تصدير طلبات التفعيل
        function exportRequests() {
            if (systemData.activationRequests.length === 0) {
                showNotification('لا توجد طلبات للتصدير', 'warning');
                return;
            }

            const csvContent = [
                ['معرف الطلب', 'الاسم الأول', 'اللقب', 'الهاتف', 'اسم المؤسسة', 'معرف الجهاز', 'التاريخ', 'الحالة', 'الملاحظات'],
                ...systemData.activationRequests.map(req => [
                    req.id,
                    req.firstName,
                    req.lastName,
                    req.phone,
                    req.businessName || '',
                    req.deviceId,
                    new Date(req.timestamp).toLocaleDateString('ar-SA'),
                    getStatusText(req.status),
                    req.notes || ''
                ])
            ].map(row => row.join(',')).join('\n');

            const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `activation-requests-${new Date().toISOString().split('T')[0]}.csv`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            showNotification('تم تصدير طلبات التفعيل بنجاح', 'success');
        }

        // تصدير التراخيص
        function exportLicenses() {
            if (systemData.issuedLicenses.length === 0) {
                showNotification('لا توجد تراخيص للتصدير', 'warning');
                return;
            }

            const csvContent = [
                ['معرف الترخيص', 'اسم العميل', 'معرف الجهاز', 'تاريخ الإصدار', 'تاريخ الانتهاء', 'المدة', 'الحالة', 'مفتاح الترخيص'],
                ...systemData.issuedLicenses.map(license => [
                    license.id,
                    license.clientName,
                    license.deviceId,
                    new Date(license.issuedDate).toLocaleDateString('ar-SA'),
                    new Date(license.expiryDate).toLocaleDateString('ar-SA'),
                    license.duration || '',
                    new Date(license.expiryDate) > new Date() ? 'نشط' : 'منتهي',
                    license.licenseKey
                ])
            ].map(row => row.join(',')).join('\n');

            const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `issued-licenses-${new Date().toISOString().split('T')[0]}.csv`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            showNotification('تم تصدير التراخيص بنجاح', 'success');
        }

        // حفظ الإعدادات
        function saveSettings() {
            const defaultDuration = document.getElementById('defaultDuration').value;
            const licensePrefix = document.getElementById('licensePrefix').value;

            systemData.settings.defaultDuration = parseInt(defaultDuration);
            systemData.settings.licensePrefix = licensePrefix;

            saveSystemData();
            showNotification('تم حفظ الإعدادات بنجاح', 'success');
        }

        // تصدير جميع البيانات
        function exportAllData() {
            const allData = {
                exportDate: new Date().toISOString(),
                version: '2.2.0',
                activationRequests: systemData.activationRequests,
                issuedLicenses: systemData.issuedLicenses,
                settings: systemData.settings,
                statistics: {
                    totalRequests: systemData.activationRequests.length,
                    pendingRequests: systemData.activationRequests.filter(req => req.status === 'pending').length,
                    activeLicenses: systemData.issuedLicenses.filter(license => new Date(license.expiryDate) > new Date()).length
                }
            };

            const blob = new Blob([JSON.stringify(allData, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `ffc-system-backup-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            showNotification('تم تصدير جميع البيانات بنجاح', 'success');
        }

        // استيراد البيانات
        function importData() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';

            input.onchange = function(e) {
                const file = e.target.files[0];
                if (!file) return;

                const reader = new FileReader();
                reader.onload = function(e) {
                    try {
                        const importedData = JSON.parse(e.target.result);

                        if (confirm('هل أنت متأكد من استيراد البيانات؟ سيتم استبدال البيانات الحالية.')) {
                            if (importedData.activationRequests) {
                                systemData.activationRequests = importedData.activationRequests;
                            }
                            if (importedData.issuedLicenses) {
                                systemData.issuedLicenses = importedData.issuedLicenses;
                            }
                            if (importedData.settings) {
                                systemData.settings = { ...systemData.settings, ...importedData.settings };
                            }

                            saveSystemData();
                            updateStatistics();
                            loadRequestsTable();
                            loadLicensesTable();

                            showNotification('تم استيراد البيانات بنجاح', 'success');
                        }
                    } catch (error) {
                        showNotification('خطأ في قراءة الملف: ' + error.message, 'error');
                    }
                };
                reader.readAsText(file);
            };

            input.click();
        }

        // مسح جميع البيانات
        function clearAllData() {
            if (!confirm('هل أنت متأكد من مسح جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه.')) return;

            if (!confirm('تأكيد أخير: سيتم مسح جميع طلبات التفعيل والتراخيص المُصدرة. هل تريد المتابعة؟')) return;

            systemData.activationRequests = [];
            systemData.issuedLicenses = [];

            saveSystemData();
            updateStatistics();
            loadRequestsTable();
            loadLicensesTable();

            showNotification('تم مسح جميع البيانات', 'warning');
        }

        // متغيرات الرسوم البيانية
        let requestsChart = null;
        let weeklyChart = null;

        // تحميل الإحصائيات
        function loadAnalytics() {
            updateAnalyticsStats();
            createRequestsChart();
            createWeeklyChart();
            loadExpiringLicenses();
            generateDailyReport();
        }

        // تحديث إحصائيات التحليلات
        function updateAnalyticsStats() {
            const totalRequests = systemData.activationRequests.length;
            const activeLicenses = systemData.issuedLicenses.filter(license =>
                new Date(license.expiryDate) > new Date() && license.status !== 'revoked'
            ).length;
            const pendingRequests = systemData.activationRequests.filter(req => req.status === 'pending').length;
            const expiredLicenses = systemData.issuedLicenses.filter(license =>
                new Date(license.expiryDate) < new Date() || license.status === 'revoked'
            ).length;

            document.getElementById('analyticsRequests').textContent = totalRequests;
            document.getElementById('analyticsActiveLicenses').textContent = activeLicenses;
            document.getElementById('analyticsPending').textContent = pendingRequests;
            document.getElementById('analyticsExpired').textContent = expiredLicenses;
        }

        // إنشاء رسم بياني لحالات الطلبات
        function createRequestsChart() {
            const ctx = document.getElementById('requestsChart').getContext('2d');

            if (requestsChart) {
                requestsChart.destroy();
            }

            const pending = systemData.activationRequests.filter(req => req.status === 'pending').length;
            const approved = systemData.activationRequests.filter(req => req.status === 'approved').length;
            const rejected = systemData.activationRequests.filter(req => req.status === 'rejected').length;

            requestsChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['في الانتظار', 'مُوافق عليه', 'مرفوض'],
                    datasets: [{
                        data: [pending, approved, rejected],
                        backgroundColor: [
                            '#f39c12',
                            '#2ecc71',
                            '#e74c3c'
                        ],
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 20,
                                usePointStyle: true
                            }
                        }
                    }
                }
            });
        }

        // إنشاء رسم بياني للطلبات الأسبوعية
        function createWeeklyChart() {
            const ctx = document.getElementById('weeklyChart').getContext('2d');

            if (weeklyChart) {
                weeklyChart.destroy();
            }

            // حساب الطلبات لآخر 7 أيام
            const last7Days = [];
            const requestCounts = [];

            for (let i = 6; i >= 0; i--) {
                const date = new Date();
                date.setDate(date.getDate() - i);
                const dateStr = date.toISOString().split('T')[0];

                last7Days.push(date.toLocaleDateString('ar-SA', { weekday: 'short' }));

                const dayRequests = systemData.activationRequests.filter(req => {
                    const reqDate = new Date(req.timestamp).toISOString().split('T')[0];
                    return reqDate === dateStr;
                }).length;

                requestCounts.push(dayRequests);
            }

            weeklyChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: last7Days,
                    datasets: [{
                        label: 'عدد الطلبات',
                        data: requestCounts,
                        borderColor: '#3498db',
                        backgroundColor: 'rgba(52, 152, 219, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4,
                        pointBackgroundColor: '#3498db',
                        pointBorderColor: '#fff',
                        pointBorderWidth: 2,
                        pointRadius: 6
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                stepSize: 1
                            }
                        }
                    }
                }
            });
        }

        // تحميل التراخيص المنتهية قريباً
        function loadExpiringLicenses() {
            const tbody = document.getElementById('expiringLicensesTable');
            tbody.innerHTML = '';

            const now = new Date();
            const thirtyDaysFromNow = new Date(now.getTime() + (30 * 24 * 60 * 60 * 1000));

            const expiringLicenses = systemData.issuedLicenses.filter(license => {
                const expiryDate = new Date(license.expiryDate);
                return expiryDate > now && expiryDate <= thirtyDaysFromNow && license.status !== 'revoked';
            }).sort((a, b) => new Date(a.expiryDate) - new Date(b.expiryDate));

            if (expiringLicenses.length === 0) {
                tbody.innerHTML = '<tr><td colspan="5" style="text-align: center; color: #6c757d;">لا توجد تراخيص منتهية قريباً</td></tr>';
                return;
            }

            expiringLicenses.forEach(license => {
                const expiryDate = new Date(license.expiryDate);
                const daysLeft = Math.ceil((expiryDate - now) / (1000 * 60 * 60 * 24));

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${license.clientName}</td>
                    <td style="font-family: monospace; font-size: 0.8rem;">${license.deviceId.substr(0, 20)}...</td>
                    <td>${expiryDate.toLocaleDateString('ar-SA')}</td>
                    <td>
                        <span style="color: ${daysLeft <= 7 ? '#e74c3c' : daysLeft <= 15 ? '#f39c12' : '#2ecc71'}; font-weight: bold;">
                            ${daysLeft} يوم
                        </span>
                    </td>
                    <td>
                        <button class="btn btn-warning" style="padding: 0.25rem 0.5rem; font-size: 0.8rem;" onclick="renewLicense('${license.id}')">
                            <i class="fas fa-sync-alt"></i> تجديد
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // توليد تقرير النشاط اليومي
        function generateDailyReport() {
            const container = document.getElementById('dailyActivityReport');
            const today = new Date();
            const todayStr = today.toISOString().split('T')[0];

            // طلبات اليوم
            const todayRequests = systemData.activationRequests.filter(req => {
                const reqDate = new Date(req.timestamp).toISOString().split('T')[0];
                return reqDate === todayStr;
            });

            // التراخيص المُصدرة اليوم
            const todayLicenses = systemData.issuedLicenses.filter(license => {
                const issueDate = new Date(license.issuedDate).toISOString().split('T')[0];
                return issueDate === todayStr;
            });

            // التراخيص المُجددة اليوم
            const todayRenewals = systemData.issuedLicenses.filter(license => {
                return license.renewedAt && new Date(license.renewedAt).toISOString().split('T')[0] === todayStr;
            });

            container.innerHTML = `
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                    <div style="background: #e8f4fd; padding: 1rem; border-radius: 8px; border-left: 4px solid #3498db;">
                        <h4 style="margin: 0; color: #3498db;">طلبات اليوم</h4>
                        <p style="margin: 0.5rem 0 0 0; font-size: 1.5rem; font-weight: bold; color: #2c3e50;">${todayRequests.length}</p>
                    </div>
                    <div style="background: #eafaf1; padding: 1rem; border-radius: 8px; border-left: 4px solid #2ecc71;">
                        <h4 style="margin: 0; color: #2ecc71;">تراخيص مُصدرة</h4>
                        <p style="margin: 0.5rem 0 0 0; font-size: 1.5rem; font-weight: bold; color: #2c3e50;">${todayLicenses.length}</p>
                    </div>
                    <div style="background: #fef9e7; padding: 1rem; border-radius: 8px; border-left: 4px solid #f39c12;">
                        <h4 style="margin: 0; color: #f39c12;">تراخيص مُجددة</h4>
                        <p style="margin: 0.5rem 0 0 0; font-size: 1.5rem; font-weight: bold; color: #2c3e50;">${todayRenewals.length}</p>
                    </div>
                </div>

                ${todayRequests.length > 0 ? `
                    <div style="margin-top: 1.5rem;">
                        <h4 style="color: #3498db; margin-bottom: 1rem;">طلبات اليوم:</h4>
                        <div style="max-height: 200px; overflow-y: auto;">
                            ${todayRequests.map(req => `
                                <div style="background: #f8f9fa; padding: 0.75rem; margin-bottom: 0.5rem; border-radius: 6px; display: flex; justify-content: space-between; align-items: center;">
                                    <div>
                                        <strong>${req.firstName} ${req.lastName}</strong> - ${req.phone}
                                        <br><small style="color: #6c757d;">${new Date(req.timestamp).toLocaleTimeString('ar-SA')}</small>
                                    </div>
                                    <span class="status-badge status-${req.status}">${getStatusText(req.status)}</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                ` : ''}
            `;
        }

        // تحديث الإحصائيات
        function refreshAnalytics() {
            loadAnalytics();
            showNotification('تم تحديث الإحصائيات', 'success');
        }

        // تصدير تقرير الإحصائيات
        function exportAnalytics() {
            const analyticsData = {
                reportDate: new Date().toISOString(),
                summary: {
                    totalRequests: systemData.activationRequests.length,
                    activeLicenses: systemData.issuedLicenses.filter(license =>
                        new Date(license.expiryDate) > new Date() && license.status !== 'revoked'
                    ).length,
                    pendingRequests: systemData.activationRequests.filter(req => req.status === 'pending').length,
                    expiredLicenses: systemData.issuedLicenses.filter(license =>
                        new Date(license.expiryDate) < new Date() || license.status === 'revoked'
                    ).length
                },
                requestsByStatus: {
                    pending: systemData.activationRequests.filter(req => req.status === 'pending').length,
                    approved: systemData.activationRequests.filter(req => req.status === 'approved').length,
                    rejected: systemData.activationRequests.filter(req => req.status === 'rejected').length
                },
                expiringLicenses: systemData.issuedLicenses.filter(license => {
                    const now = new Date();
                    const expiryDate = new Date(license.expiryDate);
                    const thirtyDaysFromNow = new Date(now.getTime() + (30 * 24 * 60 * 60 * 1000));
                    return expiryDate > now && expiryDate <= thirtyDaysFromNow && license.status !== 'revoked';
                }).map(license => ({
                    clientName: license.clientName,
                    deviceId: license.deviceId,
                    expiryDate: license.expiryDate,
                    daysLeft: Math.ceil((new Date(license.expiryDate) - new Date()) / (1000 * 60 * 60 * 24))
                }))
            };

            const blob = new Blob([JSON.stringify(analyticsData, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `analytics-report-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            showNotification('تم تصدير تقرير الإحصائيات بنجاح', 'success');
        }

        // إضافة نشاط إلى السجل
        function logActivity(type, description, details = {}) {
            const activity = {
                id: 'ACT-' + Date.now(),
                timestamp: new Date().toISOString(),
                type: type, // request, license, system, error
                description: description,
                details: details,
                userAgent: navigator.userAgent.substr(0, 100)
            };

            systemData.activityLog.unshift(activity);

            // الاحتفاظ بآخر 1000 نشاط فقط
            if (systemData.activityLog.length > 1000) {
                systemData.activityLog = systemData.activityLog.slice(0, 1000);
            }

            saveSystemData();
        }

        // تحميل سجل النشاطات
        function loadActivityLog() {
            const tbody = document.getElementById('activityLogTable');
            tbody.innerHTML = '';

            let filteredActivities = [...systemData.activityLog];

            // تطبيق الفلاتر
            const typeFilter = document.getElementById('activityTypeFilter').value;
            const dateFromFilter = document.getElementById('dateFromFilter').value;
            const dateToFilter = document.getElementById('dateToFilter').value;

            if (typeFilter) {
                filteredActivities = filteredActivities.filter(activity => activity.type === typeFilter);
            }

            if (dateFromFilter) {
                const fromDate = new Date(dateFromFilter);
                filteredActivities = filteredActivities.filter(activity =>
                    new Date(activity.timestamp) >= fromDate
                );
            }

            if (dateToFilter) {
                const toDate = new Date(dateToFilter);
                toDate.setHours(23, 59, 59, 999);
                filteredActivities = filteredActivities.filter(activity =>
                    new Date(activity.timestamp) <= toDate
                );
            }

            if (filteredActivities.length === 0) {
                tbody.innerHTML = '<tr><td colspan="5" style="text-align: center; color: #6c757d;">لا توجد أنشطة</td></tr>';
                return;
            }

            // عرض آخر 100 نشاط
            const displayActivities = filteredActivities.slice(0, 100);

            displayActivities.forEach(activity => {
                const row = document.createElement('tr');
                const activityTypeIcon = getActivityTypeIcon(activity.type);
                const activityTypeColor = getActivityTypeColor(activity.type);

                row.innerHTML = `
                    <td style="font-size: 0.8rem;">${new Date(activity.timestamp).toLocaleString('ar-SA')}</td>
                    <td>
                        <span style="color: ${activityTypeColor};">
                            <i class="${activityTypeIcon}"></i>
                            ${getActivityTypeName(activity.type)}
                        </span>
                    </td>
                    <td>${activity.description}</td>
                    <td style="font-family: monospace; font-size: 0.8rem;">${activity.details.deviceId ? activity.details.deviceId.substr(0, 15) + '...' : 'النظام'}</td>
                    <td>
                        <button class="btn btn-primary" style="padding: 0.25rem 0.5rem; font-size: 0.8rem;" onclick="viewActivityDetails('${activity.id}')">
                            <i class="fas fa-eye"></i>
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });

            updateActivityStats();
        }

        // الحصول على أيقونة نوع النشاط
        function getActivityTypeIcon(type) {
            const icons = {
                'request': 'fas fa-paper-plane',
                'license': 'fas fa-key',
                'system': 'fas fa-cog',
                'error': 'fas fa-exclamation-triangle'
            };
            return icons[type] || 'fas fa-info-circle';
        }

        // الحصول على لون نوع النشاط
        function getActivityTypeColor(type) {
            const colors = {
                'request': '#3498db',
                'license': '#2ecc71',
                'system': '#f39c12',
                'error': '#e74c3c'
            };
            return colors[type] || '#6c757d';
        }

        // الحصول على اسم نوع النشاط
        function getActivityTypeName(type) {
            const names = {
                'request': 'طلب تفعيل',
                'license': 'ترخيص',
                'system': 'نظام',
                'error': 'خطأ'
            };
            return names[type] || 'غير محدد';
        }

        // عرض تفاصيل النشاط
        function viewActivityDetails(activityId) {
            const activity = systemData.activityLog.find(act => act.id === activityId);
            if (!activity) return;

            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.8);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
                direction: rtl;
            `;

            modal.innerHTML = `
                <div style="background: white; padding: 2rem; border-radius: 15px; max-width: 600px; width: 90%; max-height: 80vh; overflow-y: auto;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 2rem; border-bottom: 1px solid #eee; padding-bottom: 1rem;">
                        <h3 style="color: #2c3e50; margin: 0;">
                            <i class="${getActivityTypeIcon(activity.type)}" style="color: ${getActivityTypeColor(activity.type)};"></i>
                            تفاصيل النشاط
                        </h3>
                        <button onclick="this.closest('div').parentElement.remove()" style="background: #e74c3c; color: white; border: none; padding: 0.5rem; border-radius: 50%; cursor: pointer; width: 40px; height: 40px;">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;">
                        <div>
                            <h4 style="color: #3498db; margin-bottom: 1rem;">معلومات أساسية</h4>
                            <div style="margin-bottom: 1rem;">
                                <strong>معرف النشاط:</strong><br>
                                <span style="font-family: monospace; background: #f8f9fa; padding: 0.25rem; border-radius: 4px;">${activity.id}</span>
                            </div>
                            <div style="margin-bottom: 1rem;">
                                <strong>النوع:</strong><br>
                                <span style="color: ${getActivityTypeColor(activity.type)};">
                                    <i class="${getActivityTypeIcon(activity.type)}"></i>
                                    ${getActivityTypeName(activity.type)}
                                </span>
                            </div>
                            <div style="margin-bottom: 1rem;">
                                <strong>الوقت:</strong><br>
                                <span>${new Date(activity.timestamp).toLocaleString('ar-SA')}</span>
                            </div>
                        </div>

                        <div>
                            <h4 style="color: #3498db; margin-bottom: 1rem;">الوصف</h4>
                            <div style="background: #f8f9fa; padding: 1rem; border-radius: 4px; border-left: 4px solid #3498db;">
                                ${activity.description}
                            </div>
                        </div>
                    </div>

                    ${Object.keys(activity.details).length > 0 ? `
                        <div style="margin-top: 2rem;">
                            <h4 style="color: #3498db; margin-bottom: 1rem;">التفاصيل الإضافية</h4>
                            <div style="background: #f8f9fa; padding: 1rem; border-radius: 4px;">
                                <pre style="margin: 0; white-space: pre-wrap; font-family: monospace; font-size: 0.9rem;">${JSON.stringify(activity.details, null, 2)}</pre>
                            </div>
                        </div>
                    ` : ''}

                    <div style="margin-top: 2rem;">
                        <h4 style="color: #3498db; margin-bottom: 1rem;">معلومات تقنية</h4>
                        <div style="font-size: 0.8rem; color: #6c757d;">
                            <strong>User Agent:</strong> ${activity.userAgent}
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
        }

        // تحديث إحصائيات النشاطات
        function updateActivityStats() {
            const now = new Date();
            const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
            const weekAgo = new Date(today.getTime() - (7 * 24 * 60 * 60 * 1000));
            const monthAgo = new Date(today.getTime() - (30 * 24 * 60 * 60 * 1000));

            const todayCount = systemData.activityLog.filter(activity =>
                new Date(activity.timestamp) >= today
            ).length;

            const weekCount = systemData.activityLog.filter(activity =>
                new Date(activity.timestamp) >= weekAgo
            ).length;

            const monthCount = systemData.activityLog.filter(activity =>
                new Date(activity.timestamp) >= monthAgo
            ).length;

            const errorCount = systemData.activityLog.filter(activity =>
                activity.type === 'error'
            ).length;

            document.getElementById('todayActivities').textContent = todayCount;
            document.getElementById('weekActivities').textContent = weekCount;
            document.getElementById('monthActivities').textContent = monthCount;
            document.getElementById('errorActivities').textContent = errorCount;
        }

        // فلترة سجل النشاطات
        function filterActivityLog() {
            loadActivityLog();
        }

        // إعادة تعيين فلاتر النشاطات
        function resetActivityFilters() {
            document.getElementById('activityTypeFilter').value = '';
            document.getElementById('dateFromFilter').value = '';
            document.getElementById('dateToFilter').value = '';
            loadActivityLog();
        }

        // تحديث سجل النشاطات
        function refreshActivityLog() {
            loadActivityLog();
            showNotification('تم تحديث سجل النشاطات', 'success');
        }

        // مسح سجل النشاطات
        function clearActivityLog() {
            if (!confirm('هل أنت متأكد من مسح سجل النشاطات؟ هذا الإجراء لا يمكن التراجع عنه.')) return;

            systemData.activityLog = [];
            saveSystemData();
            loadActivityLog();
            showNotification('تم مسح سجل النشاطات', 'warning');
        }

        // تصدير سجل النشاطات
        function exportActivityLog() {
            if (systemData.activityLog.length === 0) {
                showNotification('لا توجد أنشطة للتصدير', 'warning');
                return;
            }

            const csvContent = [
                ['الوقت', 'النوع', 'الوصف', 'معرف الجهاز', 'التفاصيل'],
                ...systemData.activityLog.map(activity => [
                    new Date(activity.timestamp).toLocaleString('ar-SA'),
                    getActivityTypeName(activity.type),
                    activity.description,
                    activity.details.deviceId || 'النظام',
                    JSON.stringify(activity.details)
                ])
            ].map(row => row.join(',')).join('\n');

            const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `activity-log-${new Date().toISOString().split('T')[0]}.csv`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            showNotification('تم تصدير سجل النشاطات بنجاح', 'success');
        }

        // عرض التنبيهات
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type}`;
            notification.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
                ${message}
            `;

            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                min-width: 300px;
                max-width: 500px;
                transform: translateX(600px);
                transition: transform 0.3s ease;
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);

            setTimeout(() => {
                notification.style.transform = 'translateX(600px)';
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }, 4000);
        }
    </script>
</body>
</html>
