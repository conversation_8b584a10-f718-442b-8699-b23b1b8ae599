<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم الإدارية - مؤسسة وقود المستقبل</title>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            min-height: 100vh;
            direction: rtl;
            color: #e0e6ed;
            overflow-x: hidden;
        }

        /* الرأس الرئيسي */
        .main-header {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            padding: 1.5rem 2rem;
            box-shadow: 0 4px 20px rgba(231, 76, 60, 0.3);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1400px;
            margin: 0 auto;
        }

        .header-title {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .header-title h1 {
            font-size: 1.8rem;
            font-weight: 700;
        }

        .header-title .subtitle {
            font-size: 0.9rem;
            opacity: 0.9;
            margin-top: 0.2rem;
        }

        .header-stats {
            display: flex;
            gap: 2rem;
            align-items: center;
        }

        .stat-item {
            text-align: center;
        }

        .stat-value {
            font-size: 1.5rem;
            font-weight: bold;
            display: block;
        }

        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }

        /* الحاوية الرئيسية */
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        /* التبويبات */
        .tabs-container {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 1rem;
            margin-bottom: 2rem;
            backdrop-filter: blur(10px);
        }

        .tabs {
            display: flex;
            gap: 0.5rem;
            overflow-x: auto;
        }

        .tab-btn {
            background: transparent;
            color: #e0e6ed;
            border: none;
            padding: 1rem 1.5rem;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            white-space: nowrap;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
        }

        .tab-btn:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .tab-btn.active {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
        }

        /* محتوى التبويبات */
        .tab-content {
            display: none;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 2rem;
            backdrop-filter: blur(10px);
        }

        .tab-content.active {
            display: block;
        }

        /* الأزرار */
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 600;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        /* النماذج */
        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #e0e6ed;
        }

        .form-input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            color: #e0e6ed;
            font-size: 1rem;
        }

        .form-input:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
        }

        /* الجداول */
        .table-container {
            overflow-x: auto;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.05);
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            padding: 1rem;
            text-align: right;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .table th {
            background: rgba(255, 255, 255, 0.1);
            font-weight: 600;
            color: #3498db;
        }

        .table tr:hover {
            background: rgba(255, 255, 255, 0.05);
        }

        /* البطاقات */
        .card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .card-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #3498db;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        /* الحالات */
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .status-pending {
            background: #f39c12;
            color: white;
        }

        .status-approved {
            background: #2ecc71;
            color: white;
        }

        .status-rejected {
            background: #e74c3c;
            color: white;
        }

        .status-active {
            background: #2ecc71;
            color: white;
        }

        .status-expired {
            background: #e74c3c;
            color: white;
        }

        /* التنبيهات */
        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .alert-success {
            background: rgba(46, 204, 113, 0.2);
            border: 1px solid #2ecc71;
            color: #2ecc71;
        }

        .alert-warning {
            background: rgba(243, 156, 18, 0.2);
            border: 1px solid #f39c12;
            color: #f39c12;
        }

        .alert-danger {
            background: rgba(231, 76, 60, 0.2);
            border: 1px solid #e74c3c;
            color: #e74c3c;
        }

        /* تحسينات الاستجابة */
        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }

            .header-content {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }

            .header-stats {
                flex-direction: row;
                gap: 1rem;
            }

            .tabs {
                flex-direction: column;
            }

            .tab-content {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- الرأس الرئيسي -->
    <header class="main-header">
        <div class="header-content">
            <div class="header-title">
                <i class="fas fa-shield-alt" style="font-size: 2rem;"></i>
                <div>
                    <h1>لوحة التحكم الإدارية</h1>
                    <div class="subtitle">مؤسسة وقود المستقبل - إدارة التراخيص</div>
                </div>
            </div>
            
            <div class="header-stats">
                <div class="stat-item">
                    <span class="stat-value" id="totalRequests">0</span>
                    <span class="stat-label">طلبات التفعيل</span>
                </div>
                <div class="stat-item">
                    <span class="stat-value" id="activeLicenses">0</span>
                    <span class="stat-label">التراخيص النشطة</span>
                </div>
                <div class="stat-item">
                    <span class="stat-value" id="pendingRequests">0</span>
                    <span class="stat-label">في الانتظار</span>
                </div>
            </div>
        </div>
    </header>

    <!-- الحاوية الرئيسية -->
    <div class="container">
        <!-- التبويبات -->
        <div class="tabs-container">
            <div class="tabs">
                <button class="tab-btn active" data-tab="requests">
                    <i class="fas fa-inbox"></i>
                    طلبات التفعيل
                </button>
                <button class="tab-btn" data-tab="licenses">
                    <i class="fas fa-key"></i>
                    إدارة التراخيص
                </button>
                <button class="tab-btn" data-tab="generator">
                    <i class="fas fa-plus-circle"></i>
                    مولد التراخيص
                </button>
                <button class="tab-btn" data-tab="settings">
                    <i class="fas fa-cog"></i>
                    الإعدادات
                </button>
            </div>
        </div>

        <!-- محتوى التبويبات -->

        <!-- تبويب طلبات التفعيل -->
        <div class="tab-content active" id="requests-tab">
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">
                        <i class="fas fa-inbox"></i>
                        طلبات التفعيل
                    </h2>
                    <div>
                        <button class="btn btn-primary" onclick="refreshRequests()">
                            <i class="fas fa-sync-alt"></i>
                            تحديث
                        </button>
                        <button class="btn btn-success" onclick="exportRequests()">
                            <i class="fas fa-download"></i>
                            تصدير
                        </button>
                    </div>
                </div>

                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>معرف الطلب</th>
                                <th>الاسم</th>
                                <th>الهاتف</th>
                                <th>معرف الجهاز</th>
                                <th>التاريخ</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="requestsTableBody">
                            <!-- سيتم ملؤها بواسطة JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- تبويب إدارة التراخيص -->
        <div class="tab-content" id="licenses-tab">
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">
                        <i class="fas fa-key"></i>
                        التراخيص المُصدرة
                    </h2>
                    <div>
                        <button class="btn btn-primary" onclick="refreshLicenses()">
                            <i class="fas fa-sync-alt"></i>
                            تحديث
                        </button>
                        <button class="btn btn-success" onclick="exportLicenses()">
                            <i class="fas fa-download"></i>
                            تصدير
                        </button>
                    </div>
                </div>

                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>معرف الترخيص</th>
                                <th>معرف الجهاز</th>
                                <th>العميل</th>
                                <th>تاريخ الإصدار</th>
                                <th>تاريخ الانتهاء</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="licensesTableBody">
                            <!-- سيتم ملؤها بواسطة JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- تبويب مولد التراخيص -->
        <div class="tab-content" id="generator-tab">
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">
                        <i class="fas fa-plus-circle"></i>
                        مولد التراخيص الجديد
                    </h2>
                </div>

                <form id="licenseGeneratorForm">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;">
                        <div>
                            <div class="form-group">
                                <label class="form-label">اسم العميل:</label>
                                <input type="text" class="form-input" id="clientName" required>
                            </div>

                            <div class="form-group">
                                <label class="form-label">معرف الجهاز:</label>
                                <input type="text" class="form-input" id="deviceIdInput" required
                                       placeholder="FFC-XXXX-XXXX-XXXX-XXXX">
                            </div>

                            <div class="form-group">
                                <label class="form-label">مدة الترخيص:</label>
                                <select class="form-input" id="licenseDuration">
                                    <option value="30">30 يوم</option>
                                    <option value="90">90 يوم</option>
                                    <option value="180">180 يوم</option>
                                    <option value="365" selected>سنة واحدة</option>
                                    <option value="730">سنتان</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label class="form-label">ملاحظات:</label>
                                <textarea class="form-input" id="licenseNotes" rows="3"></textarea>
                            </div>

                            <button type="submit" class="btn btn-success" style="width: 100%;">
                                <i class="fas fa-key"></i>
                                توليد الترخيص
                            </button>
                        </div>

                        <div>
                            <div class="form-group">
                                <label class="form-label">الترخيص المُولد:</label>
                                <textarea class="form-input" id="generatedLicense" rows="8" readonly
                                          placeholder="سيظهر الترخيص هنا بعد التوليد..."></textarea>
                            </div>

                            <div style="display: flex; gap: 1rem;">
                                <button type="button" class="btn btn-primary" onclick="copyLicense()" style="flex: 1;">
                                    <i class="fas fa-copy"></i>
                                    نسخ الترخيص
                                </button>
                                <button type="button" class="btn btn-warning" onclick="saveLicense()" style="flex: 1;">
                                    <i class="fas fa-save"></i>
                                    حفظ الترخيص
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- تبويب الإعدادات -->
        <div class="tab-content" id="settings-tab">
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">
                        <i class="fas fa-cog"></i>
                        إعدادات النظام
                    </h2>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;">
                    <div>
                        <h3 style="color: #3498db; margin-bottom: 1rem;">إعدادات التراخيص</h3>

                        <div class="form-group">
                            <label class="form-label">مدة الترخيص الافتراضية (بالأيام):</label>
                            <input type="number" class="form-input" id="defaultDuration" value="365" min="1" max="3650">
                        </div>

                        <div class="form-group">
                            <label class="form-label">بادئة التراخيص:</label>
                            <input type="text" class="form-input" id="licensePrefix" value="FFC" maxlength="5">
                        </div>

                        <button class="btn btn-primary" onclick="saveSettings()">
                            <i class="fas fa-save"></i>
                            حفظ الإعدادات
                        </button>
                    </div>

                    <div>
                        <h3 style="color: #3498db; margin-bottom: 1rem;">أدوات النظام</h3>

                        <div style="display: flex; flex-direction: column; gap: 1rem;">
                            <button class="btn btn-success" onclick="exportAllData()">
                                <i class="fas fa-download"></i>
                                تصدير جميع البيانات
                            </button>

                            <button class="btn btn-warning" onclick="importData()">
                                <i class="fas fa-upload"></i>
                                استيراد البيانات
                            </button>

                            <button class="btn btn-danger" onclick="clearAllData()">
                                <i class="fas fa-trash"></i>
                                مسح جميع البيانات
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // بيانات النظام
        let systemData = {
            activationRequests: [],
            issuedLicenses: [],
            settings: {
                defaultDuration: 365,
                licensePrefix: 'FFC'
            }
        };

        // تهيئة النظام
        document.addEventListener('DOMContentLoaded', function() {
            loadSystemData();
            setupEventListeners();
            updateStatistics();
            loadRequestsTable();
            loadLicensesTable();

            console.log('✅ تم تهيئة لوحة التحكم الإدارية بنجاح');
        });

        // تحميل بيانات النظام
        function loadSystemData() {
            // تحميل طلبات التفعيل
            const requests = localStorage.getItem('developerPanel_activationRequests');
            if (requests) {
                systemData.activationRequests = JSON.parse(requests);
            }

            // تحميل التراخيص المُصدرة
            const licenses = localStorage.getItem('developerPanel_issuedLicenses');
            if (licenses) {
                systemData.issuedLicenses = JSON.parse(licenses);
            }

            // تحميل الإعدادات
            const settings = localStorage.getItem('developerPanel_settings');
            if (settings) {
                systemData.settings = { ...systemData.settings, ...JSON.parse(settings) };
            }
        }

        // حفظ بيانات النظام
        function saveSystemData() {
            localStorage.setItem('developerPanel_activationRequests', JSON.stringify(systemData.activationRequests));
            localStorage.setItem('developerPanel_issuedLicenses', JSON.stringify(systemData.issuedLicenses));
            localStorage.setItem('developerPanel_settings', JSON.stringify(systemData.settings));
        }

        // إعداد مستمعي الأحداث
        function setupEventListeners() {
            // التبويبات
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const tabName = this.dataset.tab;
                    switchTab(tabName);
                });
            });

            // نموذج توليد الترخيص
            document.getElementById('licenseGeneratorForm').addEventListener('submit', handleLicenseGeneration);
        }

        // تبديل التبويبات
        function switchTab(tabName) {
            // إخفاء جميع التبويبات
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });

            // إزالة الحالة النشطة من جميع الأزرار
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            // إظهار التبويب المحدد
            document.getElementById(tabName + '-tab').classList.add('active');
            document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

            // تحديث البيانات حسب التبويب
            if (tabName === 'requests') {
                loadRequestsTable();
            } else if (tabName === 'licenses') {
                loadLicensesTable();
            }
        }

        // تحديث الإحصائيات
        function updateStatistics() {
            const totalRequests = systemData.activationRequests.length;
            const pendingRequests = systemData.activationRequests.filter(req => req.status === 'pending').length;
            const activeLicenses = systemData.issuedLicenses.filter(license => {
                return new Date(license.expiryDate) > new Date();
            }).length;

            document.getElementById('totalRequests').textContent = totalRequests;
            document.getElementById('pendingRequests').textContent = pendingRequests;
            document.getElementById('activeLicenses').textContent = activeLicenses;
        }

        // تحميل جدول طلبات التفعيل
        function loadRequestsTable() {
            const tbody = document.getElementById('requestsTableBody');
            tbody.innerHTML = '';

            if (systemData.activationRequests.length === 0) {
                tbody.innerHTML = '<tr><td colspan="7" style="text-align: center; color: #6c757d;">لا توجد طلبات تفعيل</td></tr>';
                return;
            }

            systemData.activationRequests.forEach(request => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td style="font-family: monospace; font-size: 0.8rem;">${request.id}</td>
                    <td>${request.firstName} ${request.lastName}</td>
                    <td>${request.phone}</td>
                    <td style="font-family: monospace; font-size: 0.8rem;">${request.deviceId.substr(0, 20)}...</td>
                    <td>${new Date(request.timestamp).toLocaleDateString('ar-SA')}</td>
                    <td><span class="status-badge status-${request.status}">${getStatusText(request.status)}</span></td>
                    <td>
                        <div style="display: flex; gap: 0.5rem;">
                            ${request.status === 'pending' ? `
                                <button class="btn btn-success" style="padding: 0.25rem 0.5rem; font-size: 0.8rem;" onclick="approveRequest('${request.id}')">
                                    <i class="fas fa-check"></i>
                                </button>
                                <button class="btn btn-danger" style="padding: 0.25rem 0.5rem; font-size: 0.8rem;" onclick="rejectRequest('${request.id}')">
                                    <i class="fas fa-times"></i>
                                </button>
                            ` : ''}
                            <button class="btn btn-primary" style="padding: 0.25rem 0.5rem; font-size: 0.8rem;" onclick="viewRequestDetails('${request.id}')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // تحميل جدول التراخيص
        function loadLicensesTable() {
            const tbody = document.getElementById('licensesTableBody');
            tbody.innerHTML = '';

            if (systemData.issuedLicenses.length === 0) {
                tbody.innerHTML = '<tr><td colspan="7" style="text-align: center; color: #6c757d;">لا توجد تراخيص مُصدرة</td></tr>';
                return;
            }

            systemData.issuedLicenses.forEach(license => {
                const isExpired = new Date(license.expiryDate) < new Date();
                const status = isExpired ? 'expired' : 'active';

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td style="font-family: monospace; font-size: 0.8rem;">${license.id}</td>
                    <td style="font-family: monospace; font-size: 0.8rem;">${license.deviceId.substr(0, 20)}...</td>
                    <td>${license.clientName}</td>
                    <td>${new Date(license.issuedDate).toLocaleDateString('ar-SA')}</td>
                    <td>${new Date(license.expiryDate).toLocaleDateString('ar-SA')}</td>
                    <td><span class="status-badge status-${status}">${status === 'active' ? 'نشط' : 'منتهي'}</span></td>
                    <td>
                        <div style="display: flex; gap: 0.5rem;">
                            <button class="btn btn-primary" style="padding: 0.25rem 0.5rem; font-size: 0.8rem;" onclick="viewLicenseDetails('${license.id}')">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-warning" style="padding: 0.25rem 0.5rem; font-size: 0.8rem;" onclick="renewLicense('${license.id}')">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                            <button class="btn btn-danger" style="padding: 0.25rem 0.5rem; font-size: 0.8rem;" onclick="revokeLicense('${license.id}')">
                                <i class="fas fa-ban"></i>
                            </button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // الحصول على نص الحالة
        function getStatusText(status) {
            const statusTexts = {
                'pending': 'في الانتظار',
                'approved': 'مُوافق عليه',
                'rejected': 'مرفوض'
            };
            return statusTexts[status] || status;
        }

        // الموافقة على طلب التفعيل
        function approveRequest(requestId) {
            const request = systemData.activationRequests.find(req => req.id === requestId);
            if (!request) return;

            // تحديث حالة الطلب
            request.status = 'approved';
            request.approvedAt = new Date().toISOString();

            // ملء نموذج توليد الترخيص
            document.getElementById('clientName').value = `${request.firstName} ${request.lastName}`;
            document.getElementById('deviceIdInput').value = request.deviceId;

            // التبديل إلى تبويب مولد التراخيص
            switchTab('generator');

            // حفظ البيانات
            saveSystemData();
            updateStatistics();
            loadRequestsTable();

            showNotification('تم الموافقة على الطلب. يمكنك الآن توليد الترخيص.', 'success');
        }

        // رفض طلب التفعيل
        function rejectRequest(requestId) {
            if (!confirm('هل أنت متأكد من رفض هذا الطلب؟')) return;

            const request = systemData.activationRequests.find(req => req.id === requestId);
            if (!request) return;

            request.status = 'rejected';
            request.rejectedAt = new Date().toISOString();

            saveSystemData();
            updateStatistics();
            loadRequestsTable();

            showNotification('تم رفض الطلب.', 'warning');
        }

        // عرض تفاصيل الطلب
        function viewRequestDetails(requestId) {
            const request = systemData.activationRequests.find(req => req.id === requestId);
            if (!request) return;

            const details = `
معرف الطلب: ${request.id}
الاسم: ${request.firstName} ${request.lastName}
الهاتف: ${request.phone}
اسم المؤسسة: ${request.businessName || 'غير محدد'}
معرف الجهاز: ${request.deviceId}
تاريخ الطلب: ${new Date(request.timestamp).toLocaleString('ar-SA')}
الحالة: ${getStatusText(request.status)}
الملاحظات: ${request.notes || 'لا توجد ملاحظات'}
            `;

            alert(details);
        }

        // معالجة توليد الترخيص
        function handleLicenseGeneration(event) {
            event.preventDefault();

            const clientName = document.getElementById('clientName').value.trim();
            const deviceId = document.getElementById('deviceIdInput').value.trim();
            const duration = parseInt(document.getElementById('licenseDuration').value);
            const notes = document.getElementById('licenseNotes').value.trim();

            if (!clientName || !deviceId) {
                showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
                return;
            }

            // التحقق من صيغة معرف الجهاز
            if (!deviceId.match(/^FFC-[A-F0-9]{4}-[A-F0-9]{4}-[A-F0-9]{4}-[A-F0-9]{4}$/)) {
                showNotification('صيغة معرف الجهاز غير صحيحة', 'error');
                return;
            }

            // توليد الترخيص
            const license = generateLicense(clientName, deviceId, duration, notes);

            // عرض الترخيص
            document.getElementById('generatedLicense').value = license.licenseKey;

            showNotification('تم توليد الترخيص بنجاح!', 'success');
        }

        // توليد الترخيص
        function generateLicense(clientName, deviceId, durationDays, notes) {
            const now = new Date();
            const expiryDate = new Date(now.getTime() + (durationDays * 24 * 60 * 60 * 1000));

            const licenseData = {
                id: 'LIC-' + Date.now(),
                clientName: clientName,
                deviceId: deviceId,
                issuedDate: now.toISOString(),
                expiryDate: expiryDate.toISOString(),
                duration: durationDays,
                notes: notes,
                type: 'FFC_LICENSE',
                version: '2.0'
            };

            // تشفير الترخيص
            const licenseKey = btoa(JSON.stringify({
                deviceId: deviceId,
                expiryDate: expiryDate.toISOString(),
                type: 'FFC_LICENSE',
                issued: now.toISOString()
            }));

            licenseData.licenseKey = licenseKey;

            // حفظ الترخيص
            systemData.issuedLicenses.push(licenseData);
            saveSystemData();
            updateStatistics();
            loadLicensesTable();

            return licenseData;
        }

        // نسخ الترخيص
        function copyLicense() {
            const licenseText = document.getElementById('generatedLicense').value;
            if (!licenseText) {
                showNotification('لا يوجد ترخيص للنسخ', 'warning');
                return;
            }

            navigator.clipboard.writeText(licenseText).then(() => {
                showNotification('تم نسخ الترخيص إلى الحافظة', 'success');
            }).catch(() => {
                // طريقة بديلة
                const textArea = document.createElement('textarea');
                textArea.value = licenseText;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showNotification('تم نسخ الترخيص', 'success');
            });
        }

        // حفظ الترخيص
        function saveLicense() {
            const licenseText = document.getElementById('generatedLicense').value;
            if (!licenseText) {
                showNotification('لا يوجد ترخيص للحفظ', 'warning');
                return;
            }

            const blob = new Blob([licenseText], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `license-${Date.now()}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            showNotification('تم حفظ الترخيص كملف', 'success');
        }

        // وظائف التحديث
        function refreshRequests() {
            loadSystemData();
            loadRequestsTable();
            updateStatistics();
            showNotification('تم تحديث طلبات التفعيل', 'success');
        }

        function refreshLicenses() {
            loadSystemData();
            loadLicensesTable();
            updateStatistics();
            showNotification('تم تحديث التراخيص', 'success');
        }

        // عرض التنبيهات
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type}`;
            notification.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
                ${message}
            `;

            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                min-width: 300px;
                max-width: 500px;
                transform: translateX(600px);
                transition: transform 0.3s ease;
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);

            setTimeout(() => {
                notification.style.transform = 'translateX(600px)';
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }, 4000);
        }
    </script>
</body>
</html>
