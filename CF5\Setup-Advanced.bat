@echo off
chcp 65001 >nul
title معالج تثبيت مؤسسة وقود المستقبل - Future Fuel Setup Wizard

:: متغيرات النظام
set APP_NAME=مؤسسة وقود المستقبل
set APP_NAME_EN=Future Fuel Corporation
set VERSION=2.2.0
set DEFAULT_DIR=%USERPROFILE%\Desktop\Future Fuel
set INSTALL_DIR=%DEFAULT_DIR%

:: ألوان النص
for /F %%a in ('echo prompt $E ^| cmd') do set "ESC=%%a"
set "RED=%ESC%[91m"
set "GREEN=%ESC%[92m"
set "YELLOW=%ESC%[93m"
set "BLUE=%ESC%[94m"
set "MAGENTA=%ESC%[95m"
set "CYAN=%ESC%[96m"
set "WHITE=%ESC%[97m"
set "RESET=%ESC%[0m"

:WELCOME
cls
echo.
echo %CYAN%================================================================================
echo                    🚀 معالج تثبيت مؤسسة وقود المستقبل
echo                    Future Fuel Corporation Setup Wizard
echo ================================================================================%RESET%
echo.
echo %WHITE%مرحباً بكم في معالج تثبيت %APP_NAME%%RESET%
echo.
echo %YELLOW%الإصدار: %VERSION%%RESET%
echo %YELLOW%المطور: Future Fuel Corporation%RESET%
echo %YELLOW%تاريخ الإصدار: 2025-07-08%RESET%
echo.
echo %GREEN%هذا المعالج سيقوم بتثبيت البرنامج على جهازكم خطوة بخطوة%RESET%
echo.
echo %BLUE%الميزات المتضمنة:%RESET%
echo   ✅ إدارة العملاء والمركبات
echo   ✅ إصدار وتجديد بطاقات الغاز  
echo   ✅ جدولة المواعيد والتذكيرات
echo   ✅ إدارة الديون والمدفوعات
echo   ✅ التقارير والإحصائيات الشاملة
echo   ✅ النسخ الاحتياطية التلقائية
echo.
echo %RED%⚠️  تأكد من إغلاق جميع البرامج الأخرى قبل المتابعة%RESET%
echo.
set /p continue="هل تريد المتابعة؟ (y/n): "
if /i not "%continue%"=="y" goto :EXIT

:LICENSE
cls
echo.
echo %CYAN%================================================================================
echo                              📋 اتفاقية الترخيص
echo                              License Agreement  
echo ================================================================================%RESET%
echo.
echo %WHITE%اتفاقية ترخيص استخدام البرنامج:%RESET%
echo.
echo %YELLOW%1. هذا البرنامج مرخص للاستخدام الشخصي أو التجاري%RESET%
echo %YELLOW%2. يُمنع نسخ أو توزيع البرنامج بدون إذن مكتوب%RESET%
echo %YELLOW%3. الشركة غير مسؤولة عن أي أضرار ناتجة عن سوء الاستخدام%RESET%
echo %YELLOW%4. يحق للشركة تحديث البرنامج وإضافة ميزات جديدة%RESET%
echo %YELLOW%5. الدعم الفني متاح للعملاء المرخصين فقط%RESET%
echo.
echo %GREEN%بالمتابعة، أنت توافق على شروط الاستخدام أعلاه%RESET%
echo.
set /p accept="هل توافق على اتفاقية الترخيص؟ (y/n): "
if /i not "%accept%"=="y" goto :EXIT

:INSTALL_PATH
cls
echo.
echo %CYAN%================================================================================
echo                              📁 اختيار مجلد التثبيت
echo                              Choose Installation Directory
echo ================================================================================%RESET%
echo.
echo %WHITE%اختر مجلد التثبيت:%RESET%
echo.
echo %YELLOW%المجلد الافتراضي: %DEFAULT_DIR%%RESET%
echo.
echo %GREEN%1. استخدام المجلد الافتراضي (موصى به)%RESET%
echo %BLUE%2. اختيار مجلد مخصص%RESET%
echo.
set /p path_choice="اختر (1 أو 2): "

if "%path_choice%"=="2" (
    echo.
    echo %YELLOW%أدخل المسار الكامل للمجلد:%RESET%
    set /p INSTALL_DIR="المسار: "
    if "!INSTALL_DIR!"=="" set INSTALL_DIR=%DEFAULT_DIR%
) else (
    set INSTALL_DIR=%DEFAULT_DIR%
)

echo.
echo %GREEN%سيتم التثبيت في: %INSTALL_DIR%%RESET%
echo.
set /p confirm_path="هل هذا صحيح؟ (y/n): "
if /i not "%confirm_path%"=="y" goto :INSTALL_PATH

:COMPONENTS
cls
echo.
echo %CYAN%================================================================================
echo                              🔧 اختيار المكونات
echo                              Select Components
echo ================================================================================%RESET%
echo.
echo %WHITE%اختر المكونات المراد تثبيتها:%RESET%
echo.
echo %GREEN%✅ 1. التطبيق الأساسي (مطلوب)%RESET%
echo %YELLOW%☐ 2. اختصارات سطح المكتب%RESET%
echo %YELLOW%☐ 3. اختصارات قائمة ابدأ%RESET%
echo %YELLOW%☐ 4. ملفات المساعدة والدليل%RESET%
echo %YELLOW%☐ 5. أمثلة وبيانات تجريبية%RESET%
echo.
echo %BLUE%اكتب أرقام المكونات المطلوبة مفصولة بمسافات (مثال: 2 3 4)%RESET%
echo %BLUE%أو اضغط Enter لتثبيت جميع المكونات%RESET%
echo.
set /p components="المكونات المطلوبة: "
if "%components%"=="" set components=2 3 4 5

:INSTALLATION
cls
echo.
echo %CYAN%================================================================================
echo                              🔄 جاري التثبيت
echo                              Installing...
echo ================================================================================%RESET%
echo.
echo %WHITE%جاري تثبيت %APP_NAME%...%RESET%
echo %YELLOW%المجلد: %INSTALL_DIR%%RESET%
echo.

:: بدء التثبيت الفعلي
call :INSTALL_STEP "فحص المتطلبات" :CHECK_REQUIREMENTS
call :INSTALL_STEP "إنشاء مجلد التثبيت" :CREATE_DIRECTORY  
call :INSTALL_STEP "نسخ ملفات التطبيق" :COPY_FILES
call :INSTALL_STEP "تثبيت المكونات الإضافية" :INSTALL_COMPONENTS
call :INSTALL_STEP "إنشاء الاختصارات" :CREATE_SHORTCUTS
call :INSTALL_STEP "تسجيل البرنامج" :REGISTER_PROGRAM
call :INSTALL_STEP "إنشاء ملف إلغاء التثبيت" :CREATE_UNINSTALLER
call :INSTALL_STEP "التحقق من التثبيت" :VERIFY_INSTALLATION

goto :SUCCESS

:INSTALL_STEP
echo.
echo %BLUE%[%~1]%RESET%
call %~2
if %errorlevel% neq 0 (
    echo %RED%❌ فشل في: %~1%RESET%
    goto :ERROR
) else (
    echo %GREEN%✅ تم بنجاح: %~1%RESET%
)
exit /b 0

:CHECK_REQUIREMENTS
:: فحص المساحة المتاحة
for /f "tokens=3" %%a in ('dir /-c "%USERPROFILE%" ^| find "bytes free"') do set FREE_SPACE=%%a
if %FREE_SPACE% lss 104857600 (
    echo %RED%مساحة غير كافية. مطلوب 100 MB على الأقل%RESET%
    exit /b 1
)
exit /b 0

:CREATE_DIRECTORY
if not exist "%INSTALL_DIR%" (
    mkdir "%INSTALL_DIR%" 2>nul
    if not exist "%INSTALL_DIR%" exit /b 1
)
exit /b 0

:COPY_FILES
xcopy /E /I /Y "customer-package\*" "%INSTALL_DIR%\" >nul 2>&1
if %errorlevel% neq 0 exit /b 1

if exist "shared" (
    xcopy /E /I /Y "shared\*" "%INSTALL_DIR%\shared\" >nul 2>&1
)
exit /b 0

:INSTALL_COMPONENTS
:: تثبيت المكونات الإضافية حسب الاختيار
if exist "modules" (
    xcopy /E /I /Y "modules\*" "%INSTALL_DIR%\modules\" >nul 2>&1
)

:: نسخ ملفات المساعدة
if "%components%" neq "" (
    echo %components% | find "4" >nul
    if !errorlevel! equ 0 (
        if exist "docs" xcopy /E /I /Y "docs\*" "%INSTALL_DIR%\docs\" >nul 2>&1
        if exist "*.md" copy "*.md" "%INSTALL_DIR%\" >nul 2>&1
    )
    
    :: نسخ البيانات التجريبية
    echo %components% | find "5" >nul
    if !errorlevel! equ 0 (
        if exist "sample-data" xcopy /E /I /Y "sample-data\*" "%INSTALL_DIR%\sample-data\" >nul 2>&1
    )
)
exit /b 0

:CREATE_SHORTCUTS
:: إنشاء اختصار سطح المكتب
echo %components% | find "2" >nul
if %errorlevel% equ 0 (
    powershell -Command "
    $WshShell = New-Object -comObject WScript.Shell;
    $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\مؤسسة وقود المستقبل.lnk');
    $Shortcut.TargetPath = '%INSTALL_DIR%\app\index.html';
    $Shortcut.WorkingDirectory = '%INSTALL_DIR%\app';
    $Shortcut.Description = '%APP_NAME% - %VERSION%';
    $Shortcut.Save()
    " >nul 2>&1
)

:: إنشاء اختصار قائمة ابدأ
echo %components% | find "3" >nul
if %errorlevel% equ 0 (
    set START_MENU_DIR=%APPDATA%\Microsoft\Windows\Start Menu\Programs\Future Fuel
    if not exist "!START_MENU_DIR!" mkdir "!START_MENU_DIR!" >nul 2>&1
    
    powershell -Command "
    $WshShell = New-Object -comObject WScript.Shell;
    $Shortcut = $WshShell.CreateShortcut('!START_MENU_DIR!\%APP_NAME%.lnk');
    $Shortcut.TargetPath = '%INSTALL_DIR%\app\index.html';
    $Shortcut.WorkingDirectory = '%INSTALL_DIR%\app';
    $Shortcut.Description = '%APP_NAME% - %VERSION%';
    $Shortcut.Save()
    " >nul 2>&1
)
exit /b 0

:REGISTER_PROGRAM
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Uninstall\FutureFuel" /v "DisplayName" /t REG_SZ /d "%APP_NAME%" /f >nul 2>&1
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Uninstall\FutureFuel" /v "DisplayVersion" /t REG_SZ /d "%VERSION%" /f >nul 2>&1
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Uninstall\FutureFuel" /v "InstallLocation" /t REG_SZ /d "%INSTALL_DIR%" /f >nul 2>&1
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Uninstall\FutureFuel" /v "UninstallString" /t REG_SZ /d "%INSTALL_DIR%\Uninstall.bat" /f >nul 2>&1
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Uninstall\FutureFuel" /v "Publisher" /t REG_SZ /d "Future Fuel Corporation" /f >nul 2>&1
exit /b 0

:CREATE_UNINSTALLER
(
echo @echo off
echo title إلغاء تثبيت %APP_NAME%
echo.
echo هل أنت متأكد من إلغاء تثبيت %APP_NAME%؟
echo.
pause
echo.
echo جاري إلغاء التثبيت...
echo rmdir /s /q "%INSTALL_DIR%"
echo del "%USERPROFILE%\Desktop\مؤسسة وقود المستقبل.lnk" 2^>nul
echo reg delete "HKCU\Software\Microsoft\Windows\CurrentVersion\Uninstall\FutureFuel" /f 2^>nul
echo echo تم إلغاء التثبيت بنجاح
echo pause
) > "%INSTALL_DIR%\Uninstall.bat"
exit /b 0

:VERIFY_INSTALLATION
if not exist "%INSTALL_DIR%\app\index.html" exit /b 1
exit /b 0

:SUCCESS
cls
echo.
echo %GREEN%================================================================================
echo                           ✅ تم التثبيت بنجاح!
echo                           Installation Completed Successfully!
echo ================================================================================%RESET%
echo.
echo %WHITE%🎉 تهانينا! تم تثبيت %APP_NAME% بنجاح%RESET%
echo.
echo %YELLOW%📁 مجلد التثبيت: %INSTALL_DIR%%RESET%
echo %YELLOW%🖥️  اختصار سطح المكتب: مؤسسة وقود المستقبل%RESET%
echo %YELLOW%📋 قائمة ابدأ: البرامج > Future Fuel%RESET%
echo.
echo %CYAN%================================================================================
echo                              🚀 بدء الاستخدام
echo ================================================================================%RESET%
echo.
echo %GREEN%لبدء استخدام البرنامج:%RESET%
echo   1️⃣  انقر نقراً مزدوجاً على اختصار سطح المكتب
echo   2️⃣  أو اذهب إلى قائمة ابدأ > البرامج > Future Fuel
echo.
echo %BLUE%🔑 ستحتاج إلى كود الترخيص لتسجيل الدخول%RESET%
echo %BLUE%📧 للحصول على الترخيص: <EMAIL>%RESET%
echo.
set /p launch="هل تريد تشغيل البرنامج الآن؟ (y/n): "
if /i "%launch%"=="y" (
    start "" "%INSTALL_DIR%\app\index.html"
    echo %GREEN%تم تشغيل البرنامج بنجاح!%RESET%
)
echo.
echo %WHITE%شكراً لاختياركم مؤسسة وقود المستقبل!%RESET%
goto :END

:ERROR
echo.
echo %RED%================================================================================
echo                              ❌ فشل التثبيت
echo                              Installation Failed
echo ================================================================================%RESET%
echo.
echo %RED%حدث خطأ أثناء التثبيت. يرجى المحاولة مرة أخرى%RESET%
echo.
echo %YELLOW%نصائح لحل المشكلة:%RESET%
echo   • تأكد من تشغيل البرنامج كمدير
echo   • تأكد من وجود مساحة كافية على القرص
echo   • أغلق جميع البرامج الأخرى
echo   • تأكد من وجود ملفات التطبيق
echo.
goto :END

:EXIT
echo.
echo %YELLOW%تم إلغاء التثبيت بواسطة المستخدم%RESET%
goto :END

:END
echo.
pause
exit
