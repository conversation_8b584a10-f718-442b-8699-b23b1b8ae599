<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص النظام الرئيسي</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success {
            background: #e8f5e8;
            color: #2e7d32;
            border: 1px solid #4caf50;
        }
        .error {
            background: #ffebee;
            color: #c62828;
            border: 1px solid #f44336;
        }
        .info {
            background: #e3f2fd;
            color: #1976d2;
            border: 1px solid #2196f3;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffc107;
        }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 12px;
        }
        button {
            background: #2196f3;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1976d2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 تشخيص النظام الرئيسي</h1>
        
        <div id="diagnostics">
            <div class="status info">
                <h3>🔄 جاري فحص النظام...</h3>
            </div>
        </div>
        
        <div style="margin-top: 20px;">
            <button onclick="runDiagnostics()">🔄 إعادة الفحص</button>
            <button onclick="clearData()">🗑️ مسح البيانات</button>
            <button onclick="testDashboard()">🧪 اختبار dashboard.html</button>
            <button onclick="goToLogin()">🔐 العودة لتسجيل الدخول</button>
        </div>
        
        <div id="details" style="margin-top: 30px;"></div>
    </div>

    <script src="database-sync.js"></script>
    <script>
        function runDiagnostics() {
            const diagnosticsDiv = document.getElementById('diagnostics');
            const detailsDiv = document.getElementById('details');
            
            let results = [];
            let details = [];
            
            // فحص localStorage
            try {
                const testKey = 'test_' + Date.now();
                localStorage.setItem(testKey, 'test');
                localStorage.removeItem(testKey);
                results.push({ type: 'success', message: '✅ localStorage يعمل بشكل صحيح' });
            } catch (error) {
                results.push({ type: 'error', message: '❌ مشكلة في localStorage: ' + error.message });
            }
            
            // فحص الجلسة الحالية
            const currentSession = localStorage.getItem('currentSession');
            if (currentSession) {
                try {
                    const session = JSON.parse(currentSession);
                    results.push({ type: 'success', message: '✅ توجد جلسة نشطة' });
                    details.push(`<h3>📱 بيانات الجلسة الحالية:</h3><pre>${JSON.stringify(session, null, 2)}</pre>`);
                } catch (error) {
                    results.push({ type: 'error', message: '❌ خطأ في قراءة بيانات الجلسة: ' + error.message });
                }
            } else {
                results.push({ type: 'warning', message: '⚠️ لا توجد جلسة نشطة' });
            }
            
            // فحص التراخيص
            const licenses = localStorage.getItem('validLicenses');
            if (licenses) {
                try {
                    const licensesData = JSON.parse(licenses);
                    results.push({ type: 'success', message: `✅ توجد ${licensesData.length} تراخيص` });
                    details.push(`<h3>🔑 التراخيص المتاحة:</h3><pre>${JSON.stringify(licensesData, null, 2)}</pre>`);
                } catch (error) {
                    results.push({ type: 'error', message: '❌ خطأ في قراءة التراخيص: ' + error.message });
                }
            } else {
                results.push({ type: 'warning', message: '⚠️ لا توجد تراخيص محفوظة' });
            }
            
            // فحص قاعدة البيانات المشتركة
            if (window.DB) {
                results.push({ type: 'success', message: '✅ نظام قاعدة البيانات المشتركة متاح' });
                try {
                    const dbLicenses = window.DB.getLicenses();
                    const dbSessions = window.DB.getSessions();
                    const dbDevices = window.DB.getDevices();
                    const dbStats = window.DB.getStatistics();
                    
                    details.push(`<h3>🗄️ إحصائيات قاعدة البيانات:</h3>`);
                    details.push(`<pre>التراخيص: ${dbLicenses.length}</pre>`);
                    details.push(`<pre>الجلسات: ${dbSessions.length}</pre>`);
                    details.push(`<pre>الأجهزة: ${dbDevices.length}</pre>`);
                    details.push(`<pre>الإحصائيات: ${JSON.stringify(dbStats, null, 2)}</pre>`);
                } catch (error) {
                    results.push({ type: 'error', message: '❌ خطأ في قاعدة البيانات: ' + error.message });
                }
            } else {
                results.push({ type: 'error', message: '❌ نظام قاعدة البيانات المشتركة غير متاح' });
            }
            
            // فحص ملفات CSS و JS
            const stylesheets = document.querySelectorAll('link[rel="stylesheet"]');
            const scripts = document.querySelectorAll('script[src]');
            
            results.push({ type: 'info', message: `📄 تم تحميل ${stylesheets.length} ملف CSS و ${scripts.length} ملف JavaScript` });
            
            // فحص الأخطاء في وحدة التحكم
            const errors = [];
            const originalError = console.error;
            console.error = function(...args) {
                errors.push(args.join(' '));
                originalError.apply(console, args);
            };
            
            if (errors.length > 0) {
                results.push({ type: 'error', message: `❌ توجد ${errors.length} أخطاء في وحدة التحكم` });
                details.push(`<h3>🐛 الأخطاء:</h3><pre>${errors.join('\n')}</pre>`);
            } else {
                results.push({ type: 'success', message: '✅ لا توجد أخطاء في وحدة التحكم' });
            }
            
            // فحص الاتصال بالإنترنت
            if (navigator.onLine) {
                results.push({ type: 'success', message: '✅ الاتصال بالإنترنت متاح' });
            } else {
                results.push({ type: 'warning', message: '⚠️ لا يوجد اتصال بالإنترنت' });
            }
            
            // فحص معلومات المتصفح
            details.push(`<h3>🌐 معلومات المتصفح:</h3>`);
            details.push(`<pre>User Agent: ${navigator.userAgent}</pre>`);
            details.push(`<pre>Platform: ${navigator.platform}</pre>`);
            details.push(`<pre>Language: ${navigator.language}</pre>`);
            details.push(`<pre>URL الحالي: ${window.location.href}</pre>`);
            
            // عرض النتائج
            let html = '';
            results.forEach(result => {
                html += `<div class="status ${result.type}"><h3>${result.message}</h3></div>`;
            });
            
            diagnosticsDiv.innerHTML = html;
            detailsDiv.innerHTML = details.join('');
            
            console.log('🔧 تم إكمال التشخيص:', results);
        }
        
        function clearData() {
            if (confirm('هل تريد مسح جميع البيانات المحفوظة؟')) {
                localStorage.clear();
                alert('تم مسح جميع البيانات');
                runDiagnostics();
            }
        }
        
        function testDashboard() {
            console.log('🧪 اختبار فتح dashboard.html...');
            try {
                window.location.href = 'dashboard.html';
            } catch (error) {
                alert('خطأ في فتح dashboard.html: ' + error.message);
            }
        }
        
        function goToLogin() {
            window.location.href = 'index.html';
        }
        
        // تشغيل التشخيص عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔧 بدء تشخيص النظام...');
            runDiagnostics();
        });
        
        // مراقبة الأخطاء
        window.addEventListener('error', function(e) {
            console.error('خطأ في الصفحة:', e.error);
        });
        
        window.addEventListener('unhandledrejection', function(e) {
            console.error('خطأ في Promise:', e.reason);
        });
    </script>
</body>
</html>
