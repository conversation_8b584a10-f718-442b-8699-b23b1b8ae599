<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مولد الأيقونات - Future Fuel</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            color: white;
        }
        
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            max-width: 600px;
            width: 100%;
        }
        
        h1 {
            margin-bottom: 30px;
            font-size: 2rem;
        }
        
        .icon-preview {
            margin: 20px 0;
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }
        
        .icon-size {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            padding: 15px;
            margin: 10px;
        }
        
        .icon-size h3 {
            margin: 0 0 10px 0;
            font-size: 1rem;
        }
        
        canvas {
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            background: white;
        }
        
        .download-section {
            margin-top: 30px;
        }
        
        .btn {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 1rem;
            cursor: pointer;
            margin: 5px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        
        .info {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            text-align: right;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 مولد أيقونات مؤسسة وقود المستقبل</h1>
        
        <div class="icon-preview">
            <div class="icon-size">
                <h3>16x16</h3>
                <canvas id="canvas16" width="16" height="16"></canvas>
            </div>
            <div class="icon-size">
                <h3>32x32</h3>
                <canvas id="canvas32" width="32" height="32"></canvas>
            </div>
            <div class="icon-size">
                <h3>48x48</h3>
                <canvas id="canvas48" width="48" height="48"></canvas>
            </div>
            <div class="icon-size">
                <h3>64x64</h3>
                <canvas id="canvas64" width="64" height="64"></canvas>
            </div>
            <div class="icon-size">
                <h3>128x128</h3>
                <canvas id="canvas128" width="128" height="128"></canvas>
            </div>
            <div class="icon-size">
                <h3>256x256</h3>
                <canvas id="canvas256" width="256" height="256"></canvas>
            </div>
        </div>
        
        <div class="download-section">
            <button class="btn" onclick="downloadIcon(16)">تحميل 16x16</button>
            <button class="btn" onclick="downloadIcon(32)">تحميل 32x32</button>
            <button class="btn" onclick="downloadIcon(48)">تحميل 48x48</button>
            <button class="btn" onclick="downloadIcon(64)">تحميل 64x64</button>
            <button class="btn" onclick="downloadIcon(128)">تحميل 128x128</button>
            <button class="btn" onclick="downloadIcon(256)">تحميل 256x256</button>
        </div>
        
        <div class="info">
            <h3>📋 تعليمات الاستخدام:</h3>
            <p>1. انتظر حتى يتم تحميل جميع الأيقونات</p>
            <p>2. انقر على الزر المناسب لتحميل الحجم المطلوب</p>
            <p>3. استخدم الأيقونة 32x32 للاختصارات</p>
            <p>4. استخدم الأيقونة 256x256 للتطبيقات الحديثة</p>
            <p>5. احفظ الملفات في مجلد assets</p>
        </div>
    </div>

    <script>
        // رسم الأيقونة على Canvas
        function drawIcon(canvas, size) {
            const ctx = canvas.getContext('2d');
            const scale = size / 256;
            
            // تنظيف Canvas
            ctx.clearRect(0, 0, size, size);
            
            // خلفية متدرجة
            const bgGradient = ctx.createLinearGradient(0, 0, size, size);
            bgGradient.addColorStop(0, '#1e3c72');
            bgGradient.addColorStop(0.5, '#2a5298');
            bgGradient.addColorStop(1, '#3498db');
            
            // رسم الخلفية الدائرية
            ctx.beginPath();
            ctx.arc(size/2, size/2, size/2 - 5*scale, 0, 2 * Math.PI);
            ctx.fillStyle = bgGradient;
            ctx.fill();
            
            // رسم مضخة الغاز المبسطة
            const centerX = size / 2;
            const centerY = size / 2;
            
            // جسم المضخة
            const pumpGradient = ctx.createLinearGradient(0, 0, size, size);
            pumpGradient.addColorStop(0, '#e74c3c');
            pumpGradient.addColorStop(1, '#c0392b');
            
            ctx.fillStyle = pumpGradient;
            ctx.fillRect(centerX - 25*scale, centerY - 30*scale, 50*scale, 50*scale);
            
            // شاشة العرض
            ctx.fillStyle = '#2c3e50';
            ctx.fillRect(centerX - 20*scale, centerY - 25*scale, 40*scale, 20*scale);
            
            // خرطوم الوقود
            ctx.strokeStyle = '#f39c12';
            ctx.lineWidth = 4 * scale;
            ctx.beginPath();
            ctx.moveTo(centerX + 25*scale, centerY - 10*scale);
            ctx.quadraticCurveTo(centerX + 40*scale, centerY + 5*scale, centerX + 35*scale, centerY + 25*scale);
            ctx.stroke();
            
            // نجمة في الأعلى
            if (size >= 32) {
                ctx.fillStyle = '#f1c40f';
                ctx.beginPath();
                const starX = centerX;
                const starY = centerY - 50*scale;
                const starSize = 8 * scale;
                
                for (let i = 0; i < 5; i++) {
                    const angle = (i * 144 - 90) * Math.PI / 180;
                    const x = starX + Math.cos(angle) * starSize;
                    const y = starY + Math.sin(angle) * starSize;
                    if (i === 0) ctx.moveTo(x, y);
                    else ctx.lineTo(x, y);
                }
                ctx.closePath();
                ctx.fill();
            }
            
            // حدود بيضاء
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.5)';
            ctx.lineWidth = 1;
            ctx.beginPath();
            ctx.arc(size/2, size/2, size/2 - 5*scale, 0, 2 * Math.PI);
            ctx.stroke();
        }
        
        // تحميل الأيقونة
        function downloadIcon(size) {
            const canvas = document.getElementById(`canvas${size}`);
            const link = document.createElement('a');
            link.download = `futurefuel-icon-${size}x${size}.png`;
            link.href = canvas.toDataURL();
            link.click();
        }
        
        // رسم جميع الأيقونات عند تحميل الصفحة
        window.addEventListener('load', function() {
            const sizes = [16, 32, 48, 64, 128, 256];
            sizes.forEach(size => {
                const canvas = document.getElementById(`canvas${size}`);
                drawIcon(canvas, size);
            });
        });
    </script>
</body>
</html>
