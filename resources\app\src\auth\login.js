// نظام تسجيل الدخول والمصادقة
class LoginSystem {
    constructor() {
        this.isAuthenticated = false;
        this.currentUser = null;
        this.licenseStatus = 'inactive';
        this.deviceId = null;
        this.init();
    }

    init() {
        console.log('🔐 تهيئة نظام تسجيل الدخول...');
        this.generateOrLoadDeviceId();
        this.setupEventListeners();
        this.updateSystemTime();
        this.checkConnectionStatus();
        this.loadStatesData();
        this.checkLicenseStatus();

        // تحديث الوقت كل ثانية
        setInterval(() => this.updateSystemTime(), 1000);

        // فحص الاتصال كل 30 ثانية
        setInterval(() => this.checkConnectionStatus(), 30000);
    }

    setupEventListeners() {
        // نموذج تسجيل الدخول
        const loginForm = document.getElementById('login-form');
        const togglePassword = document.getElementById('toggle-password');
        const activationBtn = document.getElementById('activation-btn');
        const contactBtn = document.getElementById('contact-btn');

        // النوافذ المنبثقة
        const activationModal = document.getElementById('activation-modal');
        const contactModal = document.getElementById('contact-modal');
        const closeActivationModal = document.getElementById('close-activation-modal');
        const closeContactModal = document.getElementById('close-contact-modal');
        const cancelActivation = document.getElementById('cancel-activation');

        // نموذج التفعيل
        const activationForm = document.getElementById('activation-form');
        const stateSelect = document.getElementById('state');
        const municipalitySelect = document.getElementById('municipality');

        // أزرار إضافية
        const copyDeviceIdBtn = document.getElementById('copy-device-id');
        const validateLicenseBtn = document.getElementById('validate-license-btn');

        // أحداث تسجيل الدخول
        if (loginForm) {
            loginForm.addEventListener('submit', (e) => this.handleLogin(e));
        }

        if (togglePassword) {
            togglePassword.addEventListener('click', () => this.togglePasswordVisibility());
        }

        // أحداث النوافذ المنبثقة
        if (activationBtn) {
            activationBtn.addEventListener('click', () => this.showActivationModal());
        }

        if (contactBtn) {
            contactBtn.addEventListener('click', () => this.showContactModal());
        }

        if (closeActivationModal) {
            closeActivationModal.addEventListener('click', () => this.hideActivationModal());
        }

        if (closeContactModal) {
            closeContactModal.addEventListener('click', () => this.hideContactModal());
        }

        if (cancelActivation) {
            cancelActivation.addEventListener('click', () => this.hideActivationModal());
        }

        // إغلاق النوافذ بالنقر خارجها
        if (activationModal) {
            activationModal.addEventListener('click', (e) => {
                if (e.target === activationModal) this.hideActivationModal();
            });
        }

        if (contactModal) {
            contactModal.addEventListener('click', (e) => {
                if (e.target === contactModal) this.hideContactModal();
            });
        }

        // نموذج التفعيل
        if (activationForm) {
            activationForm.addEventListener('submit', (e) => this.handleActivationRequest(e));
        }

        if (stateSelect) {
            stateSelect.addEventListener('change', () => this.updateMunicipalities());
        }

        // أحداث الأزرار الإضافية
        if (copyDeviceIdBtn) {
            copyDeviceIdBtn.addEventListener('click', () => this.copyDeviceId());
        }

        if (validateLicenseBtn) {
            validateLicenseBtn.addEventListener('click', () => this.validateLicense());
        }

        // اختصارات لوحة المفاتيح
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.hideAllModals();
            }
        });
    }

    async handleLogin(event) {
        event.preventDefault();

        const loginBtn = document.getElementById('login-btn');
        const spinner = document.getElementById('login-spinner');
        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;
        const rememberMe = document.getElementById('remember-me').checked;

        // تعطيل الزر وإظهار التحميل
        loginBtn.disabled = true;
        spinner.style.display = 'block';

        try {
            console.log('🔑 محاولة تسجيل الدخول...');

            // التحقق من وجود واجهة Electron
            if (window.electronAPI) {
                // استخدام IPC للتواصل مع العملية الرئيسية
                const result = await window.electronAPI.login({ username, password });

                if (!result.success) {
                    this.showError(result.error || 'فشل في تسجيل الدخول');
                    return;
                }

                // حفظ بيانات المستخدم
                if (rememberMe) {
                    localStorage.setItem('rememberedUser', username);
                }

                // تسجيل الدخول بنجاح
                this.isAuthenticated = true;
                this.currentUser = result.user;

                this.showSuccess('تم تسجيل الدخول بنجاح! جاري تحميل التطبيق...');

                // انتقال إلى التطبيق الرئيسي عبر IPC
                setTimeout(async () => {
                    await window.electronAPI.reloadAfterLogin();
                }, 2000);
            } else {
                // محاكاة التحقق من بيانات الاعتماد (للاختبار في المتصفح)
                await this.simulateLogin(username, password);

                // فحص حالة الترخيص
                const licenseValid = await this.validateLicense();

                if (!licenseValid) {
                    this.showError('الترخيص غير صالح أو منتهي الصلاحية. يرجى طلب تفعيل جديد.');
                    return;
                }

                // حفظ بيانات المستخدم
                if (rememberMe) {
                    localStorage.setItem('rememberedUser', username);
                }

                // تسجيل الدخول بنجاح
                this.isAuthenticated = true;
                this.currentUser = { username, loginTime: new Date() };

                this.showSuccess('تم تسجيل الدخول بنجاح! جاري تحميل التطبيق...');

                // انتقال إلى التطبيق الرئيسي
                setTimeout(() => {
                    this.redirectToMainApp();
                }, 2000);
            }

        } catch (error) {
            console.error('❌ خطأ في تسجيل الدخول:', error);
            this.showError(error.message || 'حدث خطأ أثناء تسجيل الدخول');
        } finally {
            // إعادة تفعيل الزر وإخفاء التحميل
            loginBtn.disabled = false;
            spinner.style.display = 'none';
        }
    }

    async simulateLogin(username, password) {
        // محاكاة تأخير الشبكة
        await new Promise(resolve => setTimeout(resolve, 1500));
        
        // بيانات اعتماد افتراضية للاختبار
        const validCredentials = [
            { username: 'admin', password: 'admin123' },
            { username: 'user', password: 'user123' },
            { username: 'manager', password: 'manager123' }
        ];

        const isValid = validCredentials.some(cred => 
            cred.username === username && cred.password === password
        );

        if (!isValid) {
            throw new Error('اسم المستخدم أو كلمة المرور غير صحيحة');
        }

        return true;
    }

    async validateLicense() {
        // محاكاة فحص الترخيص
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // فحص الترخيص المحلي
        const localLicense = localStorage.getItem('appLicense');
        if (!localLicense) {
            return false;
        }

        try {
            const license = JSON.parse(localLicense);
            const now = new Date();
            const expiryDate = new Date(license.expiryDate);
            
            return now < expiryDate && license.status === 'active';
        } catch {
            return false;
        }
    }

    togglePasswordVisibility() {
        const passwordInput = document.getElementById('password');
        const toggleBtn = document.getElementById('toggle-password');
        const icon = toggleBtn.querySelector('i');

        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            icon.className = 'fas fa-eye-slash';
        } else {
            passwordInput.type = 'password';
            icon.className = 'fas fa-eye';
        }
    }

    showActivationModal() {
        const modal = document.getElementById('activation-modal');
        modal.classList.add('show');
        modal.style.display = 'flex';
    }

    hideActivationModal() {
        const modal = document.getElementById('activation-modal');
        modal.classList.remove('show');
        modal.style.display = 'none';
    }

    showContactModal() {
        const modal = document.getElementById('contact-modal');
        modal.classList.add('show');
        modal.style.display = 'flex';
    }

    hideContactModal() {
        const modal = document.getElementById('contact-modal');
        modal.classList.remove('show');
        modal.style.display = 'none';
    }

    hideAllModals() {
        this.hideActivationModal();
        this.hideContactModal();
    }

    loadStatesData() {
        const stateSelect = document.getElementById('state');
        if (!stateSelect || !window.algeriaData) return;

        // مسح الخيارات الموجودة
        stateSelect.innerHTML = '<option value="">اختر الولاية</option>';

        // إضافة الولايات
        const states = window.algeriaData.getAllStates();
        states.forEach(state => {
            const option = document.createElement('option');
            option.value = state.code;
            option.textContent = `${state.code} - ${state.name}`;
            stateSelect.appendChild(option);
        });
    }

    updateMunicipalities() {
        const stateSelect = document.getElementById('state');
        const municipalitySelect = document.getElementById('municipality');
        
        if (!stateSelect || !municipalitySelect || !window.algeriaData) return;

        const selectedStateCode = stateSelect.value;
        
        // مسح البلديات
        municipalitySelect.innerHTML = '<option value="">اختر البلدية</option>';

        if (selectedStateCode) {
            const municipalities = window.algeriaData.getMunicipalities(selectedStateCode);
            municipalities.forEach(municipality => {
                const option = document.createElement('option');
                option.value = municipality;
                option.textContent = municipality;
                municipalitySelect.appendChild(option);
            });
        }
    }

    async handleActivationRequest(event) {
        event.preventDefault();
        
        const formData = new FormData(event.target);
        const activationData = {
            firstName: formData.get('firstName'),
            lastName: formData.get('lastName'),
            phone: formData.get('phone'),
            state: formData.get('state'),
            municipality: formData.get('municipality'),
            businessName: formData.get('businessName'),
            notes: formData.get('notes'),
            deviceId: this.deviceId,
            timestamp: new Date().toISOString(),
            deviceInfo: this.getDeviceInfo()
        };

        try {
            console.log('📤 إرسال طلب التفعيل...', activationData);

            // حفظ الطلب محلياً
            this.saveActivationRequest(activationData);

            // إرسال الطلب
            if (window.electronAPI) {
                // استخدام IPC في Electron
                const result = await window.electronAPI.submitActivationRequest(activationData);
                if (!result.success) {
                    throw new Error(result.error || 'فشل في إرسال الطلب');
                }
            } else {
                // محاكاة إرسال الطلب في المتصفح
                await this.submitActivationRequest(activationData);
            }

            this.showSuccess('تم إرسال طلب التفعيل بنجاح! سيتم التواصل معك قريباً.');
            this.hideActivationModal();

            // إعادة تعيين النموذج
            event.target.reset();

        } catch (error) {
            console.error('❌ خطأ في إرسال طلب التفعيل:', error);
            this.showError('حدث خطأ أثناء إرسال الطلب. يرجى المحاولة مرة أخرى.');
        }
    }

    async submitActivationRequest(data) {
        // محاكاة إرسال الطلب إلى الخادم
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // في التطبيق الحقيقي، سيتم إرسال البيانات إلى خادم الترخيص
        console.log('✅ تم إرسال طلب التفعيل إلى الخادم');
        
        return { success: true, requestId: this.generateRequestId() };
    }

    saveActivationRequest(data) {
        const requests = JSON.parse(localStorage.getItem('activationRequests') || '[]');
        requests.push(data);
        localStorage.setItem('activationRequests', JSON.stringify(requests));
    }

    generateRequestId() {
        return 'REQ-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
    }

    getDeviceInfo() {
        return {
            userAgent: navigator.userAgent,
            platform: navigator.platform,
            language: navigator.language,
            screenResolution: `${screen.width}x${screen.height}`,
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
        };
    }

    updateSystemTime() {
        const timeElement = document.getElementById('system-time');
        if (timeElement) {
            const now = new Date();
            const timeString = now.toLocaleString('ar-DZ', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            timeElement.textContent = timeString;
        }
    }

    async checkConnectionStatus() {
        const statusElement = document.getElementById('connection-status');
        if (!statusElement) return;

        try {
            // محاكاة فحص الاتصال
            const response = await fetch('https://www.google.com/favicon.ico', {
                method: 'HEAD',
                mode: 'no-cors',
                cache: 'no-cache'
            });
            
            statusElement.innerHTML = '<i class="fas fa-wifi"></i> متصل';
            statusElement.className = 'connection-status connected';
        } catch {
            statusElement.innerHTML = '<i class="fas fa-wifi-slash"></i> غير متصل';
            statusElement.className = 'connection-status disconnected';
        }
    }

    checkLicenseStatus() {
        const statusElement = document.getElementById('license-status');
        if (!statusElement) return;

        const license = localStorage.getItem('appLicense');
        if (license) {
            try {
                const licenseData = JSON.parse(license);
                const now = new Date();
                const expiryDate = new Date(licenseData.expiryDate);
                
                if (now < expiryDate && licenseData.status === 'active') {
                    statusElement.innerHTML = `
                        <div class="status-indicator">
                            <i class="fas fa-check-circle"></i>
                            <span>الترخيص نشط - ينتهي في ${this.formatDate(expiryDate)}</span>
                        </div>
                    `;
                    statusElement.style.background = 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)';
                    this.licenseStatus = 'active';
                } else {
                    this.showExpiredLicense();
                }
            } catch {
                this.showInvalidLicense();
            }
        } else {
            this.showNoLicense();
        }
    }

    showExpiredLicense() {
        const statusElement = document.getElementById('license-status');
        statusElement.innerHTML = `
            <div class="status-indicator">
                <i class="fas fa-exclamation-triangle"></i>
                <span>الترخيص منتهي الصلاحية - يرجى التجديد</span>
            </div>
        `;
        statusElement.style.background = 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)';
        this.licenseStatus = 'expired';
    }

    showInvalidLicense() {
        const statusElement = document.getElementById('license-status');
        statusElement.innerHTML = `
            <div class="status-indicator">
                <i class="fas fa-times-circle"></i>
                <span>ترخيص غير صالح - يرجى طلب ترخيص جديد</span>
            </div>
        `;
        statusElement.style.background = 'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)';
        this.licenseStatus = 'invalid';
    }

    showNoLicense() {
        const statusElement = document.getElementById('license-status');
        statusElement.innerHTML = `
            <div class="status-indicator">
                <i class="fas fa-exclamation-triangle"></i>
                <span>يتطلب تفعيل الترخيص</span>
            </div>
        `;
        statusElement.style.background = 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)';
        this.licenseStatus = 'inactive';
    }

    formatDate(date) {
        return date.toLocaleDateString('ar-DZ', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    }

    showSuccess(message) {
        this.showToast(message, 'success');
    }

    showError(message) {
        this.showToast(message, 'error');
    }

    showToast(message, type = 'info') {
        // إنشاء عنصر التنبيه
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.innerHTML = `
            <div class="toast-content">
                <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle'}"></i>
                <span>${message}</span>
            </div>
        `;

        // إضافة الأنماط
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'success' ? '#2ecc71' : type === 'error' ? '#e74c3c' : '#3498db'};
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            z-index: 10000;
            animation: slideInRight 0.3s ease-out;
            max-width: 400px;
            word-wrap: break-word;
        `;

        document.body.appendChild(toast);

        // إزالة التنبيه بعد 5 ثوان
        setTimeout(() => {
            toast.style.animation = 'slideOutRight 0.3s ease-in';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 5000);
    }

    redirectToMainApp() {
        // في التطبيق الحقيقي، سيتم الانتقال إلى الصفحة الرئيسية
        window.location.href = '../../index.html';
    }

    // ===== وظائف معرف الجهاز =====
    generateOrLoadDeviceId() {
        // محاولة تحميل معرف الجهاز المحفوظ
        let savedDeviceId = localStorage.getItem('deviceId');

        if (!savedDeviceId) {
            // توليد معرف جديد
            savedDeviceId = this.generateDeviceId();
            localStorage.setItem('deviceId', savedDeviceId);
            console.log('🆔 تم توليد معرف جهاز جديد:', savedDeviceId);
        } else {
            console.log('🆔 تم تحميل معرف الجهاز المحفوظ:', savedDeviceId);
        }

        this.deviceId = savedDeviceId;
        this.displayDeviceId();
    }

    generateDeviceId() {
        // توليد معرف فريد باستخدام معلومات الجهاز والوقت
        const timestamp = Date.now();
        const random = Math.random().toString(36).substr(2, 9);
        const userAgent = navigator.userAgent;
        const screen = `${window.screen.width}x${window.screen.height}`;
        const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

        // إنشاء hash بسيط
        const data = `${timestamp}-${random}-${userAgent}-${screen}-${timezone}`;
        const hash = this.simpleHash(data);

        // تنسيق المعرف
        return `FFC-${hash.substr(0, 4)}-${hash.substr(4, 4)}-${hash.substr(8, 4)}-${hash.substr(12, 4)}`.toUpperCase();
    }

    simpleHash(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // تحويل إلى 32bit integer
        }
        return Math.abs(hash).toString(16).padStart(16, '0');
    }

    displayDeviceId() {
        const deviceIdElement = document.getElementById('device-id-text');
        if (deviceIdElement && this.deviceId) {
            deviceIdElement.textContent = this.deviceId;
        }
    }

    copyDeviceId() {
        if (this.deviceId) {
            navigator.clipboard.writeText(this.deviceId).then(() => {
                this.showSuccess('تم نسخ معرف الجهاز إلى الحافظة');

                // تأثير بصري للزر
                const copyBtn = document.getElementById('copy-device-id');
                const originalText = copyBtn.innerHTML;
                copyBtn.innerHTML = '<i class="fas fa-check"></i>';
                copyBtn.style.background = '#2ecc71';

                setTimeout(() => {
                    copyBtn.innerHTML = originalText;
                    copyBtn.style.background = '';
                }, 2000);
            }).catch(() => {
                this.showError('فشل في نسخ معرف الجهاز');
            });
        }
    }

    // ===== وظائف التحقق من الترخيص =====
    async validateLicense() {
        const licenseKeyInput = document.getElementById('license-key');
        const validateBtn = document.getElementById('validate-license-btn');
        const licenseKey = licenseKeyInput.value.trim();

        if (!licenseKey) {
            this.showError('يرجى إدخال مفتاح الترخيص');
            return;
        }

        // تعطيل الزر أثناء التحقق
        validateBtn.disabled = true;
        validateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحقق...';

        try {
            const isValid = await this.verifyLicenseKey(licenseKey);

            if (isValid) {
                // حفظ الترخيص
                const licenseData = {
                    key: licenseKey,
                    deviceId: this.deviceId,
                    activatedAt: new Date().toISOString(),
                    expiryDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(), // سنة واحدة
                    status: 'active'
                };

                localStorage.setItem('appLicense', JSON.stringify(licenseData));
                this.licenseStatus = 'active';
                this.checkLicenseStatus();

                this.showSuccess('تم تفعيل الترخيص بنجاح!');
                licenseKeyInput.value = '';

                // إخفاء حقل الترخيص
                const licenseGroup = document.getElementById('license-input-group');
                if (licenseGroup) {
                    licenseGroup.style.display = 'none';
                }
            } else {
                this.showError('مفتاح الترخيص غير صالح أو لا يتطابق مع معرف الجهاز');
            }
        } catch (error) {
            this.showError('حدث خطأ أثناء التحقق من الترخيص');
            console.error('خطأ في التحقق من الترخيص:', error);
        } finally {
            // إعادة تفعيل الزر
            validateBtn.disabled = false;
            validateBtn.innerHTML = '<i class="fas fa-check"></i> تحقق';
        }
    }

    async verifyLicenseKey(licenseKey) {
        // محاكاة التحقق من الترخيص
        await new Promise(resolve => setTimeout(resolve, 2000));

        try {
            // فك تشفير مفتاح الترخيص (محاكاة)
            const decoded = atob(licenseKey);
            const licenseData = JSON.parse(decoded);

            // التحقق من معرف الجهاز
            return licenseData.deviceId === this.deviceId &&
                   licenseData.type === 'FFC_LICENSE' &&
                   new Date(licenseData.expiryDate) > new Date();
        } catch {
            return false;
        }
    }
}

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.loginSystem = new LoginSystem();
});

// إضافة أنماط CSS للتنبيهات
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
    
    .toast-content {
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .toast-content i {
        font-size: 1.2rem;
    }
`;
document.head.appendChild(style);
