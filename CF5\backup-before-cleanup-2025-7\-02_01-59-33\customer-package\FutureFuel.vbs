Set WshShell = CreateObject("WScript.Shell")
Set fso = CreateObject("Scripting.FileSystemObject")

' الحصول على مجلد التطبيق
appPath = fso.GetParentFolderName(WScript.ScriptFullName)

' التحقق من وجود ملفات التطبيق
If Not fso.FileExists(appPath & "\app\index.html") Then
    MsgBox "خطأ: ملفات التطبيق غير موجودة" & vbCrLf & "يرجى إعادة تثبيت البرنامج", vbCritical, "مؤسسة وقود المستقبل"
    WScript.Quit
End If

' إنشاء مجلدات مؤقتة إذا لم تكن موجودة
If Not fso.FolderExists(appPath & "\temp") Then
    fso.CreateFolder(appPath & "\temp")
End If

If Not fso.FolderExists(appPath & "\data") Then
    fso.CreateFolder(appPath & "\data")
End If

' التحقق من تشغيل نسخة أخرى
lockFile = appPath & "\temp\app.lock"
If fso.FileExists(lockFile) Then
    ' التحقق من عمر ملف القفل (إذا كان أكثر من 5 دقائق، احذفه)
    Set lockFileObj = fso.GetFile(lockFile)
    If DateDiff("n", lockFileObj.DateLastModified, Now) > 5 Then
        fso.DeleteFile(lockFile)
    Else
        MsgBox "التطبيق يعمل بالفعل", vbInformation, "مؤسسة وقود المستقبل"
        WScript.Quit
    End If
End If

' إنشاء ملف قفل
Set lockFileObj = fso.CreateTextFile(lockFile, True)
lockFileObj.WriteLine(Now)
lockFileObj.Close

' تشغيل التطبيق
htmlFile = "file:///" & Replace(appPath & "\app\index.html", "\", "/")
WshShell.Run "cmd /c start """" """ & htmlFile & """", 0, False

' تنظيف ملف القفل بعد 30 ثانية
WScript.Sleep(30000)
If fso.FileExists(lockFile) Then
    fso.DeleteFile(lockFile)
End If
