const { app, BrowserWindow, Menu, dialog, ipcMain, shell, nativeTheme } = require('electron');
const { autoUpdater } = require('electron-updater');
const log = require('electron-log');
const path = require('path');

// إعداد نظام السجلات
log.transports.file.level = 'info';
autoUpdater.logger = log;

// تعطيل تحذيرات الأمان في بيئة التطوير
process.env.NODE_ENV = 'production';

// المتغير العام للنافذة الرئيسية
let mainWindow;

// التحقق من وجود نسخة واحدة فقط من التطبيق
const gotTheLock = app.requestSingleInstanceLock();

if (!gotTheLock) {
  app.quit();
} else {
  app.on('second-instance', () => {
    // إذا حاول المستخدم فتح نسخة ثانية، ركز على النافذة الموجودة
    if (mainWindow) {
      if (mainWindow.isMinimized()) mainWindow.restore();
      mainWindow.focus();
    }
  });
}

// فحص حالة تسجيل الدخول
function checkLoginStatus() {
  try {
    const fs = require('fs');
    const sessionPath = path.join(__dirname, 'data', 'session.json');

    if (fs.existsSync(sessionPath)) {
      const sessionData = JSON.parse(fs.readFileSync(sessionPath, 'utf8'));
      const now = new Date();
      const sessionExpiry = new Date(sessionData.expiryDate);

      return now < sessionExpiry && sessionData.isValid;
    }
    return false;
  } catch (error) {
    console.error('خطأ في فحص حالة تسجيل الدخول:', error);
    return false;
  }
}

// إنشاء النافذة الرئيسية
function createMainWindow() {
  const isLoggedIn = checkLoginStatus();

  mainWindow = new BrowserWindow({
    title: isLoggedIn ? 'نظام إدارة مؤسسة وقود المستقبل' : 'تسجيل الدخول - مؤسسة وقود المستقبل',
    width: isLoggedIn ? 1200 : 800,
    height: isLoggedIn ? 800 : 600,
    minWidth: isLoggedIn ? 800 : 600,
    minHeight: isLoggedIn ? 600 : 500,
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'),
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      webSecurity: true,
      allowRunningInsecureContent: false,
      experimentalFeatures: false,
      plugins: false,
      sandbox: false, // يمكن تفعيله لمزيد من الأمان
      safeDialogs: true,
      safeDialogsMessage: 'تم حظر نافذة منبثقة غير آمنة'
    },
    icon: path.join(__dirname, 'assets/icons/app-icon.ico'),
    show: false, // لا تظهر النافذة حتى تكتمل عملية التحميل
    titleBarStyle: 'default',
    frame: true,
    resizable: true,
    maximizable: true,
    minimizable: true,
    closable: true,
    center: true
  });

  // تحميل الصفحة المناسبة
  if (isLoggedIn) {
    mainWindow.loadFile('index.html');
  } else {
    mainWindow.loadFile('src/auth/login.html');
  }

  // معالجة أخطاء التحميل
  mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription, validatedURL) => {
    log.error(`فشل في تحميل الصفحة: ${errorDescription} (${errorCode}) - ${validatedURL}`);

    // إظهار صفحة خطأ مخصصة
    const errorHtml = `
      <!DOCTYPE html>
      <html lang="ar" dir="rtl">
      <head>
        <meta charset="UTF-8">
        <title>خطأ في التحميل</title>
        <style>
          body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
          .error-container { max-width: 500px; margin: 0 auto; }
          .error-icon { font-size: 64px; color: #e74c3c; margin-bottom: 20px; }
          .error-title { font-size: 24px; margin-bottom: 10px; }
          .error-message { color: #666; margin-bottom: 30px; }
          .retry-btn { background: #3498db; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; }
        </style>
      </head>
      <body>
        <div class="error-container">
          <div class="error-icon">⚠️</div>
          <h1 class="error-title">فشل في تحميل التطبيق</h1>
          <p class="error-message">حدث خطأ أثناء تحميل التطبيق. يرجى المحاولة مرة أخرى.</p>
          <button class="retry-btn" onclick="location.reload()">إعادة المحاولة</button>
        </div>
      </body>
      </html>
    `;

    mainWindow.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(errorHtml)}`);
  });

  // معالجة أخطاء JavaScript
  mainWindow.webContents.on('console-message', (event, level, message, line, sourceId) => {
    if (level >= 2) { // خطأ أو تحذير
      log.warn(`Console ${level === 2 ? 'Warning' : 'Error'}: ${message} (${sourceId}:${line})`);
    }
  });

  // إظهار النافذة عند اكتمال التحميل
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // إنشاء قائمة التطبيق
  const mainMenu = Menu.buildFromTemplate(menuTemplate);
  Menu.setApplicationMenu(mainMenu);

  // معالجة الروابط الخارجية بأمان
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    // السماح فقط بالروابط الآمنة
    if (url.startsWith('https://') || url.startsWith('http://')) {
      shell.openExternal(url);
    }
    return { action: 'deny' };
  });

  // منع التنقل إلى مواقع خارجية
  mainWindow.webContents.on('will-navigate', (event, navigationUrl) => {
    const parsedUrl = new URL(navigationUrl);

    // السماح فقط بالملفات المحلية
    if (parsedUrl.protocol !== 'file:') {
      event.preventDefault();
    }
  });

  // إغلاق النافذة
  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

// إنشاء قائمة التطبيق
const menuTemplate = [
  {
    label: 'ملف',
    submenu: [
      {
        label: 'حفظ البيانات',
        accelerator: process.platform === 'darwin' ? 'Command+S' : 'Ctrl+S',
        click() {
          mainWindow.webContents.executeJavaScript('saveData()');
        }
      },
      {
        label: 'طباعة',
        accelerator: process.platform === 'darwin' ? 'Command+P' : 'Ctrl+P',
        click() {
          mainWindow.webContents.print();
        }
      },
      {
        type: 'separator'
      },
      {
        label: 'خروج',
        accelerator: process.platform === 'darwin' ? 'Command+Q' : 'Ctrl+Q',
        click() {
          app.quit();
        }
      }
    ]
  },
  {
    label: 'عرض',
    submenu: [
      {
        label: 'إعادة تحميل',
        accelerator: 'F5',
        click() {
          mainWindow.reload();
        }
      },
      {
        label: 'تكبير',
        accelerator: process.platform === 'darwin' ? 'Command+Plus' : 'Ctrl+Plus',
        click() {
          mainWindow.webContents.setZoomLevel(mainWindow.webContents.getZoomLevel() + 1);
        }
      },
      {
        label: 'تصغير',
        accelerator: process.platform === 'darwin' ? 'Command+-' : 'Ctrl+-',
        click() {
          mainWindow.webContents.setZoomLevel(mainWindow.webContents.getZoomLevel() - 1);
        }
      },
      {
        label: 'حجم طبيعي',
        accelerator: process.platform === 'darwin' ? 'Command+0' : 'Ctrl+0',
        click() {
          mainWindow.webContents.setZoomLevel(0);
        }
      }
    ]
  },
  {
    label: 'مساعدة',
    submenu: [
      {
        label: 'حول البرنامج',
        click() {
          dialog.showMessageBox(mainWindow, {
            type: 'info',
            title: 'حول البرنامج',
            message: 'نظام إدارة مؤسسة وقود المستقبل',
            detail: 'الإصدار 2.2.0\nنظام متكامل لإدارة مؤسسة وقود المستقبل\n\nالميزات الجديدة:\n• نسخ احتياطية تلقائية\n• تكامل تيليجرام\n• اختصارات لوحة المفاتيح\n• الوضع المظلم'
          });
        }
      }
    ]
  }
];

// معالجة أحداث IPC
const setupIpcHandlers = () => {
  // حفظ البيانات
  ipcMain.handle('save-data', async (_, data) => {
    try {
      // التحقق من صحة البيانات
      if (!data || typeof data !== 'object') {
        return { success: false, error: 'البيانات غير صالحة' };
      }

      // التحقق من حجم البيانات (حد أقصى 50MB)
      const dataString = JSON.stringify(data, null, 2);
      if (dataString.length > 50 * 1024 * 1024) {
        return { success: false, error: 'حجم البيانات كبير جداً' };
      }

      const fs = require('fs');
      const dataPath = path.join(__dirname, 'data.json');

      // إنشاء نسخة احتياطية قبل الحفظ
      if (fs.existsSync(dataPath)) {
        const backupPath = path.join(__dirname, 'data.backup.json');
        fs.copyFileSync(dataPath, backupPath);
      }

      fs.writeFileSync(dataPath, dataString);
      return { success: true };
    } catch (error) {
      console.error('خطأ في حفظ البيانات:', error);
      return { success: false, error: error.message };
    }
  });

  // تحميل البيانات
  ipcMain.handle('load-data', async () => {
    try {
      const fs = require('fs');
      const dataPath = path.join(__dirname, 'data.json');
      if (fs.existsSync(dataPath)) {
        const data = fs.readFileSync(dataPath, 'utf8');
        return JSON.parse(data);
      }
      return null;
    } catch (error) {
      console.error('خطأ في تحميل البيانات:', error);
      return null;
    }
  });

  // إنشاء نسخة احتياطية
  ipcMain.handle('create-backup', async () => {
    try {
      const fs = require('fs');
      const dataPath = path.join(__dirname, 'data.json');
      const backupPath = path.join(__dirname, `backup_${Date.now()}.json`);

      if (fs.existsSync(dataPath)) {
        fs.copyFileSync(dataPath, backupPath);
        return { success: true, backupPath };
      }
      return { success: false, error: 'ملف البيانات غير موجود' };
    } catch (error) {
      console.error('خطأ في إنشاء النسخة الاحتياطية:', error);
      return { success: false, error: error.message };
    }
  });

  // فتح مربع حوار حفظ الملف
  ipcMain.handle('save-dialog', async (_, options) => {
    const { canceled, filePath } = await dialog.showSaveDialog(mainWindow, options);
    if (canceled) {
      return null;
    }
    return filePath;
  });

  // فتح مربع حوار فتح الملف
  ipcMain.handle('open-dialog', async (_, options) => {
    const { canceled, filePaths } = await dialog.showOpenDialog(mainWindow, options);
    if (canceled) {
      return null;
    }
    return filePaths[0];
  });

  // إنشاء نسخة احتياطية تلقائية
  ipcMain.on('create-auto-backup', () => {
    log.info('إنشاء نسخة احتياطية تلقائية...');
    // يمكن إضافة كود لإنشاء نسخة احتياطية تلقائية هنا
  });

  // فتح رابط خارجي
  ipcMain.handle('open-external', async (_, url) => {
    await shell.openExternal(url);
  });

  // الحصول على معلومات النظام
  ipcMain.handle('get-system-info', () => {
    return {
      platform: process.platform,
      arch: process.arch,
      version: app.getVersion(),
      electronVersion: process.versions.electron,
      nodeVersion: process.versions.node
    };
  });

  // تغيير الثيم
  ipcMain.handle('set-theme', (_, theme) => {
    nativeTheme.themeSource = theme;
    return nativeTheme.shouldUseDarkColors;
  });

  // الحصول على الثيم الحالي
  ipcMain.handle('get-theme', () => {
    return {
      shouldUseDarkColors: nativeTheme.shouldUseDarkColors,
      themeSource: nativeTheme.themeSource
    };
  });

  // معالجات نظام تسجيل الدخول
  ipcMain.handle('login', async (_, credentials) => {
    try {
      // التحقق من صحة البيانات المدخلة
      if (!credentials || !credentials.username || !credentials.password) {
        return { success: false, error: 'بيانات تسجيل الدخول غير مكتملة' };
      }

      // التحقق من طول البيانات لمنع الهجمات
      if (credentials.username.length > 50 || credentials.password.length > 100) {
        return { success: false, error: 'بيانات تسجيل الدخول غير صالحة' };
      }

      // محاكاة التحقق من بيانات الاعتماد
      const validCredentials = [
        { username: 'admin', password: 'admin123' },
        { username: 'user', password: 'user123' },
        { username: 'manager', password: 'manager123' }
      ];

      const isValid = validCredentials.some(cred =>
        cred.username === credentials.username && cred.password === credentials.password
      );

      if (isValid) {
        // إنشاء جلسة
        const sessionData = {
          username: credentials.username,
          loginTime: new Date().toISOString(),
          expiryDate: new Date(Date.now() + 8 * 60 * 60 * 1000).toISOString(), // 8 ساعات
          isValid: true
        };

        // حفظ الجلسة
        const fs = require('fs');
        const dataDir = path.join(__dirname, 'data');
        if (!fs.existsSync(dataDir)) {
          fs.mkdirSync(dataDir, { recursive: true });
        }

        fs.writeFileSync(
          path.join(dataDir, 'session.json'),
          JSON.stringify(sessionData, null, 2)
        );

        return { success: true, user: { username: credentials.username } };
      } else {
        return { success: false, error: 'اسم المستخدم أو كلمة المرور غير صحيحة' };
      }
    } catch (error) {
      return { success: false, error: 'حدث خطأ أثناء تسجيل الدخول' };
    }
  });

  // تسجيل الخروج
  ipcMain.handle('logout', async () => {
    try {
      const fs = require('fs');
      const sessionPath = path.join(__dirname, 'data', 'session.json');

      if (fs.existsSync(sessionPath)) {
        fs.unlinkSync(sessionPath);
      }

      return { success: true };
    } catch (error) {
      return { success: false, error: 'حدث خطأ أثناء تسجيل الخروج' };
    }
  });

  // إعادة تحميل التطبيق بعد تسجيل الدخول
  ipcMain.handle('reload-after-login', () => {
    mainWindow.loadFile('index.html');
    mainWindow.setSize(1200, 800);
    mainWindow.center();
    mainWindow.setTitle('نظام إدارة مؤسسة وقود المستقبل');
  });

  // إرسال طلب تفعيل
  ipcMain.handle('submit-activation-request', async (_, requestData) => {
    try {
      // إنشاء معرف فريد للطلب
      const requestId = 'REQ-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);

      const newRequest = {
        ...requestData,
        id: requestId,
        timestamp: new Date().toISOString(),
        status: 'pending'
      };

      // حفظ الطلب في ملف JSON للنسخ الاحتياطي
      const fs = require('fs');
      const dataDir = path.join(__dirname, 'data');
      if (!fs.existsSync(dataDir)) {
        fs.mkdirSync(dataDir, { recursive: true });
      }

      const requestsFile = path.join(dataDir, 'activation-requests.json');
      let requests = [];

      if (fs.existsSync(requestsFile)) {
        requests = JSON.parse(fs.readFileSync(requestsFile, 'utf8'));
      }

      requests.push(newRequest);
      fs.writeFileSync(requestsFile, JSON.stringify(requests, null, 2));

      // حفظ الطلب أيضاً في localStorage للوحة التحكم
      // نحتاج لتنفيذ هذا في العملية الرئيسية للنافذة
      mainWindow.webContents.executeJavaScript(`
        try {
          const existingRequests = JSON.parse(localStorage.getItem('developerPanel_activationRequests') || '[]');
          existingRequests.push(${JSON.stringify(newRequest)});
          localStorage.setItem('developerPanel_activationRequests', JSON.stringify(existingRequests));
          console.log('✅ تم حفظ طلب التفعيل في localStorage');
        } catch (error) {
          console.error('❌ خطأ في حفظ طلب التفعيل في localStorage:', error);
        }
      `);

      console.log('✅ تم إرسال طلب التفعيل:', requestId);
      return { success: true, requestId: requestId };
    } catch (error) {
      console.error('❌ خطأ في إرسال طلب التفعيل:', error);
      return { success: false, error: 'فشل في إرسال طلب التفعيل' };
    }
  });
};

// إعداد نظام التحديث التلقائي
const setupAutoUpdater = () => {
  // التحقق من التحديثات عند بدء التطبيق
  autoUpdater.checkForUpdatesAndNotify();

  // أحداث التحديث
  autoUpdater.on('checking-for-update', () => {
    log.info('جاري البحث عن تحديثات...');
  });

  autoUpdater.on('update-available', (info) => {
    log.info('تحديث متوفر:', info);
    dialog.showMessageBox(mainWindow, {
      type: 'info',
      title: 'تحديث متوفر',
      message: 'يتوفر إصدار جديد من التطبيق',
      detail: `الإصدار الجديد: ${info.version}\nسيتم تنزيل التحديث في الخلفية.`,
      buttons: ['موافق']
    });
  });

  autoUpdater.on('update-not-available', (info) => {
    log.info('لا توجد تحديثات متوفرة:', info);
  });

  autoUpdater.on('error', (err) => {
    log.error('خطأ في التحديث:', err);
  });

  autoUpdater.on('download-progress', (progressObj) => {
    let log_message = `سرعة التنزيل: ${progressObj.bytesPerSecond}`;
    log_message = log_message + ` - تم تنزيل ${progressObj.percent}%`;
    log_message = log_message + ` (${progressObj.transferred}/${progressObj.total})`;
    log.info(log_message);
  });

  autoUpdater.on('update-downloaded', (info) => {
    log.info('تم تنزيل التحديث:', info);
    dialog.showMessageBox(mainWindow, {
      type: 'info',
      title: 'تحديث جاهز',
      message: 'تم تنزيل التحديث بنجاح',
      detail: 'سيتم إعادة تشغيل التطبيق لتطبيق التحديث.',
      buttons: ['إعادة التشغيل الآن', 'لاحقاً']
    }).then((result) => {
      if (result.response === 0) {
        autoUpdater.quitAndInstall();
      }
    });
  });
};

// معالجة الأخطاء غير المتوقعة
process.on('uncaughtException', (error) => {
  log.error('خطأ غير متوقع:', error);
  dialog.showErrorBox('خطأ في التطبيق', `حدث خطأ غير متوقع: ${error.message}`);
});

process.on('unhandledRejection', (reason, promise) => {
  log.error('Promise مرفوض:', reason);
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// تشغيل التطبيق
app.whenReady().then(() => {
  try {
    setupIpcHandlers();
    createMainWindow();
    setupAutoUpdater();
  } catch (error) {
    log.error('خطأ في تهيئة التطبيق:', error);
    dialog.showErrorBox('خطأ في التهيئة', `فشل في تهيئة التطبيق: ${error.message}`);
  }
});

// إغلاق التطبيق عند إغلاق جميع النوافذ
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// إعادة إنشاء النافذة عند تفعيل التطبيق (macOS)
app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createMainWindow();
  }
});