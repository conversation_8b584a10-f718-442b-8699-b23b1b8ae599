<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم الإدارية - Remote Control Panel</title>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            min-height: 100vh;
            direction: rtl;
            color: #e0e6ed;
            overflow-x: hidden;
        }

        /* الرأس الرئيسي */
        .main-header {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            padding: 1.5rem 2rem;
            box-shadow: 0 4px 20px rgba(231, 76, 60, 0.3);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1400px;
            margin: 0 auto;
        }

        .header-title {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .header-logo {
            width: 50px;
            height: 50px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
        }

        .header-info {
            text-align: left;
            font-size: 0.9rem;
            opacity: 0.9;
        }

        /* شريط الإحصائيات */
        .stats-bar {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            padding: 1rem 2rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            max-width: 1400px;
            margin: 0 auto;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 1rem;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-2px);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .stat-card.licenses .stat-number { color: #3498db; }
        .stat-card.devices .stat-number { color: #27ae60; }
        .stat-card.active .stat-number { color: #f39c12; }
        .stat-card.expired .stat-number { color: #e74c3c; }

        /* المحتوى الرئيسي */
        .main-container {
            max-width: 1400px;
            margin: 2rem auto;
            padding: 0 2rem;
        }

        /* شبكة الأدوات */
        .tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .tool-panel {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .panel-header {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            padding: 1.5rem;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .panel-header.danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }

        .panel-header.success {
            background: linear-gradient(135deg, #27ae60, #229954);
        }

        .panel-header.warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }

        .panel-icon {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }

        .panel-title {
            font-size: 1.3rem;
            font-weight: 700;
        }

        .panel-body {
            padding: 2rem;
        }

        /* النماذج */
        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            font-weight: 600;
            color: #e0e6ed;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }

        .form-input, .form-select {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            color: #e0e6ed;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: #3498db;
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }

        .form-input::placeholder {
            color: rgba(224, 230, 237, 0.5);
        }

        /* الأزرار */
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60, #229954);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }

        .btn:disabled {
            background: #7f8c8d;
            cursor: not-allowed;
            transform: none;
        }

        .btn-full {
            width: 100%;
            justify-content: center;
        }

        /* جداول البيانات */
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            overflow: hidden;
        }

        .data-table th,
        .data-table td {
            padding: 0.75rem;
            text-align: right;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .data-table th {
            background: rgba(255, 255, 255, 0.1);
            font-weight: 600;
            font-size: 0.9rem;
        }

        .data-table td {
            font-size: 0.85rem;
        }

        .data-table tr:hover {
            background: rgba(255, 255, 255, 0.05);
        }

        /* حالات المستخدمين */
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-online {
            background: rgba(39, 174, 96, 0.2);
            color: #27ae60;
            border: 1px solid #27ae60;
        }

        .status-offline {
            background: rgba(149, 165, 166, 0.2);
            color: #95a5a6;
            border: 1px solid #95a5a6;
        }

        .status-suspended {
            background: rgba(231, 76, 60, 0.2);
            color: #e74c3c;
            border: 1px solid #e74c3c;
        }

        .status-expired {
            background: rgba(243, 156, 18, 0.2);
            color: #f39c12;
            border: 1px solid #f39c12;
        }

        /* أزرار الإجراءات */
        .action-buttons {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .action-btn {
            padding: 0.25rem 0.5rem;
            border: none;
            border-radius: 4px;
            font-size: 0.75rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .action-btn.activate {
            background: #27ae60;
            color: white;
        }

        .action-btn.suspend {
            background: #e74c3c;
            color: white;
        }

        .action-btn.delete {
            background: #c0392b;
            color: white;
        }

        .action-btn.info {
            background: #3498db;
            color: white;
        }

        .action-btn:hover {
            transform: scale(1.05);
        }

        /* إشعارات */
        .notification {
            position: fixed;
            top: 20px;
            left: 20px;
            background: white;
            color: #2c3e50;
            border-radius: 12px;
            padding: 1rem 1.5rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            z-index: 10000;
            transform: translateX(-400px);
            opacity: 0;
            transition: all 0.4s ease;
            min-width: 320px;
            border-left: 5px solid #3498db;
        }

        .notification.show {
            transform: translateX(0);
            opacity: 1;
        }

        .notification.success { border-left-color: #27ae60; }
        .notification.error { border-left-color: #e74c3c; }
        .notification.warning { border-left-color: #f39c12; }

        .notification-content {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        /* تحميل */
        .loading-spinner {
            display: none;
            width: 16px;
            height: 16px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* استجابة للشاشات الصغيرة */
        @media (max-width: 768px) {
            .tools-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .main-container {
                padding: 0 1rem;
            }
            
            .action-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <!-- الرأس الرئيسي -->
    <header class="main-header">
        <div class="header-content">
            <div class="header-title">
                <div class="header-logo">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <div>
                    <h1 style="font-size: 1.5rem; font-weight: 700;">لوحة التحكم الإدارية</h1>
                    <div style="font-size: 0.9rem; opacity: 0.8;">Remote Control & License Management</div>
                </div>
            </div>
            <div class="header-info">
                <div>المدير: Admin</div>
                <div>آخر دخول: <span id="lastLogin">الآن</span></div>
            </div>
        </div>
    </header>

    <!-- شريط الإحصائيات -->
    <div class="stats-bar">
        <div class="stats-grid">
            <div class="stat-card licenses">
                <div class="stat-number" id="totalLicenses">0</div>
                <div class="stat-label">إجمالي التراخيص</div>
            </div>
            <div class="stat-card devices">
                <div class="stat-number" id="totalDevices">0</div>
                <div class="stat-label">الأجهزة المسجلة</div>
            </div>
            <div class="stat-card active">
                <div class="stat-number" id="activeUsers">0</div>
                <div class="stat-label">المستخدمون النشطون</div>
            </div>
            <div class="stat-card expired">
                <div class="stat-number" id="expiredLicenses">0</div>
                <div class="stat-label">التراخيص المنتهية</div>
            </div>
        </div>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="main-container">
        <!-- شبكة الأدوات -->
        <div class="tools-grid">
            <!-- لوحة توليد التراخيص -->
            <div class="tool-panel">
                <div class="panel-header">
                    <div class="panel-icon">
                        <i class="fas fa-key"></i>
                    </div>
                    <div class="panel-title">توليد التراخيص</div>
                </div>
                <div class="panel-body">
                    <form id="licenseGeneratorForm">
                        <div class="form-group">
                            <label class="form-label">نوع الترخيص</label>
                            <select class="form-select" id="licenseType">
                                <option value="demo">تجريبي (30 يوم)</option>
                                <option value="monthly">شهري</option>
                                <option value="yearly">سنوي</option>
                                <option value="lifetime">مدى الحياة</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">عدد التراخيص</label>
                            <input type="number" class="form-input" id="licenseCount" value="1" min="1" max="100">
                        </div>
                        <div class="form-group">
                            <label class="form-label">ملاحظات</label>
                            <input type="text" class="form-input" id="licenseNotes" placeholder="ملاحظات اختيارية">
                        </div>
                        <button type="submit" class="btn btn-primary btn-full">
                            <i class="fas fa-plus"></i>
                            توليد التراخيص
                            <div class="loading-spinner" id="generateSpinner"></div>
                        </button>
                    </form>

                    <div id="generatedLicenses" style="margin-top: 1rem; display: none;">
                        <h4 style="margin-bottom: 0.5rem; color: #3498db;">التراخيص المولدة:</h4>
                        <div id="licensesList"></div>
                    </div>
                </div>
            </div>

            <!-- لوحة التحكم عن بُعد -->
            <div class="tool-panel">
                <div class="panel-header danger">
                    <div class="panel-icon">
                        <i class="fas fa-satellite-dish"></i>
                    </div>
                    <div class="panel-title">التحكم عن بُعد</div>
                </div>
                <div class="panel-body">
                    <div class="form-group">
                        <label class="form-label">معرف الجهاز المستهدف</label>
                        <input type="text" class="form-input" id="targetDeviceId" placeholder="أدخل معرف الجهاز">
                    </div>

                    <div class="action-buttons" style="margin-bottom: 1rem;">
                        <button class="btn btn-success" onclick="remoteActivate()">
                            <i class="fas fa-play"></i> تفعيل
                        </button>
                        <button class="btn btn-warning" onclick="remoteSuspend()">
                            <i class="fas fa-pause"></i> إيقاف مؤقت
                        </button>
                        <button class="btn btn-danger" onclick="remoteShutdown()">
                            <i class="fas fa-power-off"></i> إيقاف نهائي
                        </button>
                    </div>

                    <div class="action-buttons">
                        <button class="btn btn-primary" onclick="getDeviceInfo()">
                            <i class="fas fa-info-circle"></i> معلومات الجهاز
                        </button>
                        <button class="btn btn-danger" onclick="deleteDevice()">
                            <i class="fas fa-trash"></i> حذف الجهاز
                        </button>
                    </div>
                </div>
            </div>

            <!-- لوحة إدارة المستخدمين -->
            <div class="tool-panel">
                <div class="panel-header success">
                    <div class="panel-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="panel-title">إدارة المستخدمين</div>
                </div>
                <div class="panel-body">
                    <div class="form-group">
                        <button class="btn btn-primary btn-full" onclick="refreshUserData()">
                            <i class="fas fa-sync"></i> تحديث بيانات المستخدمين
                        </button>
                    </div>

                    <div class="form-group">
                        <label class="form-label">البحث عن مستخدم</label>
                        <input type="text" class="form-input" id="userSearch" placeholder="ابحث بمعرف الجهاز أو الترخيص" oninput="searchUsers()">
                    </div>
                </div>
            </div>

            <!-- لوحة النسخ الاحتياطي -->
            <div class="tool-panel">
                <div class="panel-header warning">
                    <div class="panel-icon">
                        <i class="fas fa-database"></i>
                    </div>
                    <div class="panel-title">النسخ الاحتياطي</div>
                </div>
                <div class="panel-body">
                    <div class="action-buttons" style="margin-bottom: 1rem;">
                        <button class="btn btn-primary" onclick="createBackup()">
                            <i class="fas fa-download"></i> إنشاء نسخة احتياطية
                        </button>
                        <button class="btn btn-warning" onclick="document.getElementById('backupFile').click()">
                            <i class="fas fa-upload"></i> استعادة نسخة احتياطية
                        </button>
                    </div>

                    <input type="file" id="backupFile" accept=".json" style="display: none;" onchange="restoreBackup(this)">

                    <div class="action-buttons">
                        <button class="btn btn-danger" onclick="clearAllData()">
                            <i class="fas fa-exclamation-triangle"></i> مسح جميع البيانات
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- جدول المستخدمين والأجهزة -->
        <div class="tool-panel" style="margin-top: 2rem;">
            <div class="panel-header">
                <div class="panel-icon">
                    <i class="fas fa-table"></i>
                </div>
                <div class="panel-title">المستخدمون والأجهزة المتصلة</div>
            </div>
            <div class="panel-body">
                <table class="data-table" id="usersTable">
                    <thead>
                        <tr>
                            <th>معرف الجهاز</th>
                            <th>كود الترخيص</th>
                            <th>نوع الترخيص</th>
                            <th>الحالة</th>
                            <th>آخر نشاط</th>
                            <th>تاريخ الانتهاء</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="usersTableBody">
                        <tr>
                            <td colspan="7" style="text-align: center; padding: 2rem; opacity: 0.6;">
                                لا توجد بيانات متاحة
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        // بيانات النظام
        let systemData = {
            licenses: [],
            devices: [],
            users: [],
            sessions: []
        };

        // تهيئة النظام
        document.addEventListener('DOMContentLoaded', function() {
            initializeSystem();
            loadSystemData();
            updateStatistics();
            loadUsersTable();

            // ربط الأحداث
            document.getElementById('licenseGeneratorForm').addEventListener('submit', handleLicenseGeneration);

            // تحديث الوقت
            document.getElementById('lastLogin').textContent = new Date().toLocaleString('ar-SA');

            // تحديث دوري للبيانات
            setInterval(updateStatistics, 30000); // كل 30 ثانية
        });

        // تهيئة النظام
        function initializeSystem() {
            // تحميل البيانات من التخزين المحلي
            const storedLicenses = localStorage.getItem('validLicenses');
            const storedDevices = localStorage.getItem('devices');
            const storedSessions = localStorage.getItem('activeSessions');

            systemData.licenses = storedLicenses ? JSON.parse(storedLicenses) : [];
            systemData.devices = storedDevices ? JSON.parse(storedDevices) : [];
            systemData.sessions = storedSessions ? JSON.parse(storedSessions) : [];

            // إضافة بيانات تجريبية إذا لم تكن موجودة
            if (systemData.licenses.length === 0) {
                addDefaultLicenses();
            }
        }

        // إضافة تراخيص افتراضية
        function addDefaultLicenses() {
            const defaultLicenses = [
                {
                    code: 'DEMO-2024-TEST-0001',
                    type: 'demo',
                    expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
                    isActive: true,
                    deviceId: null,
                    activatedAt: null,
                    createdAt: new Date().toISOString(),
                    notes: 'ترخيص تجريبي افتراضي'
                },
                {
                    code: 'FULL-2024-PROD-0001',
                    type: 'yearly',
                    expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
                    isActive: true,
                    deviceId: null,
                    activatedAt: null,
                    createdAt: new Date().toISOString(),
                    notes: 'ترخيص سنوي افتراضي'
                }
            ];

            systemData.licenses = defaultLicenses;
            localStorage.setItem('validLicenses', JSON.stringify(systemData.licenses));
        }

        // تحميل بيانات النظام
        function loadSystemData() {
            // محاكاة تحميل البيانات من الخادم
            console.log('تحميل بيانات النظام...');

            // تحديث بيانات المستخدمين النشطين
            updateActiveUsers();
        }

        // تحديث المستخدمين النشطين
        function updateActiveUsers() {
            const activeLicenses = systemData.licenses.filter(l => l.deviceId && l.isActive);
            systemData.users = activeLicenses.map(license => ({
                deviceId: license.deviceId,
                licenseCode: license.code,
                licenseType: license.type,
                status: getDeviceStatus(license.deviceId),
                lastActivity: license.activatedAt || new Date().toISOString(),
                expiresAt: license.expiresAt,
                isOnline: Math.random() > 0.3 // محاكاة الحالة
            }));
        }

        // الحصول على حالة الجهاز
        function getDeviceStatus(deviceId) {
            const device = systemData.devices.find(d => d.id === deviceId);
            if (!device) return 'offline';

            const license = systemData.licenses.find(l => l.deviceId === deviceId);
            if (!license) return 'offline';

            if (!license.isActive) return 'suspended';
            if (new Date(license.expiresAt) < new Date()) return 'expired';

            return Math.random() > 0.3 ? 'online' : 'offline';
        }

        // تحديث الإحصائيات
        function updateStatistics() {
            const totalLicenses = systemData.licenses.length;
            const totalDevices = systemData.licenses.filter(l => l.deviceId).length;
            const activeUsers = systemData.users.filter(u => u.status === 'online').length;
            const expiredLicenses = systemData.licenses.filter(l => new Date(l.expiresAt) < new Date()).length;

            document.getElementById('totalLicenses').textContent = totalLicenses;
            document.getElementById('totalDevices').textContent = totalDevices;
            document.getElementById('activeUsers').textContent = activeUsers;
            document.getElementById('expiredLicenses').textContent = expiredLicenses;
        }

        // معالجة توليد التراخيص
        async function handleLicenseGeneration(event) {
            event.preventDefault();

            const licenseType = document.getElementById('licenseType').value;
            const licenseCount = parseInt(document.getElementById('licenseCount').value);
            const licenseNotes = document.getElementById('licenseNotes').value;

            if (licenseCount < 1 || licenseCount > 100) {
                showNotification('عدد التراخيص يجب أن يكون بين 1 و 100', 'error');
                return;
            }

            const spinner = document.getElementById('generateSpinner');
            const submitBtn = event.target.querySelector('button[type="submit"]');

            spinner.style.display = 'inline-block';
            submitBtn.disabled = true;

            try {
                await new Promise(resolve => setTimeout(resolve, 1500));

                const generatedLicenses = [];

                for (let i = 0; i < licenseCount; i++) {
                    const license = generateNewLicense(licenseType, licenseNotes);
                    generatedLicenses.push(license);
                    systemData.licenses.push(license);
                }

                // حفظ التراخيص
                localStorage.setItem('validLicenses', JSON.stringify(systemData.licenses));

                // عرض التراخيص المولدة
                displayGeneratedLicenses(generatedLicenses);

                // تحديث الإحصائيات
                updateStatistics();

                showNotification(`تم توليد ${licenseCount} ترخيص بنجاح`, 'success');

            } catch (error) {
                showNotification('حدث خطأ في توليد التراخيص', 'error');
            } finally {
                spinner.style.display = 'none';
                submitBtn.disabled = false;
            }
        }

        // توليد ترخيص جديد
        function generateNewLicense(type, notes) {
            const now = new Date();
            let expiresAt = new Date();

            switch (type) {
                case 'demo':
                    expiresAt.setDate(now.getDate() + 30);
                    break;
                case 'monthly':
                    expiresAt.setMonth(now.getMonth() + 1);
                    break;
                case 'yearly':
                    expiresAt.setFullYear(now.getFullYear() + 1);
                    break;
                case 'lifetime':
                    expiresAt.setFullYear(now.getFullYear() + 100);
                    break;
            }

            return {
                code: generateLicenseCode(),
                type: type,
                expiresAt: expiresAt.toISOString(),
                isActive: true,
                deviceId: null,
                activatedAt: null,
                createdAt: now.toISOString(),
                notes: notes || ''
            };
        }

        // توليد كود ترخيص
        function generateLicenseCode() {
            const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
            let result = '';

            for (let i = 0; i < 4; i++) {
                if (i > 0) result += '-';
                for (let j = 0; j < 4; j++) {
                    result += chars.charAt(Math.floor(Math.random() * chars.length));
                }
            }

            return result;
        }

        // عرض التراخيص المولدة
        function displayGeneratedLicenses(licenses) {
            const container = document.getElementById('generatedLicenses');
            const listContainer = document.getElementById('licensesList');

            listContainer.innerHTML = '';

            licenses.forEach(license => {
                const item = document.createElement('div');
                item.style.cssText = 'background: rgba(52, 152, 219, 0.1); padding: 0.5rem; margin: 0.25rem 0; border-radius: 4px; font-family: monospace; font-size: 0.8rem; display: flex; justify-content: space-between; align-items: center;';
                item.innerHTML = `
                    <span>${license.code}</span>
                    <button onclick="copyToClipboard('${license.code}')" style="background: #3498db; color: white; border: none; padding: 0.25rem 0.5rem; border-radius: 4px; cursor: pointer; font-size: 0.7rem;">
                        <i class="fas fa-copy"></i>
                    </button>
                `;
                listContainer.appendChild(item);
            });

            container.style.display = 'block';
        }

        // وظائف التحكم عن بُعد
        function remoteActivate() {
            const deviceId = document.getElementById('targetDeviceId').value.trim();
            if (!deviceId) {
                showNotification('يرجى إدخال معرف الجهاز', 'warning');
                return;
            }

            // محاكاة التفعيل عن بُعد
            const license = systemData.licenses.find(l => l.deviceId === deviceId);
            if (license) {
                license.isActive = true;
                localStorage.setItem('validLicenses', JSON.stringify(systemData.licenses));
                showNotification(`تم تفعيل الجهاز: ${deviceId}`, 'success');
                loadUsersTable();
            } else {
                showNotification('الجهاز غير موجود', 'error');
            }
        }

        function remoteSuspend() {
            const deviceId = document.getElementById('targetDeviceId').value.trim();
            if (!deviceId) {
                showNotification('يرجى إدخال معرف الجهاز', 'warning');
                return;
            }

            const license = systemData.licenses.find(l => l.deviceId === deviceId);
            if (license) {
                license.isActive = false;
                localStorage.setItem('validLicenses', JSON.stringify(systemData.licenses));
                showNotification(`تم إيقاف الجهاز مؤقتاً: ${deviceId}`, 'warning');
                loadUsersTable();
            } else {
                showNotification('الجهاز غير موجود', 'error');
            }
        }

        function remoteShutdown() {
            const deviceId = document.getElementById('targetDeviceId').value.trim();
            if (!deviceId) {
                showNotification('يرجى إدخال معرف الجهاز', 'warning');
                return;
            }

            if (!confirm(`هل أنت متأكد من إيقاف الجهاز نهائياً: ${deviceId}؟`)) {
                return;
            }

            const license = systemData.licenses.find(l => l.deviceId === deviceId);
            if (license) {
                license.isActive = false;
                license.deviceId = null;
                license.activatedAt = null;
                localStorage.setItem('validLicenses', JSON.stringify(systemData.licenses));
                showNotification(`تم إيقاف الجهاز نهائياً: ${deviceId}`, 'error');
                loadUsersTable();
            } else {
                showNotification('الجهاز غير موجود', 'error');
            }
        }

        function getDeviceInfo() {
            const deviceId = document.getElementById('targetDeviceId').value.trim();
            if (!deviceId) {
                showNotification('يرجى إدخال معرف الجهاز', 'warning');
                return;
            }

            const license = systemData.licenses.find(l => l.deviceId === deviceId);
            if (license) {
                const info = `
معرف الجهاز: ${deviceId}
كود الترخيص: ${license.code}
نوع الترخيص: ${getLicenseTypeName(license.type)}
الحالة: ${license.isActive ? 'نشط' : 'معطل'}
تاريخ التفعيل: ${license.activatedAt ? new Date(license.activatedAt).toLocaleDateString('ar-SA') : 'غير محدد'}
تاريخ الانتهاء: ${new Date(license.expiresAt).toLocaleDateString('ar-SA')}
                `;
                alert(info);
            } else {
                showNotification('الجهاز غير موجود', 'error');
            }
        }

        function deleteDevice() {
            const deviceId = document.getElementById('targetDeviceId').value.trim();
            if (!deviceId) {
                showNotification('يرجى إدخال معرف الجهاز', 'warning');
                return;
            }

            if (!confirm(`هل أنت متأكد من حذف الجهاز: ${deviceId}؟`)) {
                return;
            }

            const licenseIndex = systemData.licenses.findIndex(l => l.deviceId === deviceId);
            if (licenseIndex !== -1) {
                systemData.licenses[licenseIndex].deviceId = null;
                systemData.licenses[licenseIndex].activatedAt = null;
                localStorage.setItem('validLicenses', JSON.stringify(systemData.licenses));
                showNotification(`تم حذف الجهاز: ${deviceId}`, 'success');
                loadUsersTable();
                document.getElementById('targetDeviceId').value = '';
            } else {
                showNotification('الجهاز غير موجود', 'error');
            }
        }

        // تحديث بيانات المستخدمين
        function refreshUserData() {
            loadSystemData();
            updateActiveUsers();
            loadUsersTable();
            updateStatistics();
            showNotification('تم تحديث بيانات المستخدمين', 'success');
        }

        // البحث عن المستخدمين
        function searchUsers() {
            const searchTerm = document.getElementById('userSearch').value.toLowerCase();
            const filteredUsers = systemData.users.filter(user =>
                user.deviceId.toLowerCase().includes(searchTerm) ||
                user.licenseCode.toLowerCase().includes(searchTerm)
            );
            loadUsersTable(filteredUsers);
        }

        // تحميل جدول المستخدمين
        function loadUsersTable(users = null) {
            const usersToShow = users || systemData.users;
            const tbody = document.getElementById('usersTableBody');

            if (usersToShow.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" style="text-align: center; padding: 2rem; opacity: 0.6;">
                            لا توجد بيانات متاحة
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = '';

            usersToShow.forEach(user => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td style="font-family: monospace; font-size: 0.8rem;">${user.deviceId}</td>
                    <td style="font-family: monospace; font-size: 0.8rem;">${user.licenseCode}</td>
                    <td>${getLicenseTypeName(user.licenseType)}</td>
                    <td><span class="status-badge status-${user.status}">${getStatusName(user.status)}</span></td>
                    <td>${new Date(user.lastActivity).toLocaleDateString('ar-SA')}</td>
                    <td>${new Date(user.expiresAt).toLocaleDateString('ar-SA')}</td>
                    <td>
                        <div class="action-buttons">
                            <button class="action-btn activate" onclick="activateUser('${user.deviceId}')" title="تفعيل">
                                <i class="fas fa-play"></i>
                            </button>
                            <button class="action-btn suspend" onclick="suspendUser('${user.deviceId}')" title="إيقاف">
                                <i class="fas fa-pause"></i>
                            </button>
                            <button class="action-btn delete" onclick="deleteUser('${user.deviceId}')" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                            <button class="action-btn info" onclick="showUserInfo('${user.deviceId}')" title="معلومات">
                                <i class="fas fa-info"></i>
                            </button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // وظائف إدارة المستخدمين
        function activateUser(deviceId) {
            document.getElementById('targetDeviceId').value = deviceId;
            remoteActivate();
        }

        function suspendUser(deviceId) {
            document.getElementById('targetDeviceId').value = deviceId;
            remoteSuspend();
        }

        function deleteUser(deviceId) {
            document.getElementById('targetDeviceId').value = deviceId;
            deleteDevice();
        }

        function showUserInfo(deviceId) {
            document.getElementById('targetDeviceId').value = deviceId;
            getDeviceInfo();
        }

        // وظائف النسخ الاحتياطي
        function createBackup() {
            const backupData = {
                timestamp: new Date().toISOString(),
                version: '2.2.0',
                data: {
                    licenses: systemData.licenses,
                    devices: systemData.devices,
                    sessions: systemData.sessions
                }
            };

            const dataStr = JSON.stringify(backupData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `admin_backup_${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            showNotification('تم إنشاء النسخة الاحتياطية', 'success');
        }

        function restoreBackup(input) {
            const file = input.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const backupData = JSON.parse(e.target.result);

                    if (backupData.data) {
                        systemData.licenses = backupData.data.licenses || [];
                        systemData.devices = backupData.data.devices || [];
                        systemData.sessions = backupData.data.sessions || [];

                        localStorage.setItem('validLicenses', JSON.stringify(systemData.licenses));
                        localStorage.setItem('devices', JSON.stringify(systemData.devices));
                        localStorage.setItem('activeSessions', JSON.stringify(systemData.sessions));

                        refreshUserData();
                        showNotification('تم استعادة النسخة الاحتياطية بنجاح', 'success');
                    } else {
                        showNotification('ملف النسخة الاحتياطية غير صالح', 'error');
                    }
                } catch (error) {
                    showNotification('خطأ في قراءة ملف النسخة الاحتياطية', 'error');
                }
            };
            reader.readAsText(file);
        }

        function clearAllData() {
            if (!confirm('تحذير: سيتم حذف جميع البيانات نهائياً. هل تريد المتابعة؟')) {
                return;
            }

            if (!confirm('هذا الإجراء لا يمكن التراجع عنه. هل أنت متأكد؟')) {
                return;
            }

            systemData = { licenses: [], devices: [], users: [], sessions: [] };
            localStorage.removeItem('validLicenses');
            localStorage.removeItem('devices');
            localStorage.removeItem('activeSessions');

            addDefaultLicenses();
            refreshUserData();
            showNotification('تم مسح جميع البيانات', 'warning');
        }

        // وظائف مساعدة
        function getLicenseTypeName(type) {
            const names = {
                'demo': 'تجريبي',
                'monthly': 'شهري',
                'yearly': 'سنوي',
                'lifetime': 'مدى الحياة'
            };
            return names[type] || type;
        }

        function getStatusName(status) {
            const names = {
                'online': 'متصل',
                'offline': 'غير متصل',
                'suspended': 'معلق',
                'expired': 'منتهي'
            };
            return names[status] || status;
        }

        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                showNotification('تم نسخ النص', 'success');
            }).catch(() => {
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showNotification('تم نسخ النص', 'success');
            });
        }

        // نظام الإشعارات
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.innerHTML = `
                <div class="notification-content">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
                    <span>${message}</span>
                </div>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.classList.add('show');
            }, 100);

            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    if (notification.parentElement) {
                        notification.remove();
                    }
                }, 400);
            }, 4000);
        }
    </script>
</body>
</html>
