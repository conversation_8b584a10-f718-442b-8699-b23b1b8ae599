@echo off
chcp 65001 >nul
title مؤسسة وقود المستقبل - التشغيل التلقائي

echo ================================================================================
echo                    مؤسسة وقود المستقبل - إعداد التشغيل التلقائي
echo ================================================================================
echo.
echo هذا المعالج سيقوم بإعداد التطبيق للتشغيل التلقائي مع Windows
echo.

set /p "AUTOSTART=هل تريد تفعيل التشغيل التلقائي؟ (Y/N): "
if /i not "%AUTOSTART%"=="Y" if /i not "%AUTOSTART%"=="نعم" (
    echo تم إلغاء العملية
    pause
    exit /b 0
)

echo.
echo 🔧 إعداد التشغيل التلقائي...

:: الحصول على مسار التطبيق
set "APP_PATH=%~dp0"
set "APP_PATH=%APP_PATH:~0,-1%"

:: إنشاء اختصار في مجلد بدء التشغيل
set "STARTUP_FOLDER=%APPDATA%\Microsoft\Windows\Start Menu\Programs\Startup"

:: إنشاء ملف VBS للتشغيل المخفي
(
echo Set WshShell = CreateObject^("WScript.Shell"^)
echo WshShell.Run """%APP_PATH%\FutureFuel.vbs""", 0, False
) > "%STARTUP_FOLDER%\FutureFuel_AutoStart.vbs"

:: تسجيل في الريجستري كبديل
reg add "HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Run" /v "FutureFuel" /t REG_SZ /d "\"%APP_PATH%\FutureFuel.vbs\"" /f >nul

echo ✓ تم إعداد التشغيل التلقائي بنجاح
echo.
echo سيتم تشغيل التطبيق تلقائياً عند بدء تشغيل Windows
echo.

set /p "DISABLE=هل تريد إلغاء التشغيل التلقائي؟ (Y/N): "
if /i "%DISABLE%"=="Y" if /i "%DISABLE%"=="نعم" (
    echo.
    echo 🗑️ إلغاء التشغيل التلقائي...
    
    del "%STARTUP_FOLDER%\FutureFuel_AutoStart.vbs" >nul 2>&1
    reg delete "HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Run" /v "FutureFuel" /f >nul 2>&1
    
    echo ✓ تم إلغاء التشغيل التلقائي
)

echo.
echo ================================================================================
echo.
pause
