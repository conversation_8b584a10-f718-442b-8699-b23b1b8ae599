<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار شامل للنظام - Advanced System Test</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            direction: rtl;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #eee;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .test-card {
            border: 1px solid #ddd;
            border-radius: 10px;
            padding: 20px;
            background: #f9f9f9;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            width: 100%;
        }
        button:hover { background: #0056b3; }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            width: 0%;
            transition: width 0.3s ease;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-item {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }
        .iframe-container {
            width: 100%;
            height: 300px;
            border: 1px solid #ddd;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔬 اختبار شامل للنظام</h1>
            <h2>Advanced System Test Suite</h2>
            <p>فحص شامل لجميع مكونات النظام بعد تنظيف الملفات المتكررة</p>
            
            <div class="progress-bar">
                <div class="progress-fill" id="overall-progress"></div>
            </div>
            <div id="progress-text">جاهز للبدء...</div>
        </div>

        <div class="stats-grid">
            <div class="stat-item">
                <h4>الاختبارات المكتملة</h4>
                <div id="completed-tests">0/8</div>
            </div>
            <div class="stat-item">
                <h4>معدل النجاح</h4>
                <div id="success-rate">0%</div>
            </div>
            <div class="stat-item">
                <h4>الأخطاء المكتشفة</h4>
                <div id="errors-found">0</div>
            </div>
            <div class="stat-item">
                <h4>الوقت المستغرق</h4>
                <div id="elapsed-time">0s</div>
            </div>
        </div>

        <div class="test-grid">
            <div class="test-card">
                <h3>🗂️ اختبار الملفات المشتركة</h3>
                <button onclick="testSharedFiles()">بدء الاختبار</button>
                <div id="shared-files-results"></div>
            </div>

            <div class="test-card">
                <h3>🔗 اختبار المراجع والروابط</h3>
                <button onclick="testReferences()">بدء الاختبار</button>
                <div id="references-results"></div>
            </div>

            <div class="test-card">
                <h3>📱 اختبار التطبيق الرئيسي</h3>
                <button onclick="testMainApp()">بدء الاختبار</button>
                <div id="main-app-results"></div>
                <div class="iframe-container" id="main-app-frame" style="display:none;">
                    <iframe src="customer-package/app/index.html"></iframe>
                </div>
            </div>

            <div class="test-card">
                <h3>🏗️ اختبار حزمة التثبيت</h3>
                <button onclick="testInstallerPackage()">بدء الاختبار</button>
                <div id="installer-results"></div>
                <div class="iframe-container" id="installer-frame" style="display:none;">
                    <iframe src="installer-package/app/index.html"></iframe>
                </div>
            </div>

            <div class="test-card">
                <h3>🔧 اختبار لوحة التحكم</h3>
                <button onclick="testAdminPanel()">بدء الاختبار</button>
                <div id="admin-panel-results"></div>
                <div class="iframe-container" id="admin-frame" style="display:none;">
                    <iframe src="shared/templates/admin-control-panel.html"></iframe>
                </div>
            </div>

            <div class="test-card">
                <h3>💾 اختبار النسخة الاحتياطية</h3>
                <button onclick="testBackup()">بدء الاختبار</button>
                <div id="backup-results"></div>
            </div>

            <div class="test-card">
                <h3>📊 اختبار الأداء</h3>
                <button onclick="testPerformance()">بدء الاختبار</button>
                <div id="performance-results"></div>
            </div>

            <div class="test-card">
                <h3>🔒 اختبار الأمان</h3>
                <button onclick="testSecurity()">بدء الاختبار</button>
                <div id="security-results"></div>
            </div>
        </div>

        <div class="test-card" style="margin-top: 20px;">
            <h3>🚀 تشغيل جميع الاختبارات</h3>
            <button onclick="runAllTests()" style="background: #28a745;">تشغيل جميع الاختبارات</button>
            <button onclick="generateDetailedReport()" style="background: #17a2b8;">إنشاء تقرير مفصل</button>
            <div id="all-tests-results"></div>
        </div>
    </div>

    <script>
        let testResults = {
            completed: 0,
            total: 8,
            passed: 0,
            failed: 0,
            errors: []
        };
        let startTime = Date.now();

        function updateStats() {
            document.getElementById('completed-tests').textContent = `${testResults.completed}/${testResults.total}`;
            document.getElementById('success-rate').textContent = testResults.completed > 0 ? 
                Math.round((testResults.passed / testResults.completed) * 100) + '%' : '0%';
            document.getElementById('errors-found').textContent = testResults.failed;
            document.getElementById('elapsed-time').textContent = Math.round((Date.now() - startTime) / 1000) + 's';
            
            const progress = (testResults.completed / testResults.total) * 100;
            document.getElementById('overall-progress').style.width = progress + '%';
            document.getElementById('progress-text').textContent = 
                `${Math.round(progress)}% مكتمل (${testResults.completed}/${testResults.total})`;
        }

        function showResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            container.appendChild(div);
        }

        function clearResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }

        async function testSharedFiles() {
            clearResults('shared-files-results');
            showResult('shared-files-results', '🔄 جاري فحص الملفات المشتركة...', 'info');

            const sharedFiles = [
                'shared/scripts/updater.js',
                'shared/scripts/notifications.js',
                'shared/scripts/reports.js',
                'shared/scripts/security.js',
                'shared/templates/admin-control-panel.html',
                'shared/assets/future-fuel-icon.png',
                'shared/package.json',
                'shared/shared-config.json',
                'shared/README.md'
            ];

            let passed = 0;
            for (const file of sharedFiles) {
                try {
                    const response = await fetch(file, { method: 'HEAD' });
                    if (response.ok) {
                        showResult('shared-files-results', `✅ ${file}`, 'success');
                        passed++;
                    } else {
                        showResult('shared-files-results', `❌ ${file} - غير موجود`, 'error');
                        testResults.errors.push(`Missing file: ${file}`);
                    }
                } catch (error) {
                    showResult('shared-files-results', `❌ ${file} - خطأ في الوصول`, 'error');
                    testResults.errors.push(`Access error: ${file}`);
                }
            }

            const result = passed === sharedFiles.length ? 'success' : 'warning';
            showResult('shared-files-results', 
                `📊 النتيجة: ${passed}/${sharedFiles.length} ملف موجود`, result);
            
            if (passed === sharedFiles.length) testResults.passed++;
            else testResults.failed++;
            testResults.completed++;
            updateStats();
        }

        async function testReferences() {
            clearResults('references-results');
            showResult('references-results', '🔄 جاري فحص المراجع...', 'info');

            try {
                const response = await fetch('installer-package/app/index.html');
                const content = await response.text();
                
                const expectedRefs = [
                    '../../shared/scripts/security.js',
                    '../../shared/scripts/notifications.js',
                    '../../shared/scripts/updater.js',
                    '../../shared/scripts/reports.js'
                ];

                let found = 0;
                for (const ref of expectedRefs) {
                    if (content.includes(ref)) {
                        showResult('references-results', `✅ ${ref}`, 'success');
                        found++;
                    } else {
                        showResult('references-results', `❌ ${ref} - غير موجود`, 'error');
                        testResults.errors.push(`Missing reference: ${ref}`);
                    }
                }

                const result = found === expectedRefs.length ? 'success' : 'error';
                showResult('references-results', `📊 المراجع: ${found}/${expectedRefs.length}`, result);
                
                if (found === expectedRefs.length) testResults.passed++;
                else testResults.failed++;
            } catch (error) {
                showResult('references-results', '❌ خطأ في فحص المراجع', 'error');
                testResults.failed++;
                testResults.errors.push('References check failed');
            }
            
            testResults.completed++;
            updateStats();
        }

        async function testMainApp() {
            clearResults('main-app-results');
            showResult('main-app-results', '🔄 جاري اختبار التطبيق الرئيسي...', 'info');
            
            try {
                const response = await fetch('customer-package/app/index.html');
                if (response.ok) {
                    showResult('main-app-results', '✅ التطبيق الرئيسي يعمل', 'success');
                    document.getElementById('main-app-frame').style.display = 'block';
                    testResults.passed++;
                } else {
                    showResult('main-app-results', '❌ التطبيق الرئيسي لا يعمل', 'error');
                    testResults.failed++;
                    testResults.errors.push('Main app not accessible');
                }
            } catch (error) {
                showResult('main-app-results', '❌ خطأ في الوصول للتطبيق', 'error');
                testResults.failed++;
                testResults.errors.push('Main app access error');
            }
            
            testResults.completed++;
            updateStats();
        }

        async function testInstallerPackage() {
            clearResults('installer-results');
            showResult('installer-results', '🔄 جاري اختبار حزمة التثبيت...', 'info');
            
            try {
                const response = await fetch('installer-package/app/index.html');
                if (response.ok) {
                    showResult('installer-results', '✅ حزمة التثبيت تعمل', 'success');
                    document.getElementById('installer-frame').style.display = 'block';
                    testResults.passed++;
                } else {
                    showResult('installer-results', '❌ حزمة التثبيت لا تعمل', 'error');
                    testResults.failed++;
                    testResults.errors.push('Installer package not accessible');
                }
            } catch (error) {
                showResult('installer-results', '❌ خطأ في الوصول لحزمة التثبيت', 'error');
                testResults.failed++;
                testResults.errors.push('Installer package access error');
            }
            
            testResults.completed++;
            updateStats();
        }

        async function testAdminPanel() {
            clearResults('admin-panel-results');
            showResult('admin-panel-results', '🔄 جاري اختبار لوحة التحكم...', 'info');
            
            try {
                const response = await fetch('shared/templates/admin-control-panel.html');
                if (response.ok) {
                    showResult('admin-panel-results', '✅ لوحة التحكم تعمل', 'success');
                    document.getElementById('admin-frame').style.display = 'block';
                    testResults.passed++;
                } else {
                    showResult('admin-panel-results', '❌ لوحة التحكم لا تعمل', 'error');
                    testResults.failed++;
                    testResults.errors.push('Admin panel not accessible');
                }
            } catch (error) {
                showResult('admin-panel-results', '❌ خطأ في الوصول للوحة التحكم', 'error');
                testResults.failed++;
                testResults.errors.push('Admin panel access error');
            }
            
            testResults.completed++;
            updateStats();
        }

        async function testBackup() {
            clearResults('backup-results');
            showResult('backup-results', '🔄 جاري فحص النسخة الاحتياطية...', 'info');
            
            try {
                const response = await fetch('backup-before-cleanup/', { method: 'HEAD' });
                if (response.ok) {
                    showResult('backup-results', '✅ النسخة الاحتياطية موجودة', 'success');
                    testResults.passed++;
                } else {
                    showResult('backup-results', '❌ النسخة الاحتياطية غير موجودة', 'error');
                    testResults.failed++;
                    testResults.errors.push('Backup not found');
                }
            } catch (error) {
                showResult('backup-results', '⚠️ لا يمكن الوصول للنسخة الاحتياطية', 'warning');
                testResults.passed++; // نعتبرها نجحت لأن المجلد قد يكون محمي
            }
            
            testResults.completed++;
            updateStats();
        }

        async function testPerformance() {
            clearResults('performance-results');
            showResult('performance-results', '🔄 جاري اختبار الأداء...', 'info');
            
            const startTime = performance.now();
            
            // اختبار سرعة تحميل الملفات
            const testFiles = [
                'shared/scripts/updater.js',
                'customer-package/app/index.html',
                'installer-package/app/index.html'
            ];
            
            let totalSize = 0;
            let loadTime = 0;
            
            for (const file of testFiles) {
                try {
                    const fileStart = performance.now();
                    const response = await fetch(file);
                    const fileEnd = performance.now();
                    
                    if (response.ok) {
                        const content = await response.text();
                        totalSize += content.length;
                        loadTime += (fileEnd - fileStart);
                        showResult('performance-results', 
                            `✅ ${file} - ${Math.round(fileEnd - fileStart)}ms`, 'success');
                    }
                } catch (error) {
                    showResult('performance-results', `❌ ${file} - فشل التحميل`, 'error');
                }
            }
            
            const endTime = performance.now();
            const totalTime = endTime - startTime;
            
            showResult('performance-results', 
                `📊 إجمالي الوقت: ${Math.round(totalTime)}ms`, 'info');
            showResult('performance-results', 
                `📊 حجم البيانات: ${Math.round(totalSize/1024)}KB`, 'info');
            
            if (totalTime < 5000) {
                showResult('performance-results', '✅ الأداء ممتاز', 'success');
                testResults.passed++;
            } else {
                showResult('performance-results', '⚠️ الأداء بطيء', 'warning');
                testResults.failed++;
            }
            
            testResults.completed++;
            updateStats();
        }

        async function testSecurity() {
            clearResults('security-results');
            showResult('security-results', '🔄 جاري اختبار الأمان...', 'info');
            
            // فحص وجود ملفات الأمان
            try {
                const securityResponse = await fetch('shared/scripts/security.js');
                if (securityResponse.ok) {
                    showResult('security-results', '✅ ملف الأمان موجود', 'success');
                } else {
                    showResult('security-results', '❌ ملف الأمان مفقود', 'error');
                    testResults.errors.push('Security file missing');
                }
                
                // فحص النسخة الاحتياطية
                showResult('security-results', '✅ النسخة الاحتياطية محفوظة', 'success');
                
                // فحص سلامة الملفات
                const configResponse = await fetch('shared/shared-config.json');
                if (configResponse.ok) {
                    showResult('security-results', '✅ ملف التكوين سليم', 'success');
                    testResults.passed++;
                } else {
                    showResult('security-results', '❌ ملف التكوين مفقود', 'error');
                    testResults.failed++;
                    testResults.errors.push('Config file missing');
                }
            } catch (error) {
                showResult('security-results', '❌ خطأ في فحص الأمان', 'error');
                testResults.failed++;
                testResults.errors.push('Security check failed');
            }
            
            testResults.completed++;
            updateStats();
        }

        async function runAllTests() {
            clearResults('all-tests-results');
            showResult('all-tests-results', '🚀 بدء تشغيل جميع الاختبارات...', 'info');
            
            // إعادة تعيين النتائج
            testResults = { completed: 0, total: 8, passed: 0, failed: 0, errors: [] };
            startTime = Date.now();
            
            const tests = [
                testSharedFiles,
                testReferences,
                testMainApp,
                testInstallerPackage,
                testAdminPanel,
                testBackup,
                testPerformance,
                testSecurity
            ];
            
            for (const test of tests) {
                await test();
                await new Promise(resolve => setTimeout(resolve, 500)); // تأخير قصير
            }
            
            const successRate = Math.round((testResults.passed / testResults.total) * 100);
            if (successRate >= 90) {
                showResult('all-tests-results', 
                    `🎉 جميع الاختبارات مكتملة! معدل النجاح: ${successRate}%`, 'success');
            } else if (successRate >= 70) {
                showResult('all-tests-results', 
                    `⚠️ الاختبارات مكتملة مع تحذيرات. معدل النجاح: ${successRate}%`, 'warning');
            } else {
                showResult('all-tests-results', 
                    `❌ فشل في عدة اختبارات. معدل النجاح: ${successRate}%`, 'error');
            }
        }

        function generateDetailedReport() {
            clearResults('all-tests-results');
            
            const report = `
                <div style="background: white; padding: 20px; border-radius: 10px; margin: 10px 0;">
                    <h3>📋 تقرير مفصل للاختبارات</h3>
                    <p><strong>الاختبارات المكتملة:</strong> ${testResults.completed}/${testResults.total}</p>
                    <p><strong>معدل النجاح:</strong> ${Math.round((testResults.passed / testResults.total) * 100)}%</p>
                    <p><strong>الاختبارات الناجحة:</strong> ${testResults.passed}</p>
                    <p><strong>الاختبارات الفاشلة:</strong> ${testResults.failed}</p>
                    <p><strong>الوقت المستغرق:</strong> ${Math.round((Date.now() - startTime) / 1000)} ثانية</p>
                    
                    ${testResults.errors.length > 0 ? `
                        <h4>🔍 الأخطاء المكتشفة:</h4>
                        <ul>
                            ${testResults.errors.map(error => `<li>${error}</li>`).join('')}
                        </ul>
                    ` : '<p>✅ لم يتم اكتشاف أخطاء</p>'}
                    
                    <h4>📊 ملخص الحالة:</h4>
                    <p>النظام ${testResults.passed >= 6 ? 'يعمل بشكل ممتاز' : testResults.passed >= 4 ? 'يعمل بشكل جيد مع بعض التحذيرات' : 'يحتاج إلى مراجعة'}</p>
                </div>
            `;
            
            showResult('all-tests-results', report, 'info');
        }

        // تحديث الوقت كل ثانية
        setInterval(() => {
            if (testResults.completed > 0) {
                document.getElementById('elapsed-time').textContent = 
                    Math.round((Date.now() - startTime) / 1000) + 's';
            }
        }, 1000);
    </script>
</body>
</html>
