// Future Fuel Management System - Advanced Reports System
// Version 2.2.0 - Professional reporting and analytics

class ReportsManager {
    constructor() {
        this.reportTypes = {
            daily: 'تقرير يومي',
            weekly: 'تقرير أسبوعي',
            monthly: 'تقرير شهري',
            yearly: 'تقرير سنوي',
            custom: 'تقرير مخصص'
        };
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadReportTemplates();
    }

    setupEventListeners() {
        // Report generation buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-report-type]')) {
                const reportType = e.target.dataset.reportType;
                this.generateReport(reportType);
            }
        });
    }

    async generateReport(type, options = {}) {
        try {
            const {
                startDate = new Date(),
                endDate = new Date(),
                format = 'pdf',
                sections = ['all']
            } = options;

            // Show loading
            if (typeof notificationManager !== 'undefined') {
                notificationManager.info('تقارير', 'جاري إنشاء التقرير...');
            }

            const reportData = await this.collectReportData(type, startDate, endDate);
            const report = await this.buildReport(reportData, type, sections);

            if (format === 'pdf') {
                await this.exportToPDF(report, type);
            } else if (format === 'excel') {
                await this.exportToExcel(report, type);
            } else if (format === 'html') {
                this.showReportInModal(report, type);
            }

            if (typeof notificationManager !== 'undefined') {
                notificationManager.success('تقارير', 'تم إنشاء التقرير بنجاح');
            }

        } catch (error) {
            console.error('Error generating report:', error);
            if (typeof notificationManager !== 'undefined') {
                notificationManager.error('خطأ في التقرير', error.message);
            }
        }
    }

    async collectReportData(type, startDate, endDate) {
        const data = {
            period: {
                type: type,
                startDate: startDate,
                endDate: endDate,
                generatedAt: new Date()
            },
            summary: {},
            details: {}
        };

        // Collect data from appData
        if (typeof appData !== 'undefined') {
            // Gas Cards Data
            data.details.gasCards = this.filterDataByDate(
                appData.gasCards || [], 
                startDate, 
                endDate, 
                'issueDate'
            );

            // Appointments Data
            data.details.appointments = this.filterDataByDate(
                appData.appointments || [], 
                startDate, 
                endDate, 
                'date'
            );

            // Sales Data
            data.details.sales = this.filterDataByDate(
                appData.sales || [], 
                startDate, 
                endDate, 
                'date'
            );

            // Purchases Data
            data.details.purchases = this.filterDataByDate(
                appData.purchases || [], 
                startDate, 
                endDate, 
                'date'
            );

            // Debts Data
            data.details.debts = this.filterDataByDate(
                appData.debts || [], 
                startDate, 
                endDate, 
                'createdDate'
            );

            // Customers Data
            data.details.customers = appData.customers || [];
            data.details.suppliers = appData.suppliers || [];
            data.details.inventory = appData.inventory || [];

            // Calculate summaries
            data.summary = this.calculateSummaries(data.details);
        }

        return data;
    }

    filterDataByDate(dataArray, startDate, endDate, dateField) {
        return dataArray.filter(item => {
            const itemDate = new Date(item[dateField]);
            return itemDate >= startDate && itemDate <= endDate;
        });
    }

    calculateSummaries(details) {
        const summary = {
            gasCards: {
                total: details.gasCards.length,
                active: details.gasCards.filter(card => card.status === 'active').length,
                expired: details.gasCards.filter(card => card.status === 'expired').length,
                expiringSoon: details.gasCards.filter(card => {
                    const expiryDate = new Date(card.expiryDate);
                    const daysUntilExpiry = Math.ceil((expiryDate - new Date()) / (1000 * 60 * 60 * 24));
                    return daysUntilExpiry <= 30 && daysUntilExpiry > 0;
                }).length
            },
            appointments: {
                total: details.appointments.length,
                completed: details.appointments.filter(apt => apt.status === 'completed').length,
                pending: details.appointments.filter(apt => apt.status === 'pending').length,
                cancelled: details.appointments.filter(apt => apt.status === 'cancelled').length
            },
            sales: {
                total: details.sales.length,
                totalAmount: details.sales.reduce((sum, sale) => sum + (sale.totalAmount || 0), 0),
                averageAmount: details.sales.length > 0 ? 
                    details.sales.reduce((sum, sale) => sum + (sale.totalAmount || 0), 0) / details.sales.length : 0
            },
            purchases: {
                total: details.purchases.length,
                totalAmount: details.purchases.reduce((sum, purchase) => sum + (purchase.totalAmount || 0), 0),
                averageAmount: details.purchases.length > 0 ? 
                    details.purchases.reduce((sum, purchase) => sum + (purchase.totalAmount || 0), 0) / details.purchases.length : 0
            },
            debts: {
                total: details.debts.length,
                active: details.debts.filter(debt => debt.status === 'active').length,
                overdue: details.debts.filter(debt => {
                    const dueDate = new Date(debt.dueDate);
                    return dueDate < new Date() && debt.status === 'active';
                }).length,
                totalAmount: details.debts.reduce((sum, debt) => sum + (debt.remainingAmount || debt.amount || 0), 0)
            },
            customers: {
                total: details.customers.length,
                newCustomers: details.customers.filter(customer => {
                    const createdDate = new Date(customer.createdDate || customer.registrationDate);
                    const thirtyDaysAgo = new Date();
                    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
                    return createdDate >= thirtyDaysAgo;
                }).length
            },
            inventory: {
                totalItems: details.inventory.length,
                lowStock: details.inventory.filter(item => 
                    item.currentStock <= (item.minStock || 0)
                ).length,
                outOfStock: details.inventory.filter(item => 
                    item.currentStock <= 0
                ).length,
                totalValue: details.inventory.reduce((sum, item) => 
                    sum + ((item.currentStock || 0) * (item.purchasePrice || 0)), 0
                )
            }
        };

        return summary;
    }

    async buildReport(data, type, sections) {
        const report = {
            header: this.buildReportHeader(data, type),
            summary: this.buildSummarySection(data.summary),
            charts: await this.buildChartsSection(data),
            details: this.buildDetailsSection(data.details, sections),
            footer: this.buildReportFooter()
        };

        return report;
    }

    buildReportHeader(data, type) {
        const companyName = appData?.settings?.shopName || 'مؤسسة وقود المستقبل';
        const reportTitle = this.reportTypes[type] || 'تقرير مخصص';
        
        return {
            companyName: companyName,
            reportTitle: reportTitle,
            period: `من ${this.formatDate(data.period.startDate)} إلى ${this.formatDate(data.period.endDate)}`,
            generatedAt: `تم إنشاؤه في: ${this.formatDateTime(data.period.generatedAt)}`,
            logo: 'assets/icons/company-logo.svg'
        };
    }

    buildSummarySection(summary) {
        return {
            title: 'ملخص التقرير',
            cards: [
                {
                    title: 'بطاقات الغاز',
                    icon: 'fas fa-id-card',
                    stats: [
                        { label: 'إجمالي البطاقات', value: summary.gasCards.total },
                        { label: 'البطاقات النشطة', value: summary.gasCards.active },
                        { label: 'البطاقات المنتهية', value: summary.gasCards.expired },
                        { label: 'تنتهي قريباً', value: summary.gasCards.expiringSoon }
                    ]
                },
                {
                    title: 'المواعيد',
                    icon: 'fas fa-calendar-alt',
                    stats: [
                        { label: 'إجمالي المواعيد', value: summary.appointments.total },
                        { label: 'مكتملة', value: summary.appointments.completed },
                        { label: 'معلقة', value: summary.appointments.pending },
                        { label: 'ملغاة', value: summary.appointments.cancelled }
                    ]
                },
                {
                    title: 'المبيعات',
                    icon: 'fas fa-shopping-cart',
                    stats: [
                        { label: 'عدد الفواتير', value: summary.sales.total },
                        { label: 'إجمالي المبيعات', value: this.formatCurrency(summary.sales.totalAmount) },
                        { label: 'متوسط الفاتورة', value: this.formatCurrency(summary.sales.averageAmount) }
                    ]
                },
                {
                    title: 'المشتريات',
                    icon: 'fas fa-shopping-bag',
                    stats: [
                        { label: 'عدد الفواتير', value: summary.purchases.total },
                        { label: 'إجمالي المشتريات', value: this.formatCurrency(summary.purchases.totalAmount) },
                        { label: 'متوسط الفاتورة', value: this.formatCurrency(summary.purchases.averageAmount) }
                    ]
                },
                {
                    title: 'الديون',
                    icon: 'fas fa-money-bill-wave',
                    stats: [
                        { label: 'إجمالي الديون', value: summary.debts.total },
                        { label: 'الديون النشطة', value: summary.debts.active },
                        { label: 'الديون المتأخرة', value: summary.debts.overdue },
                        { label: 'إجمالي المبلغ', value: this.formatCurrency(summary.debts.totalAmount) }
                    ]
                },
                {
                    title: 'المخزون',
                    icon: 'fas fa-boxes',
                    stats: [
                        { label: 'إجمالي الأصناف', value: summary.inventory.totalItems },
                        { label: 'مخزون منخفض', value: summary.inventory.lowStock },
                        { label: 'نفد المخزون', value: summary.inventory.outOfStock },
                        { label: 'قيمة المخزون', value: this.formatCurrency(summary.inventory.totalValue) }
                    ]
                }
            ]
        };
    }

    async buildChartsSection(data) {
        // This would generate chart data for the report
        return {
            title: 'الرسوم البيانية',
            charts: [
                {
                    type: 'pie',
                    title: 'توزيع حالات بطاقات الغاز',
                    data: {
                        labels: ['نشطة', 'منتهية', 'تنتهي قريباً'],
                        values: [
                            data.summary.gasCards.active,
                            data.summary.gasCards.expired,
                            data.summary.gasCards.expiringSoon
                        ]
                    }
                },
                {
                    type: 'bar',
                    title: 'المبيعات مقابل المشتريات',
                    data: {
                        labels: ['المبيعات', 'المشتريات'],
                        values: [
                            data.summary.sales.totalAmount,
                            data.summary.purchases.totalAmount
                        ]
                    }
                }
            ]
        };
    }

    buildDetailsSection(details, sections) {
        const detailSections = {};

        if (sections.includes('all') || sections.includes('gasCards')) {
            detailSections.gasCards = {
                title: 'تفاصيل بطاقات الغاز',
                headers: ['اسم الزبون', 'رقم السيارة', 'رقم البطاقة', 'تاريخ الإصدار', 'تاريخ الانتهاء', 'الحالة'],
                data: details.gasCards.map(card => [
                    card.customerName,
                    card.vehicleNumber,
                    card.cardNumber,
                    this.formatDate(card.issueDate),
                    this.formatDate(card.expiryDate),
                    this.getStatusText(card.status)
                ])
            };
        }

        if (sections.includes('all') || sections.includes('sales')) {
            detailSections.sales = {
                title: 'تفاصيل المبيعات',
                headers: ['رقم الفاتورة', 'التاريخ', 'اسم الزبون', 'المبلغ', 'طريقة الدفع'],
                data: details.sales.map(sale => [
                    sale.invoiceNumber,
                    this.formatDate(sale.date),
                    sale.customerName,
                    this.formatCurrency(sale.totalAmount),
                    sale.paymentMethod
                ])
            };
        }

        return detailSections;
    }

    buildReportFooter() {
        return {
            generatedBy: 'نظام إدارة مؤسسة وقود المستقبل',
            version: '2.2.0',
            website: 'https://futurefuel.sa',
            disclaimer: 'هذا التقرير تم إنشاؤه تلقائياً بواسطة النظام'
        };
    }

    async exportToPDF(report, type) {
        // Implementation for PDF export using jsPDF
        const { jsPDF } = window.jspdf;
        const doc = new jsPDF('p', 'mm', 'a4');

        // Add Arabic font support
        doc.setFont('Arial', 'normal');
        doc.setFontSize(16);

        // Header
        doc.text(report.header.companyName, 105, 20, { align: 'center' });
        doc.setFontSize(14);
        doc.text(report.header.reportTitle, 105, 30, { align: 'center' });
        doc.setFontSize(10);
        doc.text(report.header.period, 105, 40, { align: 'center' });

        // Summary section
        let yPosition = 60;
        doc.setFontSize(12);
        doc.text('ملخص التقرير', 20, yPosition);
        yPosition += 10;

        // Add summary cards
        report.summary.cards.forEach(card => {
            doc.setFontSize(10);
            doc.text(card.title, 20, yPosition);
            yPosition += 5;
            
            card.stats.forEach(stat => {
                doc.text(`${stat.label}: ${stat.value}`, 25, yPosition);
                yPosition += 4;
            });
            yPosition += 5;
        });

        // Footer
        doc.setFontSize(8);
        doc.text(report.footer.generatedBy, 20, 280);
        doc.text(report.footer.disclaimer, 20, 285);

        // Save the PDF
        const filename = `تقرير-${type}-${new Date().toISOString().split('T')[0]}.pdf`;
        doc.save(filename);
    }

    showReportInModal(report, type) {
        // Create and show report modal
        const modal = this.createReportModal(report, type);
        document.body.appendChild(modal);
    }

    createReportModal(report, type) {
        const modal = document.createElement('div');
        modal.className = 'report-modal';
        modal.innerHTML = `
            <div class="report-modal-content">
                <div class="report-modal-header">
                    <h3>${report.header.reportTitle}</h3>
                    <button class="report-modal-close">&times;</button>
                </div>
                <div class="report-modal-body">
                    ${this.renderReportHTML(report)}
                </div>
                <div class="report-modal-footer">
                    <button class="btn btn-primary" onclick="reportsManager.exportToPDF(${JSON.stringify(report)}, '${type}')">
                        تصدير PDF
                    </button>
                    <button class="btn btn-secondary" onclick="this.closest('.report-modal').remove()">
                        إغلاق
                    </button>
                </div>
            </div>
        `;

        // Add event listeners
        modal.querySelector('.report-modal-close').onclick = () => modal.remove();

        return modal;
    }

    renderReportHTML(report) {
        let html = `
            <div class="report-header">
                <h2>${report.header.companyName}</h2>
                <h3>${report.header.reportTitle}</h3>
                <p>${report.header.period}</p>
            </div>
            
            <div class="report-summary">
                <h4>${report.summary.title}</h4>
                <div class="summary-grid">
        `;

        report.summary.cards.forEach(card => {
            html += `
                <div class="summary-card">
                    <h5><i class="${card.icon}"></i> ${card.title}</h5>
                    <ul>
            `;
            card.stats.forEach(stat => {
                html += `<li>${stat.label}: <strong>${stat.value}</strong></li>`;
            });
            html += `</ul></div>`;
        });

        html += `</div></div>`;

        return html;
    }

    // Utility methods
    formatDate(date) {
        return new Date(date).toLocaleDateString('ar-SA');
    }

    formatDateTime(date) {
        return new Date(date).toLocaleString('ar-SA');
    }

    formatCurrency(amount) {
        return new Intl.NumberFormat('ar-SA', {
            style: 'currency',
            currency: 'SAR'
        }).format(amount || 0);
    }

    getStatusText(status) {
        const statusMap = {
            'active': 'نشطة',
            'expired': 'منتهية',
            'pending': 'معلقة',
            'completed': 'مكتملة',
            'cancelled': 'ملغاة'
        };
        return statusMap[status] || status;
    }

    loadReportTemplates() {
        // Load predefined report templates
        this.templates = {
            daily: {
                sections: ['gasCards', 'appointments', 'sales'],
                charts: ['appointments', 'sales']
            },
            weekly: {
                sections: ['all'],
                charts: ['sales', 'appointments', 'debts']
            },
            monthly: {
                sections: ['all'],
                charts: ['all']
            }
        };
    }
}

// Initialize reports manager
const reportsManager = new ReportsManager();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ReportsManager;
}
