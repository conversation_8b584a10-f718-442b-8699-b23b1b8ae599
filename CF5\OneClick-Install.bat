@echo off
chcp 65001 >nul
title تثبيت مؤسسة وقود المستقبل - Future Fuel Installation

:: تحديد متغيرات النظام
set APP_NAME=مؤسسة وقود المستقبل
set APP_NAME_EN=Future Fuel Corporation
set VERSION=2.2.0
set INSTALL_DIR=%USERPROFILE%\Desktop\Future Fuel
set SHORTCUT_NAME=مؤسسة وقود المستقبل.lnk

echo.
echo ================================================================================
echo                    🚀 مرحباً بكم في برنامج التثبيت
echo                    Welcome to Installation Program
echo ================================================================================
echo.
echo برنامج: %APP_NAME%
echo الإصدار: %VERSION%
echo.
echo سيتم تثبيت البرنامج في: %INSTALL_DIR%
echo.
echo ⚠️  تأكد من إغلاق جميع البرامج الأخرى قبل المتابعة
echo.
pause

echo.
echo ================================================================================
echo                           🔄 بدء عملية التثبيت
echo                           Starting Installation
echo ================================================================================
echo.

:: التحقق من صلاحيات المدير
echo [1/8] فحص صلاحيات النظام...
net session >nul 2>&1
if %errorLevel% == 0 (
    echo      ✅ صلاحيات المدير متوفرة
) else (
    echo      ⚠️  تشغيل بصلاحيات المستخدم العادي
)

:: إنشاء مجلد التثبيت
echo [2/8] إنشاء مجلد التثبيت...
if not exist "%INSTALL_DIR%" (
    mkdir "%INSTALL_DIR%" 2>nul
    if exist "%INSTALL_DIR%" (
        echo      ✅ تم إنشاء مجلد التثبيت بنجاح
    ) else (
        echo      ❌ فشل في إنشاء مجلد التثبيت
        echo      جرب تشغيل البرنامج كمدير
        pause
        exit /b 1
    )
) else (
    echo      ✅ مجلد التثبيت موجود مسبقاً
)

:: نسخ ملفات التطبيق
echo [3/8] نسخ ملفات التطبيق...
xcopy /E /I /Y "customer-package\*" "%INSTALL_DIR%\" >nul 2>&1
if %errorLevel% == 0 (
    echo      ✅ تم نسخ ملفات التطبيق بنجاح
) else (
    echo      ❌ فشل في نسخ ملفات التطبيق
    echo      تأكد من وجود مجلد customer-package
    pause
    exit /b 1
)

:: نسخ الملفات المشتركة
echo [4/8] نسخ الملفات المشتركة...
if exist "shared" (
    xcopy /E /I /Y "shared\*" "%INSTALL_DIR%\shared\" >nul 2>&1
    echo      ✅ تم نسخ الملفات المشتركة
) else (
    echo      ⚠️  مجلد الملفات المشتركة غير موجود
)

:: إنشاء اختصار على سطح المكتب
echo [5/8] إنشاء اختصار على سطح المكتب...
powershell -Command "
$WshShell = New-Object -comObject WScript.Shell;
$Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\%SHORTCUT_NAME%');
$Shortcut.TargetPath = '%INSTALL_DIR%\app\index.html';
$Shortcut.WorkingDirectory = '%INSTALL_DIR%\app';
$Shortcut.IconLocation = '%INSTALL_DIR%\app\assets\future-fuel-icon.png';
$Shortcut.Description = '%APP_NAME% - %VERSION%';
$Shortcut.Save()
" >nul 2>&1

if exist "%USERPROFILE%\Desktop\%SHORTCUT_NAME%" (
    echo      ✅ تم إنشاء اختصار سطح المكتب
) else (
    echo      ⚠️  لم يتم إنشاء اختصار سطح المكتب
)

:: إنشاء اختصار في قائمة ابدأ
echo [6/8] إنشاء اختصار في قائمة ابدأ...
set START_MENU_DIR=%APPDATA%\Microsoft\Windows\Start Menu\Programs\Future Fuel
if not exist "%START_MENU_DIR%" mkdir "%START_MENU_DIR%" >nul 2>&1

powershell -Command "
$WshShell = New-Object -comObject WScript.Shell;
$Shortcut = $WshShell.CreateShortcut('%START_MENU_DIR%\%APP_NAME%.lnk');
$Shortcut.TargetPath = '%INSTALL_DIR%\app\index.html';
$Shortcut.WorkingDirectory = '%INSTALL_DIR%\app';
$Shortcut.IconLocation = '%INSTALL_DIR%\app\assets\future-fuel-icon.png';
$Shortcut.Description = '%APP_NAME% - %VERSION%';
$Shortcut.Save()
" >nul 2>&1

if exist "%START_MENU_DIR%\%APP_NAME%.lnk" (
    echo      ✅ تم إنشاء اختصار قائمة ابدأ
) else (
    echo      ⚠️  لم يتم إنشاء اختصار قائمة ابدأ
)

:: تسجيل البرنامج في النظام
echo [7/8] تسجيل البرنامج في النظام...
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Uninstall\FutureFuel" /v "DisplayName" /t REG_SZ /d "%APP_NAME%" /f >nul 2>&1
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Uninstall\FutureFuel" /v "DisplayVersion" /t REG_SZ /d "%VERSION%" /f >nul 2>&1
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Uninstall\FutureFuel" /v "InstallLocation" /t REG_SZ /d "%INSTALL_DIR%" /f >nul 2>&1
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Uninstall\FutureFuel" /v "UninstallString" /t REG_SZ /d "%INSTALL_DIR%\Uninstall.bat" /f >nul 2>&1
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Uninstall\FutureFuel" /v "Publisher" /t REG_SZ /d "Future Fuel Corporation" /f >nul 2>&1

echo      ✅ تم تسجيل البرنامج في النظام

:: إنشاء ملف إلغاء التثبيت
echo [8/8] إنشاء ملف إلغاء التثبيت...
(
echo @echo off
echo title إلغاء تثبيت %APP_NAME%
echo.
echo هل أنت متأكد من إلغاء تثبيت %APP_NAME%؟
echo.
echo سيتم حذف جميع ملفات البرنامج والاختصارات
echo.
pause
echo.
echo جاري إلغاء التثبيت...
echo.
echo حذف ملفات البرنامج...
rmdir /s /q "%INSTALL_DIR%" 2^>nul
echo.
echo حذف الاختصارات...
del "%USERPROFILE%\Desktop\%SHORTCUT_NAME%" 2^>nul
rmdir /s /q "%START_MENU_DIR%" 2^>nul
echo.
echo إزالة التسجيل من النظام...
reg delete "HKCU\Software\Microsoft\Windows\CurrentVersion\Uninstall\FutureFuel" /f 2^>nul
echo.
echo تم إلغاء التثبيت بنجاح
echo.
pause
) > "%INSTALL_DIR%\Uninstall.bat"

echo      ✅ تم إنشاء ملف إلغاء التثبيت

echo.
echo ================================================================================
echo                           ✅ تم التثبيت بنجاح!
echo                           Installation Completed Successfully!
echo ================================================================================
echo.
echo 🎉 تهانينا! تم تثبيت %APP_NAME% بنجاح
echo.
echo 📁 مجلد التثبيت: %INSTALL_DIR%
echo 🖥️  اختصار سطح المكتب: %SHORTCUT_NAME%
echo 📋 قائمة ابدأ: البرامج ^> Future Fuel
echo.
echo ================================================================================
echo                              🚀 بدء الاستخدام
echo ================================================================================
echo.
echo لبدء استخدام البرنامج:
echo.
echo 1️⃣  انقر نقراً مزدوجاً على اختصار سطح المكتب
echo    أو
echo 2️⃣  اذهب إلى قائمة ابدأ ^> البرامج ^> Future Fuel
echo    أو  
echo 3️⃣  افتح المجلد: %INSTALL_DIR%\app
echo    وانقر نقراً مزدوجاً على index.html
echo.
echo ================================================================================
echo                              📋 معلومات مهمة
echo ================================================================================
echo.
echo 🔑 ستحتاج إلى كود الترخيص لتسجيل الدخول
echo 📧 إذا لم تحصل على كود الترخيص، تواصل معنا:
echo    البريد الإلكتروني: <EMAIL>
echo    الهاتف: +966-11-123-4567
echo.
echo 🔄 لإلغاء التثبيت: شغل ملف Uninstall.bat من مجلد البرنامج
echo.
echo ================================================================================

echo.
set /p choice="هل تريد تشغيل البرنامج الآن؟ (y/n): "
if /i "%choice%"=="y" (
    echo.
    echo 🚀 جاري تشغيل البرنامج...
    start "" "%INSTALL_DIR%\app\index.html"
    echo.
    echo تم تشغيل البرنامج بنجاح!
    echo يمكنك الآن إغلاق هذه النافذة
) else (
    echo.
    echo يمكنك تشغيل البرنامج لاحقاً من اختصار سطح المكتب
)

echo.
echo شكراً لاختياركم مؤسسة وقود المستقبل!
echo.
pause
