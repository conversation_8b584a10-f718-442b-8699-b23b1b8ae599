// ملف إصلاح مشاكل جدول الإرسال
// يجب تشغيل هذا الملف في وحدة التحكم للمتصفح

console.log('🔧 بدء إصلاح مشاكل جدول الإرسال...');

// 1. التحقق من وجود العناصر المطلوبة
function checkRequiredElements() {
    console.log('📋 فحص العناصر المطلوبة...');
    
    const requiredElements = [
        'service-type',
        'service-date', 
        'transmission-table',
        'transmission-table-main',
        'transmission-table-body',
        'add-transmission-entry-btn'
    ];
    
    const missingElements = [];
    
    requiredElements.forEach(elementId => {
        const element = document.getElementById(elementId);
        if (!element) {
            missingElements.push(elementId);
            console.error(`❌ عنصر مفقود: ${elementId}`);
        } else {
            console.log(`✅ عنصر موجود: ${elementId}`);
        }
    });
    
    return missingElements;
}

// 2. إصلاح وظيفة handleServiceTypeChange
function fixHandleServiceTypeChange() {
    console.log('🔧 إصلاح وظيفة handleServiceTypeChange...');
    
    window.handleServiceTypeChange = function() {
        console.log('🔄 تم استدعاء handleServiceTypeChange');
        
        const serviceType = document.getElementById('service-type');
        const serviceDate = document.getElementById('service-date');
        
        if (!serviceType) {
            console.error('❌ عنصر service-type غير موجود');
            return;
        }
        
        const selectedType = serviceType.value;
        console.log('📝 نوع الخدمة المختار:', selectedType);
        
        // تعيين تاريخ الخدمة الافتراضي
        if (serviceDate && !serviceDate.value) {
            serviceDate.value = new Date().toISOString().split('T')[0];
        }
        
        // إظهار رسالة توضيحية
        let message = '';
        if (selectedType === 'تركيب') {
            message = '✅ سيتم إضافة هذه العملية إلى جدول الإرسال';
        } else if (selectedType === 'مراقبة') {
            message = '✅ سيتم إضافة هذه العملية إلى جدول الإرسال';
        } else if (selectedType === 'تجديد بطاقة') {
            message = '🎫 سيتم إضافة/تحديث بطاقة الغاز للعميل';
        }
        
        // البحث عن أو إنشاء عنصر الرسالة
        let serviceInfo = document.getElementById('service-type-info');
        if (!serviceInfo) {
            serviceInfo = document.createElement('div');
            serviceInfo.id = 'service-type-info';
            serviceInfo.style.cssText = 'margin-top: 0.5rem; padding: 0.5rem; background: #e0f2fe; border-radius: 4px; font-size: 0.9rem; color: #0277bd; border: 1px solid #b3e5fc;';
            
            // إضافة العنصر بعد select
            if (serviceType.parentNode) {
                serviceType.parentNode.appendChild(serviceInfo);
            }
        }
        
        if (message) {
            serviceInfo.textContent = message;
            serviceInfo.style.display = 'block';
        } else {
            serviceInfo.style.display = 'none';
        }
    };
    
    // إضافة مستمع الأحداث
    const serviceTypeElement = document.getElementById('service-type');
    if (serviceTypeElement) {
        serviceTypeElement.removeEventListener('change', window.handleServiceTypeChange);
        serviceTypeElement.addEventListener('change', window.handleServiceTypeChange);
        console.log('✅ تم إضافة مستمع أحداث handleServiceTypeChange');
    }
}

// 3. إصلاح عرض قسم جدول الإرسال
function fixTransmissionTableDisplay() {
    console.log('🔧 إصلاح عرض قسم جدول الإرسال...');
    
    const transmissionSection = document.getElementById('transmission-table');
    if (transmissionSection) {
        // إزالة جميع الفئات النشطة من الأقسام الأخرى
        document.querySelectorAll('section.active-section').forEach(section => {
            section.classList.remove('active-section');
        });
        
        // إضافة الفئة النشطة لقسم جدول الإرسال
        transmissionSection.classList.add('active-section');
        transmissionSection.style.display = 'block';
        
        console.log('✅ تم إظهار قسم جدول الإرسال');
        
        // تحديث رابط القائمة
        const navLinks = document.querySelectorAll('nav a');
        navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('data-section') === 'transmission-table') {
                link.classList.add('active');
            }
        });
        
        console.log('✅ تم تحديث رابط القائمة');
    } else {
        console.error('❌ قسم جدول الإرسال غير موجود');
    }
}

// 4. إصلاح بيانات التطبيق
function fixAppData() {
    console.log('🔧 إصلاح بيانات التطبيق...');
    
    if (typeof window.appData === 'undefined') {
        window.appData = {
            customers: [],
            vehicles: [],
            gasTanks: [],
            gasCards: [],
            appointments: [],
            debts: [],
            debtPayments: [],
            suppliers: [],
            inventory: [],
            sales: [],
            purchases: [],
            stockAdjustments: [],
            notifications: [],
            transmissionTable: [],
            settings: {
                shopName: 'مؤسسة وقود المستقبل',
                reminderDays: 30,
                workingHoursStart: '08:00',
                workingHoursEnd: '18:00',
                debtReminderDays: 7,
                telegram: {
                    botToken: '',
                    chatId: '',
                    enabled: false,
                    autoBackup: true,
                    backupFrequency: 'daily'
                },
                company: {
                    name: 'مركز وقود المستقبل - عزيري عبد الله اسحاق',
                    number: '463/2019',
                    directorateName: 'مدير الصناعة و المناجم لولاية المدية',
                    transmissionFrequency: 'both',
                    nextTransmissionDate: '',
                    transmissionReminderDays: 7,
                    lastTransmissionDate: '',
                    transmissionHistory: []
                }
            }
        };
        console.log('✅ تم إنشاء بيانات التطبيق');
    } else {
        // التحقق من وجود جدول الإرسال
        if (!window.appData.transmissionTable) {
            window.appData.transmissionTable = [];
            console.log('✅ تم إضافة جدول الإرسال إلى البيانات');
        }
        
        // التحقق من وجود إعدادات المؤسسة
        if (!window.appData.settings.company) {
            window.appData.settings.company = {
                name: 'مركز وقود المستقبل - عزيري عبد الله اسحاق',
                number: '463/2019',
                directorateName: 'مدير الصناعة و المناجم لولاية المدية',
                transmissionFrequency: 'both',
                nextTransmissionDate: '',
                transmissionReminderDays: 7,
                lastTransmissionDate: '',
                transmissionHistory: []
            };
            console.log('✅ تم إضافة إعدادات المؤسسة');
        }
    }
}

// 5. إصلاح وظائف جدول الإرسال
function fixTransmissionFunctions() {
    console.log('🔧 إصلاح وظائف جدول الإرسال...');
    
    // وظيفة تحديث جدول الإرسال
    if (typeof window.updateTransmissionTable !== 'function') {
        window.updateTransmissionTable = function() {
            console.log('🔄 تحديث جدول الإرسال...');
            
            const tableBody = document.getElementById('transmission-table-body');
            if (!tableBody) {
                console.error('❌ عنصر transmission-table-body غير موجود');
                return;
            }
            
            const data = window.appData.transmissionTable || [];
            
            if (data.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="9" style="text-align: center; padding: 2rem; color: #6b7280;">لا توجد بيانات</td></tr>';
            } else {
                tableBody.innerHTML = data.map((entry, index) => `
                    <tr>
                        <td>${entry.type}</td>
                        <td>${entry.tankNumber}</td>
                        <td>${entry.carType}</td>
                        <td>${entry.serialNumber}</td>
                        <td>${entry.registrationNumber}</td>
                        <td>${entry.ownerName}</td>
                        <td>${index + 1}</td>
                        <td>${new Date(entry.operationDate).toLocaleDateString('ar-SA')}</td>
                        <td>
                            <button onclick="deleteTransmissionEntry('${entry.id}')" style="background: #ef4444; color: white; border: none; padding: 0.25rem 0.5rem; border-radius: 4px; cursor: pointer;">
                                حذف
                            </button>
                        </td>
                    </tr>
                `).join('');
            }
            
            console.log(`✅ تم تحديث جدول الإرسال (${data.length} عنصر)`);
        };
    }
    
    // وظيفة حذف عملية
    if (typeof window.deleteTransmissionEntry !== 'function') {
        window.deleteTransmissionEntry = function(entryId) {
            if (confirm('هل أنت متأكد من حذف هذه العملية؟')) {
                const index = window.appData.transmissionTable.findIndex(e => e.id === entryId);
                if (index !== -1) {
                    window.appData.transmissionTable.splice(index, 1);
                    window.updateTransmissionTable();
                    console.log('✅ تم حذف العملية');
                }
            }
        };
    }
}

// 6. إضافة بيانات تجريبية
function addSampleData() {
    console.log('🔧 إضافة بيانات تجريبية...');
    
    const sampleEntry = {
        id: Date.now().toString(),
        type: 'تركيب',
        tankNumber: '1506623',
        carType: 'Toyota Corolla',
        serialNumber: 'VF32A5FWC12345678',
        registrationNumber: '1506615-26',
        ownerName: 'أحمد محمد علي',
        operationDate: new Date().toISOString().split('T')[0],
        createdAt: new Date().toISOString()
    };
    
    window.appData.transmissionTable.push(sampleEntry);
    
    if (typeof window.updateTransmissionTable === 'function') {
        window.updateTransmissionTable();
    }
    
    console.log('✅ تم إضافة بيانات تجريبية');
}

// 7. تشغيل جميع الإصلاحات
function runAllFixes() {
    console.log('🚀 تشغيل جميع الإصلاحات...');
    
    const missingElements = checkRequiredElements();
    
    if (missingElements.length > 0) {
        console.warn('⚠️ بعض العناصر مفقودة:', missingElements);
    }
    
    fixAppData();
    fixHandleServiceTypeChange();
    fixTransmissionFunctions();
    fixTransmissionTableDisplay();
    
    console.log('🎉 تم الانتهاء من جميع الإصلاحات!');
    
    // اختبار النظام
    setTimeout(() => {
        console.log('🧪 اختبار النظام...');
        addSampleData();
    }, 1000);
}

// تشغيل الإصلاحات تلقائياً
runAllFixes();

// إضافة الوظائف إلى النطاق العام للاستخدام اليدوي
window.fixTransmissionIssues = {
    checkElements: checkRequiredElements,
    fixServiceTypeChange: fixHandleServiceTypeChange,
    fixDisplay: fixTransmissionTableDisplay,
    fixData: fixAppData,
    fixFunctions: fixTransmissionFunctions,
    addSample: addSampleData,
    runAll: runAllFixes
};

console.log('✅ تم تحميل أدوات الإصلاح. يمكنك استخدام fixTransmissionIssues.runAll() لتشغيل جميع الإصلاحات');
