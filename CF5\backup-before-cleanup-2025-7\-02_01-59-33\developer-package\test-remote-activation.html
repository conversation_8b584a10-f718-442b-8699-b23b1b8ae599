<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التفعيل عن بُعد</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
            direction: rtl;
            margin: 0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .panel {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .panel h2 {
            margin-top: 0;
            color: #2196f3;
            border-bottom: 2px solid #2196f3;
            padding-bottom: 10px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            box-sizing: border-box;
        }
        button {
            background: #2196f3;
            color: white;
            padding: 12px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: background 0.3s;
        }
        button:hover {
            background: #1976d2;
        }
        button.success {
            background: #4caf50;
        }
        button.success:hover {
            background: #45a049;
        }
        button.warning {
            background: #ff9800;
        }
        button.warning:hover {
            background: #f57c00;
        }
        .device-id {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 16px;
            border: 2px solid #2196f3;
            margin: 10px 0;
            text-align: center;
            font-weight: bold;
            color: #1976d2;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        .result.success {
            background: #e8f5e8;
            color: #2e7d32;
            border: 1px solid #4caf50;
        }
        .result.error {
            background: #ffebee;
            color: #c62828;
            border: 1px solid #f44336;
        }
        .result.info {
            background: #e3f2fd;
            color: #1976d2;
            border: 1px solid #2196f3;
        }
        .steps {
            background: #fff3cd;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #ffc107;
            margin: 15px 0;
        }
        .steps h4 {
            margin: 0 0 10px 0;
            color: #856404;
        }
        .steps ol {
            margin: 0;
            padding-right: 20px;
        }
        .steps li {
            margin-bottom: 5px;
        }
        .license-code {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 5px;
            border: 2px solid #4caf50;
            margin: 10px 0;
            text-align: center;
            font-family: monospace;
            font-size: 18px;
            font-weight: bold;
            color: #2e7d32;
        }
        .header {
            grid-column: 1 / -1;
            text-align: center;
            background: linear-gradient(135deg, #2196f3, #1976d2);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .header h1 {
            margin: 0;
            font-size: 2rem;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }
        .status-indicator.online {
            background: #4caf50;
        }
        .status-indicator.offline {
            background: #f44336;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1><i class="fas fa-satellite-dish"></i> اختبار نظام التفعيل عن بُعد</h1>
        <p>Future Fuel Corporation - Remote Activation Testing System</p>
    </div>

    <div class="container">
        <!-- جانب العميل -->
        <div class="panel">
            <h2><i class="fas fa-user"></i> جانب العميل <span class="status-indicator online"></span></h2>
            
            <div class="steps">
                <h4><i class="fas fa-info-circle"></i> خطوات العميل:</h4>
                <ol>
                    <li>نسخ معرف الجهاز أدناه</li>
                    <li>إرساله للمطور/المدير</li>
                    <li>انتظار كود الترخيص</li>
                    <li>إدخال الكود في التطبيق</li>
                </ol>
            </div>

            <div class="form-group">
                <label><i class="fas fa-mobile-alt"></i> معرف جهاز العميل:</label>
                <div class="device-id" id="customerDeviceId">جاري التوليد...</div>
                <button onclick="copyDeviceId()" class="success">
                    <i class="fas fa-copy"></i> نسخ معرف الجهاز
                </button>
                <button onclick="generateNewDeviceId()" class="warning">
                    <i class="fas fa-refresh"></i> توليد معرف جديد
                </button>
            </div>

            <div class="form-group">
                <label><i class="fas fa-user-tag"></i> اسم العميل (اختياري):</label>
                <input type="text" id="testCustomerName" placeholder="أحمد محمد" value="عميل تجريبي">
            </div>

            <div class="form-group">
                <label><i class="fas fa-phone"></i> رقم الهاتف (اختياري):</label>
                <input type="text" id="testCustomerPhone" placeholder="+966501234567" value="+966501234567">
            </div>

            <div id="customerResult" class="result"></div>
        </div>

        <!-- جانب المطور -->
        <div class="panel">
            <h2><i class="fas fa-code"></i> جانب المطور/المدير <span class="status-indicator online"></span></h2>
            
            <div class="steps">
                <h4><i class="fas fa-cogs"></i> خطوات المطور:</h4>
                <ol>
                    <li>استلام معرف الجهاز من العميل</li>
                    <li>إدخال المعرف في النموذج أدناه</li>
                    <li>اختيار نوع الترخيص</li>
                    <li>النقر على "تفعيل مباشرة"</li>
                    <li>إرسال كود الترخيص للعميل</li>
                </ol>
            </div>

            <form id="remoteActivationForm">
                <div class="form-group">
                    <label><i class="fas fa-mobile-alt"></i> معرف جهاز العميل:</label>
                    <input type="text" id="developerDeviceId" placeholder="DEV-XXXXX-XXXXX-XXXXX" required>
                    <button type="button" onclick="useTestDeviceId()" class="warning">
                        <i class="fas fa-arrow-left"></i> استخدام معرف الاختبار
                    </button>
                </div>

                <div class="form-group">
                    <label><i class="fas fa-certificate"></i> نوع الترخيص:</label>
                    <select id="developerLicenseType" onchange="updateLicenseDuration()">
                        <option value="demo">تجريبي (30 يوم)</option>
                        <option value="monthly">شهري (30 يوم)</option>
                        <option value="yearly" selected>سنوي (365 يوم)</option>
                        <option value="lifetime">مدى الحياة (3650 يوم)</option>
                        <option value="admin">إداري (365 يوم)</option>
                    </select>
                </div>

                <div class="form-group">
                    <label><i class="fas fa-calendar"></i> مدة الترخيص (بالأيام):</label>
                    <input type="number" id="developerLicenseDuration" value="365" min="1" max="3650" required>
                </div>

                <div class="form-group">
                    <label><i class="fas fa-user"></i> اسم العميل:</label>
                    <input type="text" id="developerCustomerName" placeholder="اسم العميل">
                </div>

                <div class="form-group">
                    <label><i class="fas fa-sticky-note"></i> ملاحظات:</label>
                    <textarea id="developerNotes" rows="3" placeholder="ملاحظات إضافية..."></textarea>
                </div>

                <button type="submit" class="success">
                    <i class="fas fa-rocket"></i> تفعيل العميل مباشرة
                </button>
            </form>

            <div id="developerResult" class="result"></div>
        </div>

        <!-- نتائج التفعيل -->
        <div class="panel" style="grid-column: 1 / -1;">
            <h2><i class="fas fa-chart-line"></i> نتائج التفعيل</h2>
            <div id="activationResults"></div>
            
            <div style="margin-top: 20px;">
                <button onclick="openAdminPanel()" class="success">
                    <i class="fas fa-external-link-alt"></i> فتح لوحة التحكم الكاملة
                </button>
                <button onclick="testGeneratedLicense()" class="warning">
                    <i class="fas fa-vial"></i> اختبار الترخيص المولد
                </button>
                <button onclick="clearAllData()">
                    <i class="fas fa-trash"></i> مسح جميع البيانات
                </button>
            </div>
        </div>
    </div>

    <script>
        let generatedLicenseCode = null;

        // توليد معرف جهاز فريد
        function generateDeviceId() {
            const timestamp = Date.now().toString(36);
            const random = Math.random().toString(36).substr(2, 9);
            return `DEV-${timestamp}-${random}`.toUpperCase();
        }

        // تحديث معرف الجهاز
        function updateCustomerDeviceId() {
            const deviceId = generateDeviceId();
            document.getElementById('customerDeviceId').textContent = deviceId;
            return deviceId;
        }

        // توليد معرف جديد
        function generateNewDeviceId() {
            updateCustomerDeviceId();
            showResult('customerResult', 'تم توليد معرف جهاز جديد', 'info');
        }

        // نسخ معرف الجهاز
        function copyDeviceId() {
            const deviceId = document.getElementById('customerDeviceId').textContent;
            navigator.clipboard.writeText(deviceId).then(() => {
                showResult('customerResult', 'تم نسخ معرف الجهاز بنجاح!', 'success');
            }).catch(() => {
                showResult('customerResult', 'فشل في نسخ معرف الجهاز', 'error');
            });
        }

        // استخدام معرف الاختبار
        function useTestDeviceId() {
            const deviceId = document.getElementById('customerDeviceId').textContent;
            document.getElementById('developerDeviceId').value = deviceId;
            document.getElementById('developerCustomerName').value = document.getElementById('testCustomerName').value;
            showResult('developerResult', 'تم نسخ معرف الجهاز من جانب العميل', 'info');
        }

        // تحديث مدة الترخيص
        function updateLicenseDuration() {
            const licenseType = document.getElementById('developerLicenseType').value;
            const durationInput = document.getElementById('developerLicenseDuration');
            
            switch(licenseType) {
                case 'demo':
                    durationInput.value = 30;
                    break;
                case 'monthly':
                    durationInput.value = 30;
                    break;
                case 'yearly':
                    durationInput.value = 365;
                    break;
                case 'lifetime':
                    durationInput.value = 3650;
                    break;
                case 'admin':
                    durationInput.value = 365;
                    break;
            }
        }

        // عرض النتيجة
        function showResult(elementId, message, type) {
            const resultDiv = document.getElementById(elementId);
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = message;
            resultDiv.style.display = 'block';
        }

        // توليد كود ترخيص
        function generateLicenseCode(type) {
            const prefix = {
                'demo': 'DEMO',
                'monthly': 'MNTH',
                'yearly': 'YEAR',
                'lifetime': 'LIFE',
                'admin': 'ADMN'
            }[type] || 'CUST';
            
            const year = new Date().getFullYear();
            const timestamp = Date.now().toString(36).toUpperCase();
            const random = Math.random().toString(36).substr(2, 4).toUpperCase();
            
            return `${prefix}-${year}-${timestamp}-${random}`;
        }

        // تفعيل العميل مباشرة
        function activateCustomerDirectly() {
            const deviceId = document.getElementById('developerDeviceId').value.trim();
            const licenseType = document.getElementById('developerLicenseType').value;
            const duration = parseInt(document.getElementById('developerLicenseDuration').value);
            const customerName = document.getElementById('developerCustomerName').value.trim();
            const notes = document.getElementById('developerNotes').value.trim();
            
            // التحقق من البيانات
            if (!deviceId) {
                showResult('developerResult', '❌ يرجى إدخال معرف جهاز العميل', 'error');
                return;
            }
            
            if (!deviceId.startsWith('DEV-')) {
                showResult('developerResult', '❌ معرف الجهاز يجب أن يبدأ بـ DEV-', 'error');
                return;
            }
            
            // توليد كود الترخيص
            generatedLicenseCode = generateLicenseCode(licenseType);
            
            // إنشاء الترخيص
            const newLicense = {
                id: 'LIC-' + Date.now(),
                code: generatedLicenseCode,
                type: licenseType,
                status: 'active',
                expiresAt: new Date(Date.now() + duration * 24 * 60 * 60 * 1000).toISOString(),
                isActive: true,
                deviceId: deviceId,
                activatedAt: new Date().toISOString(),
                createdAt: new Date().toISOString(),
                notes: notes || `تم التفعيل عن بُعد${customerName ? ' للعميل: ' + customerName : ''}`,
                customerName: customerName,
                activationMethod: 'remote-test'
            };
            
            // حفظ في localStorage
            const licenses = JSON.parse(localStorage.getItem('validLicenses') || '[]');
            licenses.push(newLicense);
            localStorage.setItem('validLicenses', JSON.stringify(licenses));
            
            // عرض النتيجة
            const licenseTypeName = {
                'demo': 'تجريبي',
                'monthly': 'شهري',
                'yearly': 'سنوي',
                'lifetime': 'مدى الحياة',
                'admin': 'إداري'
            }[licenseType];
            
            const expiryDate = new Date(newLicense.expiresAt).toLocaleDateString('ar-SA');
            
            const resultHtml = `
                <h4>✅ تم تفعيل العميل بنجاح!</h4>
                <div class="license-code">${generatedLicenseCode}</div>
                <p><strong>نوع الترخيص:</strong> ${licenseTypeName}</p>
                <p><strong>معرف الجهاز:</strong> ${deviceId}</p>
                <p><strong>تاريخ الانتهاء:</strong> ${expiryDate}</p>
                ${customerName ? `<p><strong>اسم العميل:</strong> ${customerName}</p>` : ''}
                <button onclick="copyLicenseCode('${generatedLicenseCode}')" class="success">
                    <i class="fas fa-copy"></i> نسخ كود الترخيص
                </button>
            `;
            
            showResult('developerResult', resultHtml, 'success');
            
            // عرض في قسم النتائج
            document.getElementById('activationResults').innerHTML = `
                <div class="result success">
                    <h3>🎉 تم إنشاء ترخيص جديد بنجاح!</h3>
                    <p><strong>كود الترخيص:</strong> <span class="license-code">${generatedLicenseCode}</span></p>
                    <p><strong>الآن يمكن للعميل استخدام هذا الكود في التطبيق</strong></p>
                </div>
            `;
        }

        // نسخ كود الترخيص
        function copyLicenseCode(licenseCode) {
            navigator.clipboard.writeText(licenseCode).then(() => {
                alert('تم نسخ كود الترخيص: ' + licenseCode);
            });
        }

        // اختبار الترخيص المولد
        function testGeneratedLicense() {
            if (!generatedLicenseCode) {
                alert('لا يوجد ترخيص مولد للاختبار. قم بتفعيل عميل أولاً.');
                return;
            }
            
            const testUrl = `../customer-package/app/index.html?license=${generatedLicenseCode}`;
            window.open(testUrl, '_blank');
        }

        // فتح لوحة التحكم
        function openAdminPanel() {
            window.open('admin-control-panel.html', '_blank');
        }

        // مسح البيانات
        function clearAllData() {
            if (confirm('هل تريد مسح جميع البيانات؟')) {
                localStorage.clear();
                location.reload();
            }
        }

        // معالجة النموذج
        document.getElementById('remoteActivationForm').addEventListener('submit', function(e) {
            e.preventDefault();
            activateCustomerDirectly();
        });

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateCustomerDeviceId();
            console.log('🧪 صفحة اختبار التفعيل عن بُعد جاهزة');
        });
    </script>
</body>
</html>
