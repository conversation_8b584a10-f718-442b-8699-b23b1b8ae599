/**
 * نظام مزامنة قاعدة البيانات المشتركة
 * Future Fuel Corporation - Database Synchronization System
 * 
 * يربط بين نظام تسجيل الدخول ولوحة التحكم الإدارية
 */

class DatabaseSync {
    constructor() {
        this.storageKeys = {
            licenses: 'validLicenses',
            sessions: 'activeSessions',
            devices: 'registeredDevices',
            statistics: 'systemStatistics',
            users: 'systemUsers',
            logs: 'systemLogs'
        };
        
        this.init();
    }
    
    // تهيئة النظام
    init() {
        this.setupEventListeners();
        this.initializeDefaultData();
        this.startPeriodicSync();
        console.log('🔄 نظام مزامنة قاعدة البيانات تم تهيئته');
    }
    
    // إعداد مستمعي الأحداث
    setupEventListeners() {
        // مراقبة تغييرات localStorage
        window.addEventListener('storage', (e) => {
            if (Object.values(this.storageKeys).includes(e.key)) {
                this.handleStorageChange(e);
            }
        });
        
        // مراقبة رسائل النوافذ المتعددة
        window.addEventListener('message', (e) => {
            if (e.data.type === 'DATABASE_SYNC') {
                this.handleSyncMessage(e.data);
            }
        });
    }
    
    // تهيئة البيانات الافتراضية
    initializeDefaultData() {
        // التحقق من وجود البيانات الأساسية
        if (!this.getLicenses().length) {
            this.createDefaultLicenses();
        }
        
        if (!this.getDevices().length) {
            this.initializeDevicesTable();
        }
        
        this.updateStatistics();
    }
    
    // إنشاء تراخيص افتراضية
    createDefaultLicenses() {
        const defaultLicenses = [
            {
                id: 'LIC-001',
                code: 'DEMO-2024-TEST-0001',
                type: 'demo',
                status: 'active',
                expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
                isActive: true,
                deviceId: null,
                activatedAt: null,
                createdAt: new Date().toISOString(),
                notes: 'ترخيص تجريبي افتراضي - 30 يوم',
                maxDevices: 1,
                features: ['basic', 'reports', 'backup'],
                createdBy: 'system',
                lastModified: new Date().toISOString()
            },
            {
                id: 'LIC-002',
                code: 'FULL-2024-PROD-0001',
                type: 'yearly',
                status: 'active',
                expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
                isActive: true,
                deviceId: null,
                activatedAt: null,
                createdAt: new Date().toISOString(),
                notes: 'ترخيص سنوي كامل - جميع الميزات',
                maxDevices: 1,
                features: ['all'],
                createdBy: 'system',
                lastModified: new Date().toISOString()
            },
            {
                id: 'LIC-003',
                code: 'ADMIN-2024-CTRL-0001',
                type: 'admin',
                status: 'active',
                expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
                isActive: true,
                deviceId: null,
                activatedAt: null,
                createdAt: new Date().toISOString(),
                notes: 'ترخيص إداري للمطورين - صلاحيات كاملة',
                maxDevices: 5,
                features: ['all', 'admin', 'developer', 'control-panel'],
                createdBy: 'system',
                lastModified: new Date().toISOString()
            }
        ];
        
        this.setLicenses(defaultLicenses);
        this.logActivity('system', 'تم إنشاء التراخيص الافتراضية', 'info');
    }
    
    // تهيئة جدول الأجهزة
    initializeDevicesTable() {
        const devices = [];
        this.setDevices(devices);
    }
    
    // الحصول على التراخيص
    getLicenses() {
        const stored = localStorage.getItem(this.storageKeys.licenses);
        return stored ? JSON.parse(stored) : [];
    }
    
    // حفظ التراخيص
    setLicenses(licenses) {
        localStorage.setItem(this.storageKeys.licenses, JSON.stringify(licenses));
        this.broadcastSync('licenses', licenses);
    }
    
    // الحصول على الجلسات النشطة
    getSessions() {
        const stored = localStorage.getItem(this.storageKeys.sessions);
        return stored ? JSON.parse(stored) : [];
    }
    
    // حفظ الجلسات النشطة
    setSessions(sessions) {
        localStorage.setItem(this.storageKeys.sessions, JSON.stringify(sessions));
        this.broadcastSync('sessions', sessions);
    }
    
    // الحصول على الأجهزة المسجلة
    getDevices() {
        const stored = localStorage.getItem(this.storageKeys.devices);
        return stored ? JSON.parse(stored) : [];
    }
    
    // حفظ الأجهزة المسجلة
    setDevices(devices) {
        localStorage.setItem(this.storageKeys.devices, JSON.stringify(devices));
        this.broadcastSync('devices', devices);
    }
    
    // تسجيل جلسة جديدة
    registerSession(sessionData, licenseInfo) {
        try {
            // تحديث بيانات الترخيص
            const licenses = this.getLicenses();
            const licenseIndex = licenses.findIndex(l => l.code === sessionData.licenseCode);
            
            if (licenseIndex !== -1) {
                licenses[licenseIndex].deviceId = sessionData.deviceId;
                licenses[licenseIndex].activatedAt = sessionData.loginTime;
                licenses[licenseIndex].lastActivity = sessionData.loginTime;
                licenses[licenseIndex].sessionId = sessionData.sessionId;
                licenses[licenseIndex].userAgent = sessionData.userAgent;
                licenses[licenseIndex].lastModified = new Date().toISOString();
                
                this.setLicenses(licenses);
            }
            
            // تسجيل الجلسة النشطة
            const sessions = this.getSessions();
            const newSession = {
                id: 'SES-' + Date.now(),
                sessionId: sessionData.sessionId,
                licenseCode: sessionData.licenseCode,
                deviceId: sessionData.deviceId,
                startTime: sessionData.loginTime,
                lastActivity: sessionData.loginTime,
                isActive: true,
                userAgent: sessionData.userAgent,
                ipAddress: sessionData.ipAddress || 'local',
                location: 'Unknown',
                duration: 0
            };
            
            sessions.push(newSession);
            this.setSessions(sessions);
            
            // تسجيل الجهاز إذا لم يكن موجوداً
            this.registerDevice(sessionData.deviceId, sessionData.licenseCode, sessionData.userAgent);
            
            // تحديث الإحصائيات
            this.updateStatistics();
            
            // تسجيل النشاط
            this.logActivity(sessionData.deviceId, `تسجيل دخول جديد - ${sessionData.licenseCode}`, 'login');
            
            console.log('✅ تم تسجيل الجلسة في قاعدة البيانات:', sessionData.sessionId);
            return true;
            
        } catch (error) {
            console.error('❌ خطأ في تسجيل الجلسة:', error);
            this.logActivity('system', `خطأ في تسجيل الجلسة: ${error.message}`, 'error');
            return false;
        }
    }
    
    // تسجيل جهاز جديد
    registerDevice(deviceId, licenseCode, userAgent) {
        const devices = this.getDevices();
        const existingDevice = devices.find(d => d.deviceId === deviceId);
        
        if (!existingDevice) {
            const newDevice = {
                id: 'DEV-' + Date.now(),
                deviceId: deviceId,
                licenseCode: licenseCode,
                userAgent: userAgent,
                firstSeen: new Date().toISOString(),
                lastSeen: new Date().toISOString(),
                status: 'active',
                loginCount: 1,
                location: 'Unknown',
                notes: 'تم التسجيل تلقائياً عند تسجيل الدخول'
            };
            
            devices.push(newDevice);
            this.setDevices(devices);
            
            this.logActivity(deviceId, 'تسجيل جهاز جديد', 'device_registration');
        } else {
            // تحديث آخر ظهور
            existingDevice.lastSeen = new Date().toISOString();
            existingDevice.loginCount = (existingDevice.loginCount || 0) + 1;
            this.setDevices(devices);
        }
    }
    
    // تحديث الإحصائيات
    updateStatistics() {
        const licenses = this.getLicenses();
        const sessions = this.getSessions();
        const devices = this.getDevices();
        
        const stats = {
            totalLicenses: licenses.length,
            activeLicenses: licenses.filter(l => l.deviceId && l.isActive).length,
            expiredLicenses: licenses.filter(l => new Date(l.expiresAt) < new Date()).length,
            totalDevices: devices.length,
            activeDevices: devices.filter(d => d.status === 'active').length,
            activeSessions: sessions.filter(s => s.isActive).length,
            totalSessions: sessions.length,
            lastUpdate: new Date().toISOString(),
            licenseTypes: {
                demo: licenses.filter(l => l.type === 'demo').length,
                monthly: licenses.filter(l => l.type === 'monthly').length,
                yearly: licenses.filter(l => l.type === 'yearly').length,
                lifetime: licenses.filter(l => l.type === 'lifetime').length,
                admin: licenses.filter(l => l.type === 'admin').length
            }
        };
        
        localStorage.setItem(this.storageKeys.statistics, JSON.stringify(stats));
        this.broadcastSync('statistics', stats);
        
        return stats;
    }
    
    // تسجيل النشاط
    logActivity(userId, action, type = 'info') {
        const logs = JSON.parse(localStorage.getItem(this.storageKeys.logs) || '[]');
        
        const logEntry = {
            id: 'LOG-' + Date.now(),
            timestamp: new Date().toISOString(),
            userId: userId,
            action: action,
            type: type,
            userAgent: navigator.userAgent,
            url: window.location.href
        };
        
        logs.push(logEntry);
        
        // الاحتفاظ بآخر 1000 سجل فقط
        if (logs.length > 1000) {
            logs.splice(0, logs.length - 1000);
        }
        
        localStorage.setItem(this.storageKeys.logs, JSON.stringify(logs));
    }
    
    // بث التحديثات للنوافذ الأخرى
    broadcastSync(dataType, data) {
        const message = {
            type: 'DATABASE_SYNC',
            dataType: dataType,
            data: data,
            timestamp: new Date().toISOString()
        };
        
        // إرسال للنوافذ الأخرى
        if (window.opener) {
            window.opener.postMessage(message, '*');
        }
        
        // إرسال للنوافذ الفرعية
        if (window.frames) {
            for (let i = 0; i < window.frames.length; i++) {
                try {
                    window.frames[i].postMessage(message, '*');
                } catch (e) {
                    // تجاهل الأخطاء
                }
            }
        }
    }
    
    // معالجة تغييرات التخزين
    handleStorageChange(event) {
        console.log('🔄 تغيير في قاعدة البيانات:', event.key);
        
        // تحديث الإحصائيات عند أي تغيير
        if (event.key === this.storageKeys.licenses || 
            event.key === this.storageKeys.sessions || 
            event.key === this.storageKeys.devices) {
            this.updateStatistics();
        }
    }
    
    // معالجة رسائل المزامنة
    handleSyncMessage(data) {
        console.log('📨 رسالة مزامنة مستلمة:', data.dataType);
        
        // تحديث البيانات المحلية
        if (data.dataType && data.data) {
            const storageKey = this.storageKeys[data.dataType];
            if (storageKey) {
                localStorage.setItem(storageKey, JSON.stringify(data.data));
            }
        }
    }
    
    // مزامنة دورية
    startPeriodicSync() {
        setInterval(() => {
            this.updateStatistics();
            this.cleanupExpiredSessions();
        }, 30000); // كل 30 ثانية
    }
    
    // تنظيف الجلسات المنتهية
    cleanupExpiredSessions() {
        const sessions = this.getSessions();
        const now = new Date();
        const maxSessionDuration = 24 * 60 * 60 * 1000; // 24 ساعة
        
        const activeSessions = sessions.filter(session => {
            const sessionAge = now - new Date(session.lastActivity);
            return sessionAge < maxSessionDuration;
        });
        
        if (activeSessions.length !== sessions.length) {
            this.setSessions(activeSessions);
            console.log('🧹 تم تنظيف الجلسات المنتهية');
        }
    }
    
    // الحصول على إحصائيات مفصلة
    getDetailedStatistics() {
        const stats = this.updateStatistics();
        const logs = JSON.parse(localStorage.getItem(this.storageKeys.logs) || '[]');
        
        return {
            ...stats,
            recentActivity: logs.slice(-10).reverse(),
            systemHealth: this.checkSystemHealth()
        };
    }
    
    // فحص صحة النظام
    checkSystemHealth() {
        const licenses = this.getLicenses();
        const sessions = this.getSessions();
        const devices = this.getDevices();
        
        return {
            status: 'healthy',
            issues: [],
            recommendations: [],
            lastCheck: new Date().toISOString()
        };
    }
}

// إنشاء مثيل عام للنظام
window.DatabaseSync = new DatabaseSync();

// إتاحة الوظائف للاستخدام العام
window.DB = {
    sync: window.DatabaseSync,
    getLicenses: () => window.DatabaseSync.getLicenses(),
    getSessions: () => window.DatabaseSync.getSessions(),
    getDevices: () => window.DatabaseSync.getDevices(),
    getStatistics: () => window.DatabaseSync.getDetailedStatistics(),
    registerSession: (sessionData, licenseInfo) => window.DatabaseSync.registerSession(sessionData, licenseInfo),
    logActivity: (userId, action, type) => window.DatabaseSync.logActivity(userId, action, type)
};

console.log('🚀 نظام قاعدة البيانات المشتركة جاهز للاستخدام');
