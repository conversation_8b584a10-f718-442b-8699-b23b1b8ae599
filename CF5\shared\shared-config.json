{"version": "1.0.0", "description": "Configuration for shared files across packages", "lastUpdated": "2025-07-08", "sharedFiles": {"scripts": {"updater.js": {"description": "System update manager", "usedBy": ["customer-package", "installer-package"], "dependencies": ["notifications.js"]}, "notifications.js": {"description": "Notification system", "usedBy": ["customer-package", "installer-package"], "dependencies": []}, "reports.js": {"description": "Report generation system", "usedBy": ["customer-package", "installer-package"], "dependencies": []}, "security.js": {"description": "Security and authentication", "usedBy": ["customer-package", "installer-package"], "dependencies": []}}, "templates": {"admin-control-panel.html": {"description": "Administrative control panel", "usedBy": ["root", "developer-package"], "dependencies": []}}, "assets": {"future-fuel-icon.png": {"description": "Application icon", "usedBy": ["customer-package", "installer-package"], "dependencies": []}}, "config": {"package.json": {"description": "Node.js package configuration", "usedBy": ["customer-package", "installer-package"], "dependencies": []}}}, "pathMappings": {"customer-package": {"scripts": "../shared/scripts/", "templates": "../shared/templates/", "assets": "../shared/assets/", "config": "../shared/"}, "installer-package": {"scripts": "../../shared/scripts/", "templates": "../../shared/templates/", "assets": "../../shared/assets/", "config": "../../shared/"}, "developer-package": {"scripts": "../shared/scripts/", "templates": "../shared/templates/", "assets": "../shared/assets/", "config": "../shared/"}}, "buildInstructions": {"beforeBuild": ["Verify all shared files exist", "Check path mappings are correct", "Validate dependencies"], "duringBuild": ["Copy shared files to appropriate locations", "Update references in HTML/JS files", "Verify no broken links"], "afterBuild": ["Test all functionality", "Verify shared files are accessible", "Run integration tests"]}, "maintenance": {"duplicateCheck": {"enabled": true, "schedule": "weekly", "action": "report"}, "integrityCheck": {"enabled": true, "schedule": "daily", "action": "auto-fix"}}}